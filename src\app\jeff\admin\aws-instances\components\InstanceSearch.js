import React from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  TextField,
  InputAdornment,
  Button,
  CircularProgress
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import RefreshIcon from '@mui/icons-material/Refresh';

const InstanceSearch = ({ 
  searchTerm, 
  setSearchTerm, 
  onRefresh, 
  isLoading,
  isDarkMode 
}) => {
  return (
    <Box>
      <Typography 
        variant="h4" 
        mb={2} 
        color={isDarkMode ? '#90CAF9' : '#1976d2'}
        sx={{ 
          fontWeight: 600,
          letterSpacing: '0.25px',
          textShadow: isDarkMode ? '0px 1px 2px rgba(0,0,0,0.3)' : 'none',
        }}
      >
        AWS Instances
      </Typography>
      <Card 
        elevation={isDarkMode ? 2 : 1}
        sx={{ 
          borderColor: 'divider',
          backgroundColor: isDarkMode ? '#2D3748' : '#ffffff',
          boxShadow: isDarkMode 
            ? '0px 3px 3px -2px rgba(0,0,0,0.2),0px 3px 4px 0px rgba(0,0,0,0.14),0px 1px 8px 0px rgba(0,0,0,0.12)'
            : '0px 2px 1px -1px rgba(0,0,0,0.1),0px 1px 1px 0px rgba(0,0,0,0.07),0px 1px 3px 0px rgba(0,0,0,0.06)'
        }}
      >
        <CardContent sx={{ color: isDarkMode ? '#E2E8F0' : 'inherit' }}>
          <Box>
            <Typography 
              variant="body1" 
              gutterBottom 
              color={isDarkMode ? '#e2e8f0' : '#333333'}
            >
              Manage your AWS EC2 instances. Start, stop, or reboot instances as needed.
            </Typography>
            <Button
              variant="contained"
              startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : <RefreshIcon />}
              onClick={onRefresh}
              disabled={isLoading}
              sx={{
                mt: 2, 
                mb: 2,
                boxShadow: isDarkMode ? '0px 2px 4px rgba(0,0,0,0.3)' : '0px 1px 2px rgba(0,0,0,0.1)',
                backgroundColor: isDarkMode ? '#3182CE' : '#2f6ad9',
                color: '#ffffff',
                '&:hover': {
                  boxShadow: isDarkMode ? '0px 3px 6px rgba(0,0,0,0.4)' : '0px 2px 4px rgba(0,0,0,0.2)',
                  backgroundColor: isDarkMode ? '#4299E1' : '#3b7be8'
                },
                transition: 'all 0.2s ease-in-out',
                fontWeight: 500,
                textTransform: 'none',
                borderRadius: '4px'
              }}
              fullWidth
            >
              {isLoading ? 'Refreshing...' : 'Refresh Instances'}
            </Button>
            <TextField
              fullWidth
              id="search-instances"
              placeholder="Search by ID, type, or name"
              variant="outlined"
              size="small"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon 
                      sx={{ 
                        color: isDarkMode ? '#90CAF9' : '#1976d2',
                      }} 
                    />
                  </InputAdornment>
                ),
              }}
              sx={{
                mb: 2,
                '& .MuiOutlinedInput-root': {
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  '& fieldset': {
                    borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.23)' : 'rgba(0, 0, 0, 0.23)',
                  },
                  '&:hover fieldset': {
                    borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.4)' : 'rgba(0, 0, 0, 0.4)',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                },
                '& .MuiInputLabel-root': {
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)',
                },
                '& .MuiInputAdornment-root': {
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.54)',
                },
                '& .MuiInputBase-input::placeholder': {
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.4)',
                  opacity: 1,
                },
              }}
            />
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default InstanceSearch; 