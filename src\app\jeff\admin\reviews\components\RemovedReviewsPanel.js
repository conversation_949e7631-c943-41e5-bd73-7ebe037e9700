import React from 'react';
import {
  <PERSON>,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Card,
  CardContent,
  Button,
  IconButton,
  Stack,
} from '@mui/material';
import {
  ChevronLeft,
  ChevronRight,
  MoreHoriz,
} from '@mui/icons-material';

const RemovedReviewsPanel = ({
  removedReviews,
  paginationMeta,
  isDarkMode,
  fetchRemovedReviews,
}) => {
  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= paginationMeta.totalPages) {
      fetchRemovedReviews(newPage, paginationMeta.limit);
    }
  };

  const renderPageNumbers = () => {
    const { page, totalPages } = paginationMeta;
    const buttons = [];
    const maxVisibleButtons = 7;
    const halfVisible = Math.floor(maxVisibleButtons / 2);

    if (totalPages <= maxVisibleButtons) {
      // Show all buttons if total pages are less than or equal to maxVisibleButtons
      for (let i = 1; i <= totalPages; i++) {
        buttons.push(
          <Button
            key={i}
            onClick={() => handlePageChange(i)}
            variant={i === page ? 'contained' : 'text'}
            sx={{
              minWidth: '32px',
              height: '32px',
              padding: '0',
              color: isDarkMode ? 'rgba(255,255,255,0.7)' : 'inherit',
              backgroundColor: i === page ? (isDarkMode ? '#90CAF9' : '#1976d2') : 'transparent',
              '&:hover': {
                backgroundColor: i === page 
                  ? (isDarkMode ? '#64B5F6' : '#1565C0')
                  : (isDarkMode ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.04)'),
              },
            }}
          >
            {i}
          </Button>
        );
      }
    } else {
      // Show first page
      buttons.push(
        <Button
          key={1}
          onClick={() => handlePageChange(1)}
          variant={1 === page ? 'contained' : 'text'}
          sx={{
            minWidth: '32px',
            height: '32px',
            padding: '0',
            color: isDarkMode ? 'rgba(255,255,255,0.7)' : 'inherit',
            backgroundColor: 1 === page ? (isDarkMode ? '#90CAF9' : '#1976d2') : 'transparent',
            '&:hover': {
              backgroundColor: 1 === page 
                ? (isDarkMode ? '#64B5F6' : '#1565C0')
                : (isDarkMode ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.04)'),
            },
          }}
        >
          1
        </Button>
      );

      // Show ellipsis if current page is far from start
      if (page > halfVisible + 1) {
        buttons.push(
          <IconButton
            key="start-ellipsis"
            disabled
            sx={{
              color: isDarkMode ? 'rgba(255,255,255,0.7)' : 'inherit',
              cursor: 'default',
            }}
          >
            <MoreHoriz />
          </IconButton>
        );
      }

      // Show pages around current page
      const startPage = Math.max(2, page - halfVisible);
      const endPage = Math.min(totalPages - 1, page + halfVisible);

      for (let i = startPage; i <= endPage; i++) {
        buttons.push(
          <Button
            key={i}
            onClick={() => handlePageChange(i)}
            variant={i === page ? 'contained' : 'text'}
            sx={{
              minWidth: '32px',
              height: '32px',
              padding: '0',
              color: isDarkMode ? 'rgba(255,255,255,0.7)' : 'inherit',
              backgroundColor: i === page ? (isDarkMode ? '#90CAF9' : '#1976d2') : 'transparent',
              '&:hover': {
                backgroundColor: i === page 
                  ? (isDarkMode ? '#64B5F6' : '#1565C0')
                  : (isDarkMode ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.04)'),
              },
            }}
          >
            {i}
          </Button>
        );
      }

      // Show ellipsis if current page is far from end
      if (page < totalPages - halfVisible) {
        buttons.push(
          <IconButton
            key="end-ellipsis"
            disabled
            sx={{
              color: isDarkMode ? 'rgba(255,255,255,0.7)' : 'inherit',
              cursor: 'default',
            }}
          >
            <MoreHoriz />
          </IconButton>
        );
      }

      // Show last page
      buttons.push(
        <Button
          key={totalPages}
          onClick={() => handlePageChange(totalPages)}
          variant={totalPages === page ? 'contained' : 'text'}
          sx={{
            minWidth: '32px',
            height: '32px',
            padding: '0',
            color: isDarkMode ? 'rgba(255,255,255,0.7)' : 'inherit',
            backgroundColor: totalPages === page ? (isDarkMode ? '#90CAF9' : '#1976d2') : 'transparent',
            '&:hover': {
              backgroundColor: totalPages === page 
                ? (isDarkMode ? '#64B5F6' : '#1565C0')
                : (isDarkMode ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.04)'),
            },
          }}
        >
          {totalPages}
        </Button>
      );
    }

    return buttons;
  };

  return (
    <Card
      sx={{
        mb: 3,
        backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
      }}
    >
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              color: isDarkMode ? '#fff' : 'inherit',
            }}
          >
            Total Removed Reviews: {paginationMeta.total}
          </Typography>
          <Stack direction="row" spacing={1} alignItems="center">
            <Typography
              variant="body2"
              sx={{
                color: isDarkMode ? 'rgba(255,255,255,0.7)' : 'inherit',
              }}
            >
              Page {paginationMeta.page} of {paginationMeta.totalPages}
            </Typography>
            <IconButton
              onClick={() => handlePageChange(paginationMeta.page - 1)}
              disabled={paginationMeta.page === 1}
              sx={{
                color: isDarkMode ? 'rgba(255,255,255,0.7)' : 'inherit',
                '&:hover': {
                  backgroundColor: isDarkMode ? 'rgba(255,255,255,0.08)' : undefined,
                },
              }}
            >
              <ChevronLeft />
            </IconButton>
            {renderPageNumbers()}
            <IconButton
              onClick={() => handlePageChange(paginationMeta.page + 1)}
              disabled={paginationMeta.page === paginationMeta.totalPages}
              sx={{
                color: isDarkMode ? 'rgba(255,255,255,0.7)' : 'inherit',
                '&:hover': {
                  backgroundColor: isDarkMode ? 'rgba(255,255,255,0.08)' : undefined,
                },
              }}
            >
              <ChevronRight />
            </IconButton>
          </Stack>
        </Box>

        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell
                  sx={{
                    color: isDarkMode ? 'rgba(255,255,255,0.7)' : 'inherit',
                    borderBottomColor: isDarkMode
                      ? 'rgba(255,255,255,0.12)'
                      : undefined,
                  }}
                >
                  Review ID
                </TableCell>
                <TableCell
                  sx={{
                    color: isDarkMode ? 'rgba(255,255,255,0.7)' : 'inherit',
                    borderBottomColor: isDarkMode
                      ? 'rgba(255,255,255,0.12)'
                      : undefined,
                  }}
                >
                  ASIN
                </TableCell>
                <TableCell
                  sx={{
                    color: isDarkMode ? 'rgba(255,255,255,0.7)' : 'inherit',
                    borderBottomColor: isDarkMode
                      ? 'rgba(255,255,255,0.12)'
                      : undefined,
                  }}
                >
                  Brand Name
                </TableCell>
                <TableCell
                  sx={{
                    color: isDarkMode ? 'rgba(255,255,255,0.7)' : 'inherit',
                    borderBottomColor: isDarkMode
                      ? 'rgba(255,255,255,0.12)'
                      : undefined,
                  }}
                >
                  Review URL
                </TableCell>
                <TableCell
                  sx={{
                    color: isDarkMode ? 'rgba(255,255,255,0.7)' : 'inherit',
                    borderBottomColor: isDarkMode
                      ? 'rgba(255,255,255,0.12)'
                      : undefined,
                  }}
                >
                  Removed At
                </TableCell>
                <TableCell
                  sx={{
                    color: isDarkMode ? 'rgba(255,255,255,0.7)' : 'inherit',
                    borderBottomColor: isDarkMode
                      ? 'rgba(255,255,255,0.12)'
                      : undefined,
                  }}
                >
                  Created At
                </TableCell>
                <TableCell
                  sx={{
                    color: isDarkMode ? 'rgba(255,255,255,0.7)' : 'inherit',
                    borderBottomColor: isDarkMode
                      ? 'rgba(255,255,255,0.12)'
                      : undefined,
                  }}
                >
                  Updated At
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {removedReviews.map((review) => (
                <TableRow key={review.id}>
                  <TableCell
                    sx={{
                      color: isDarkMode ? '#fff' : 'inherit',
                      borderBottomColor: isDarkMode
                        ? 'rgba(255,255,255,0.12)'
                        : undefined,
                    }}
                  >
                    {review.reviewId}
                  </TableCell>
                  <TableCell
                    sx={{
                      color: isDarkMode ? '#fff' : 'inherit',
                      borderBottomColor: isDarkMode
                        ? 'rgba(255,255,255,0.12)'
                        : undefined,
                    }}
                  >
                    {review.asin}
                  </TableCell>
                  <TableCell
                    sx={{
                      color: isDarkMode ? '#fff' : 'inherit',
                      borderBottomColor: isDarkMode
                        ? 'rgba(255,255,255,0.12)'
                        : undefined,
                    }}
                  >
                    {review.brandName}
                  </TableCell>
                  <TableCell
                    sx={{
                      color: isDarkMode ? '#fff' : 'inherit',
                      borderBottomColor: isDarkMode
                        ? 'rgba(255,255,255,0.12)'
                        : undefined,
                    }}
                  >
                    <a
                      href={review.reviewUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      View Link
                    </a>
                  </TableCell>
                  <TableCell
                    sx={{
                      color: isDarkMode ? '#fff' : 'inherit',
                      borderBottomColor: isDarkMode
                        ? 'rgba(255,255,255,0.12)'
                        : undefined,
                    }}
                  >
                    {new Date(review.removedAt).toLocaleString()}
                  </TableCell>
                  <TableCell
                    sx={{
                      color: isDarkMode ? '#fff' : 'inherit',
                      borderBottomColor: isDarkMode
                        ? 'rgba(255,255,255,0.12)'
                        : undefined,
                    }}
                  >
                    {new Date(review.createdAt).toLocaleString()}
                  </TableCell>
                  <TableCell
                    sx={{
                      color: isDarkMode ? '#fff' : 'inherit',
                      borderBottomColor: isDarkMode
                        ? 'rgba(255,255,255,0.12)'
                        : undefined,
                    }}
                  >
                    {new Date(review.updatedAt).toLocaleString()}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </CardContent>
    </Card>
  );
};

export default RemovedReviewsPanel; 