'use client';

import React, { useEffect } from 'react';
import { Container, Grid } from '@mui/material';
import JeffAdminPanel from 'views/Jeff/JeffAdminPanel';
import { useUser } from 'views/Jeff/Utils/getUser';
import { useAdminTheme } from 'views/Jeff/AdminThemeContext';
import { useRouter } from 'next/navigation';
import ClientForm from './components/ClientForm';
import ClientSearch from './components/ClientSearch';
import ClientsTable from './components/ClientsTable';
import Notification from 'components/Notification';
import { useClientManagement } from './utils/hooks';

const ClientManagementPage = () => {
  const { data: user, isLoading: isUserLoading } = useUser();
  const router = useRouter();
  const { mode: adminThemeMode } = useAdminTheme() || { mode: 'light' };
  const isDarkMode = adminThemeMode === 'dark';
  
  const {
    clients,
    isLoading,
    isCreating,
    searchTerm,
    notification,
    handleCreateClient,
    handleSearch,
    handleCloseNotification
  } = useClientManagement(user);

  useEffect(() => {
    if (!isUserLoading && !user) {
      router.push('/jeff/login');
    } else if (!isUserLoading && user && user?.userType && user?.userType !== 'admin') {
      router.push('/jeff/dashboard');
    }
  }, [isUserLoading, user, router]);

  return (
    <JeffAdminPanel title="Client Management">
      <Container maxWidth="lg" sx={{ 
        py: 4,
        backgroundColor: isDarkMode ? '#1A202C' : 'inherit',
        color: isDarkMode ? '#E2E8F0' : 'inherit'
      }}>
        <Grid container spacing={3} mb={6}>
          <Grid item xs={12} md={6}>
            <ClientForm 
              onSubmit={handleCreateClient} 
              isCreating={isCreating} 
              isDarkMode={isDarkMode}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <ClientSearch 
              searchTerm={searchTerm} 
              onSearch={handleSearch} 
              isDarkMode={isDarkMode}
              clientCount={clients.length}
            />
          </Grid>
        </Grid>

        <ClientsTable 
          clients={clients} 
          isLoading={isLoading} 
          isDarkMode={isDarkMode} 
        />
        
        <Notification
          open={notification.open}
          message={notification.message}
          severity={notification.severity}
          onClose={handleCloseNotification}
        />
      </Container>
    </JeffAdminPanel>
  );
};

export default ClientManagementPage;
