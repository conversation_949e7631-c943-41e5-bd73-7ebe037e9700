import React from 'react';
import Box from '@mui/material/Box';
import { alpha } from '@mui/material/styles';
import Grid from '@mui/material/Grid';
import { colors, Typography } from '@mui/material';
import { useTheme } from '@mui/material/styles';

const CUSTOMERS = [
  '/images/jeff/customers/brandbuddy.png',
  //'/images/jeff/customers/cassette.png',
  '/images/jeff/customers/titan.png',
  //'/images/jeff/customers/edlighten.png',
  //'/images/jeff/customers/insightbond.png',
  //'/images/jeff/customers/prgenius.png',
  '/images/jeff/customers/7.png',
  '/images/jeff/customers/8.png',
  '/images/jeff/customers/9.png',
  '/images/jeff/customers/10.png',
  '/images/jeff/customers/11.png',
  '/images/jeff/customers/12.png',
];

const Customers = ({ text, styleProps }) => {
  const theme = useTheme();
  return (
    <Box
      sx={{
        backgroundColor: alpha(colors.blue[400], 0.4),
        padding: theme.spacing(2),
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
      }}
    >
      <Typography {...styleProps}>{text}</Typography>

      <Grid
        container
        spacing={2}
        justifyContent="center"
        maxWidth={900}
        sx={{ margin: 0, width: '100%' }}
      >
        {CUSTOMERS.map((cust, key) => (
          <Grid
            key={key}
            item
            xs={6} // 2 columns on small screens (12/6=2)
            md={3} // 4 columns on medium+ screens (12/3=4)
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              padding: 2, // Add padding for spacing
            }}
          >
            <img
              src={cust}
              style={{
                width: '100%', // Make images responsive
                height: 'auto',
                maxWidth: 270,
                objectFit: 'contain',
              }}
            />
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default Customers;
