'use client';

import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  CircularProgress,
} from '@mui/material';
import WarningIcon from '@mui/icons-material/Warning';
import { useAdminTheme } from 'views/Jeff/AdminThemeContext';

const DeleteConfirmationDialog = ({
  open,
  onClose,
  onConfirm,
  jobId,
  isDeleting,
}) => {
  const { mode: adminThemeMode } = useAdminTheme() || { mode: 'light' };
  const isDarkMode = adminThemeMode === 'dark';

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          backgroundColor: isDarkMode ? '#1A202C' : '#FFFFFF',
          backgroundImage: 'none'
        }
      }}
    >
      <DialogTitle sx={{ 
        backgroundColor: isDarkMode ? '#2D3748' : '#F7FAFC',
        color: isDarkMode ? '#E2E8F0' : '#2D3748',
        borderBottom: isDarkMode ? '1px solid rgba(255, 255, 255, 0.1)' : '1px solid rgba(0, 0, 0, 0.1)',
        px: 3,
        py: 2
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <WarningIcon sx={{ color: isDarkMode ? '#FC8181' : '#E53E3E', mr: 1 }} />
          <Typography variant="h6" component="div">
            Confirm Deletion
          </Typography>
        </Box>
      </DialogTitle>
      <DialogContent sx={{ p: 3, mt: 1 }}>
        <Typography variant="body1" sx={{ color: isDarkMode ? '#E2E8F0' : '#4A5568' }}>
          Are you sure you want to delete this lead job? {jobId} This action cannot be undone.
        </Typography>
        <Typography variant="body2" sx={{ color: isDarkMode ? '#A0AEC0' : '#718096', mt: 2 }}>
          All generated leads and associated data will be permanently removed from the system.
        </Typography>
      </DialogContent>
      <DialogActions sx={{ 
        px: 3, 
        py: 2,
        backgroundColor: isDarkMode ? '#2D3748' : '#F7FAFC',
        borderTop: isDarkMode ? '1px solid rgba(255, 255, 255, 0.1)' : '1px solid rgba(0, 0, 0, 0.1)',
      }}>
        <Button 
          onClick={onClose} 
          disabled={isDeleting}
          sx={{ 
            color: isDarkMode ? '#E2E8F0' : '#4A5568',
            '&:hover': {
              backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
            }
          }}
        >
          Cancel
        </Button>
        <Button 
          onClick={onConfirm}
          disabled={isDeleting}
          variant="contained"
          startIcon={isDeleting ? <CircularProgress size={20} color="inherit" /> : null}
          sx={{
            backgroundColor: isDarkMode ? '#E53E3E' : '#F56565',
            color: '#FFFFFF',
            '&:hover': {
              backgroundColor: isDarkMode ? '#C53030' : '#E53E3E',
            },
            '&:disabled': {
              backgroundColor: isDarkMode ? 'rgba(229, 62, 62, 0.5)' : 'rgba(245, 101, 101, 0.5)',
              color: '#FFFFFF',
            }
          }}
        >
          {isDeleting ? 'Deleting...' : 'Delete'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DeleteConfirmationDialog; 