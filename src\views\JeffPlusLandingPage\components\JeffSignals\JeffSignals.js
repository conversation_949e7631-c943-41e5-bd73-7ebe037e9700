import React from 'react';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Grid from '@mui/material/Grid';
import { useTheme, alpha } from '@mui/material/styles';
import { Chip, colors, Stack } from '@mui/material';
import Spacer from '../../../../components/Equal/Spacer';
import TextTransition, { presets } from 'react-text-transition';  
import Rating from '@mui/material/Rating';

import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import AddIcon from '@mui/icons-material/Add';
import StyleIcon from '@mui/icons-material/Style';
import DataThresholdingIcon from '@mui/icons-material/DataThresholding';
import RateReviewIcon from '@mui/icons-material/RateReview';
import StorefrontIcon from '@mui/icons-material/Storefront';
import AdsClickIcon from '@mui/icons-material/AdsClick';
import ImageIcon from '@mui/icons-material/Image';
import TitleIcon from '@mui/icons-material/Title';
import PhotoCameraIcon from '@mui/icons-material/PhotoCamera';
import ThumbsUpDownIcon from '@mui/icons-material/ThumbsUpDown';
import StarHalfIcon from '@mui/icons-material/StarHalf';
import BusinessIcon from '@mui/icons-material/Business';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import TextSnippetIcon from '@mui/icons-material/TextSnippet';
import SellIcon from '@mui/icons-material/Sell';
import NextWeekIcon from '@mui/icons-material/NextWeek';

const JEFF_ANALYSIS_STATE = [
  'Finding relevant case studies',
  'Searching for optimisation inisghts',
  'Competitor comparison happening',
  'Fetching amazon audit'
];

const YOUR_DATA = [
  {
    icon: RateReviewIcon,
    text: 'Your case studies & testimonials'
  },
  {
    icon: DataThresholdingIcon,
    text: 'Past Sales Conversions'
  },
  {
    icon: StyleIcon,
    text: 'Your brand communication style'
  },
];

const AMAZON_DATA = [
  {
    icon: StorefrontIcon,
    text: 'Are they selling on Amazon?'
  },
  {
    icon: AdsClickIcon,
    text: 'Are they running ads?'
  },
  {
    icon: ImageIcon,
    text: 'Do they have A+ content/videos?'
  },
  {
    icon: TitleIcon,
    text: 'Title optimisation insights'
  },
  {
    icon: PhotoCameraIcon,
    text: 'Image quality & optimisations'
  },
  {
    icon: ThumbsUpDownIcon,
    text: 'Storefront quality'
  },
  {
    icon: StarHalfIcon,
    text: 'Review analysis'
  },
  {
    icon: MonetizationOnIcon,
    text: 'Listing revenue'
  },
  {
    icon: BusinessIcon,
    text: 'Competitors'
  }
];

const WEBSITE_DATA = [
  {
    icon: TextSnippetIcon,
    text: 'About section'
  },
  {
    icon: SellIcon,
    text: 'Other Products sold'
  },
  {
    icon: NextWeekIcon,
    text: 'Mission statement & values'
  }
];

const Team = () => {
  const theme = useTheme();
  const [jeffAnalysisIndex, setFeffAnalysisIndex] = React.useState(0);

  React.useEffect(() => {
    const intervalId = setInterval(
      () => setFeffAnalysisIndex((jeffAnalysisIndex) => jeffAnalysisIndex + 1),
      3000, // every 3 seconds
    );
    return () => clearTimeout(intervalId);
  }, []);


  return (
    <Box>
      <Typography variant={'h4'} sx={{ fontWeight: 700, textAlign: 'center' }} gutterBottom>
        {'Personalised on '}
        <Typography
          color={'primary'}
          component={'span'}
          variant={'inherit'}
          sx={{
            fontWeight: 700,
            display: 'inline',
            background: `linear-gradient(180deg, transparent 82%, ${alpha(
              colors.purple[200],
              0.3,
            )} 0%)`,
          }}
        >
          {'20+ signals'}
        </Typography>
      </Typography>
      <Spacer y={2} />
      <Grid container spacing={2}>
        <Grid item md={4} sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', flexDirection: 'column' }}>

          <Box component={Card} variant='outlined'>
            <CardContent>
              <Typography variant="h5" sx={{ fontWeight: 700 }}>Your Data</Typography>
              <Spacer y={1} />
              <Stack direction="row" spacing={1} useFlexGap flexWrap="wrap">
                {YOUR_DATA.map((dataChip, key) => {
                  return (
                    <Chip
                      key={key}
                      icon={<dataChip.icon />}
                      label={dataChip.text}
                      variant="filled"
                      sx={{ borderRadius: 1 }} 
                      color="info"
                    />
                  );
                })}
              </Stack>
            </CardContent>
          </Box>

          <Box sx={{ padding: theme.spacing(1), display: 'flex', justifyContent: 'center' }}>
            <AddIcon sx={{ width: 30, height: 30 }} />
          </Box>
          <Box component={Card} variant='outlined'>
            <CardContent>
              <Typography variant="h5" sx={{ fontWeight: 700 }}>Amazon Data</Typography>
              <Spacer y={1} />
              <Stack direction="row" spacing={1} useFlexGap flexWrap="wrap">
                {AMAZON_DATA.map((dataChip, key) => {
                  return (
                    <Chip
                      key={key}
                      icon={<dataChip.icon />}
                      label={dataChip.text}
                      variant="outlined"
                      sx={{ borderRadius: 1 }} 
                    />
                  );
                })}
              </Stack>
            </CardContent>
          </Box>
          <Box sx={{ padding: theme.spacing(1), display: 'flex', justifyContent: 'center' }}>
            <AddIcon sx={{ width: 30, height: 30 }} />
          </Box>
          <Box component={Card} variant='outlined'>
            <CardContent>
              <Typography variant="h5" sx={{ fontWeight: 700 }}>Website Data</Typography>
              <Spacer y={1} />
              <Stack direction="row" spacing={1} useFlexGap flexWrap="wrap">
                {WEBSITE_DATA.map((dataChip, key) => {
                  return (
                    <Chip
                      key={key}
                      icon={<dataChip.icon />}
                      label={dataChip.text}
                      variant="filled"
                      color="primary"
                      sx={{ borderRadius: 1 }} 
                    />
                  );
                })}
              </Stack>
            </CardContent>
          </Box>
        </Grid>
        <Grid item md={3} sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', flexDirection: 'column' }}>
          <Box sx={{ backgroundColor: colors.purple[900], padding: theme.spacing(1), borderRadius: theme.spacing(2) }}>
            <Typography sx={{ color: '#fff' }} align="center">Jeff analyses 20+ data points about every seller & their listings.</Typography>
          </Box>
          <Spacer y={2} />
          <img src="/images/jeff/persona.png" width={200} />
          <Spacer y={2} />
          <Box sx={{ backgroundColor: colors.blue[200], padding: theme.spacing(0.8), borderRadius: theme.spacing(1), minWidth: '100%', display: 'flex', justifyContent: 'center' }}>
            <TextTransition style={{ align: 'center' }} inline springConfig={presets.wobbly}>{JEFF_ANALYSIS_STATE[jeffAnalysisIndex % JEFF_ANALYSIS_STATE.length]}</TextTransition>
          </Box>
        </Grid>
        <Grid item md={5}>
          <Box component={Card} variant='outlined'>
            <CardContent>
              <img src="/images/jeff/linkedinIcon.png" style={{ width: 25, height: 25  }} />
              <Typography variant="h5" sx={{ fontWeight: 700 }}>Personalised LinkedIn Message</Typography>
              <Spacer y={1} />
              <Typography>
                {'We did an Amazon audit of your product listing '}
                <Typography variant='inherit' component={'span'} sx={{ backgroundColor: colors.amber[200], borderRadius: 1, padding: theme.spacing(0.2) }}>
                  {'"Speed loader by Parabellum Designs"'}
                </Typography>
                {' and realized you could at least be making '}
                <Typography variant='inherit' component={'span'} sx={{ backgroundColor: colors.amber[200], borderRadius: 1, padding: theme.spacing(0.2) }}>
                  {'$34,311'}
                </Typography>
                {'more within the next 30 days.'}
              </Typography>
              <Spacer y={2} />
              <Typography>
                {'We analyzed your competitor '}
                <Typography variant='inherit' component={'span'} sx={{ backgroundColor: colors.amber[200], borderRadius: 1, padding: theme.spacing(0.2) }}>
                  {'Generic’s'}
                </Typography>
                {' listing, '}
                <Typography variant='inherit' component={'span'} sx={{ backgroundColor: colors.amber[200], borderRadius: 1, padding: theme.spacing(0.2) }}>
                  {'"Magazine speedloader for 9mm to 45 calibers".'}
                </Typography>
                {' They are current earning'}
                <Typography variant='inherit' component={'span'} sx={{ backgroundColor: colors.amber[200], borderRadius: 1, padding: theme.spacing(0.2) }}>
                  {'$43,207'}
                </Typography>
                {' per month while you’re earning '}
                <Typography variant='inherit' component={'span'} sx={{ backgroundColor: colors.amber[200], borderRadius: 1, padding: theme.spacing(0.2) }}>
                  {'$8,896.'}
                </Typography>
                {' Do you have a plan to address this gap?'}
              </Typography>
              
            </CardContent>
          </Box>
          <Spacer y={2} />
          <Box component={Card} variant='outlined'>
            <CardContent>
              <img src="/images/jeff/gmailIcon.png" style={{ width: 25, height: 25  }} />
              <Typography variant="h5" sx={{ fontWeight: 700 }}>eMail 1 about Pain Point</Typography>
              <Spacer y={1} />
              <Box>
                <Typography>
                  {'Hello '}
                  <Typography variant='inherit' component={'span'} sx={{ backgroundColor: colors.amber[200], borderRadius: 1, padding: theme.spacing(0.2) }}>{'{Prospect Name},'}</Typography>
                </Typography>
                <Spacer y={2} />
                <Typography>
                  {'As someone who heavily relies on quality tools for my DIY projects, I was thrilled to come across '}
                  <Typography variant='inherit' component={'span'} sx={{ backgroundColor: colors.amber[200], borderRadius: 1, padding: theme.spacing(0.2) }}>
                    {'TradeGear, especially your matte finished electrical tapes.'}
                  </Typography>
                </Typography>
                <Spacer y={2} />
                <Typography>
                  {'After doing your Amazon audit, I noticed that despite your'}
                  <Typography variant='inherit' component={'span'} sx={{ backgroundColor: colors.amber[200], borderRadius: 1, padding: theme.spacing(0.2) }}>
                    {'4.5-star rating from 5,310+ reviews, TradeGear is not running any ads.'}
                  </Typography>
                  {' You are leaving money on the table, especially when you have 5 star reviews like Jenny praising the matte purple tape\'s quality.'}
                </Typography>
                <Spacer y={2} />
                <Typography>
                  {'At '}
                  <Typography variant='inherit' component={'span'} sx={{ backgroundColor: colors.amber[200], borderRadius: 1, padding: theme.spacing(0.2) }}>
                    {'{your company name}'}
                  </Typography>
                  {' we leverage our extensive experience in Amazon Ads to transform visibility and sales. For instance, we helped '}
                  <Typography variant='inherit' component={'span'} sx={{ backgroundColor: colors.amber[200], borderRadius: 1, padding: theme.spacing(0.2) }}>
                    {'{brand name from relevant case study}'}
                  </Typography>
                  {' achieve over '}
                  <Typography variant='inherit' component={'span'} sx={{ backgroundColor: colors.amber[200], borderRadius: 1, padding: theme.spacing(0.2) }}>
                    {'$400k in revenue '}
                  </Typography>
                  within six months.
                  <Typography variant='inherit' component={'span'} sx={{ backgroundColor: colors.amber[200], borderRadius: 1, padding: theme.spacing(0.2) }}>
                    {'while maintaining an ACoS below 20%'}
                  </Typography>
                </Typography>
                <Spacer y={2} />
                <Typography>
                  {'Would you be free sometime next week for a quick call around Amazon?'}
                </Typography>
              </Box>
            </CardContent>
          </Box>
          <Spacer y={2} />
          <Box component={Card} variant='outlined'>
            <CardContent>
              <img src="/images/jeff/amazonIcon.png" style={{ width: 25, height: 25  }} />
              <Typography variant="h5" sx={{ fontWeight: 700 }}>Personalised Amazon Audit</Typography>
              <Typography>Detailed amazon audit for every seller. Fully automated.</Typography>
              <Spacer y={1} />
              <Grid container spacing={0.5}>
                <Grid item md={6} xs={12}>
                  <Typography variant="h6" sx={{ fontSize: '17px !important', fontWeight: 700 }}>Storefront Quality</Typography>
                  <Box sx={{ display: 'flex' }}>
                    <CancelIcon color='error' />
                    <Spacer x={0.5} />
                    <Typography>Okay</Typography>
                    <Spacer x={1} />
                    <Rating name="read-only" value={2} readOnly size="small" />
                  </Box>
                </Grid>
                <Grid item md={6} xs={12}>
                  <Typography variant="h6" sx={{ fontSize: '17px !important', fontWeight: 700 }}>Ad Quality</Typography>
                  <Box sx={{ display: 'flex' }}>
                    <CheckCircleIcon color='success' />
                    <Spacer x={0.5} />
                    <Typography>Decent</Typography>
                    <Spacer x={1} />
                    <Rating name="read-only" value={2} readOnly size="small" />
                  </Box>
                </Grid>
                <Grid item md={6} xs={12}>
                  <Typography variant="h6" sx={{ fontSize: '17px !important', fontWeight: 700 }}>Video</Typography>
                  <Box sx={{ display: 'flex' }}>
                    <CheckCircleIcon color='success' />
                    <Spacer x={0.5} />
                    <Typography>Great</Typography>
                    <Spacer x={1} />
                    <Rating name="read-only" value={4.5} precision={0.25} readOnly size="small" />
                  </Box>
                </Grid>
                <Grid item md={6} xs={12}>
                  <Typography variant="h6" sx={{ fontSize: '17px !important', fontWeight: 700 }}>A+ Content</Typography>
                  <Box sx={{ display: 'flex' }}>
                    <CheckCircleIcon color='success' />
                    <Spacer x={0.5} />
                    <Typography>Decent</Typography>
                    <Spacer x={1} />
                    <Rating name="read-only" value={2} readOnly size="small" />
                  </Box>
                </Grid>
              </Grid>
              <Spacer y={2} />
            </CardContent>
          </Box>
          <Spacer y={2} />
        </Grid>
      </Grid>
      <Spacer y={2} />

    </Box>
  );
};

export default Team;