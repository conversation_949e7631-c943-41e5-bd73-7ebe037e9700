'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  CircularProgress,
  Divider,
  Grid,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Card,
  CardContent,
  IconButton,
  Checkbox,
  FormControlLabel,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Paper,
} from '@mui/material';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import CloseIcon from '@mui/icons-material/Close';
import AsinTable from './AsinTable';
import { useRouter, useSearchParams } from 'next/navigation';
import { getAxiosInstance } from 'config/axios';
import { API_ENDPOINTS } from 'config/api';

const AsinTab = ({ isDarkMode, setNotification }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [countryCode, setCountryCode] = useState('');
  const [csvFile, setCsvFile] = useState(null);
  const [filters, setFilters] = useState({
    sellerId: searchParams.get('sellerId') || '',
    sellerName: searchParams.get('sellerName') || '',
    asin: searchParams.get('asin') || '',
    reviewsCount: searchParams.get('reviewsCount') || '',
    reviewOperator: searchParams.get('reviewOperator') || '',
    status: searchParams.get('status') || '',
    type: searchParams.get('type') || '',
  });
  const [isFiltering, setIsFiltering] = useState(false);
  const [asin, setAsin] = useState('');
  const [filteredAsinsData, setFilteredAsinsData] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  });

  const [isCreateLoading, setIsCreateLoading] = useState(false);

  const getASINWithURLParams = async () => {
    const urlSellerId = searchParams.get('sellerId');
    const urlSellerName = searchParams.get('sellerName');
    const urlAsin = searchParams.get('asin');
    const urlReviewsCount = searchParams.get('reviewsCount');
    const urlReviewOperator = searchParams.get('reviewOperator');
    const urlStatus = searchParams.get('status');
    const urlType = searchParams.get('type');

    // Update filters state with URL parameters
    const newFilters = {
      sellerId: urlSellerId || '',
      sellerName: urlSellerName || '',
      asin: urlAsin || '',
      reviewsCount: urlReviewsCount || '',
      reviewOperator: urlReviewOperator || '',
      status: urlStatus || '',
      type: urlType || '',
    };
    setFilters(newFilters);

    await getASIN(newFilters);
  };

  // Call API on component mount and when URL parameters change
  useEffect(() => {
    console.log('Useeffect hit');
    getASINWithURLParams();
  }, [
    searchParams.get('sellerId'),
    searchParams.get('sellerName'),
    searchParams.get('asin'),
    searchParams.get('reviewsCount'),
    searchParams.get('reviewOperator'),
    searchParams.get('status'),
    searchParams.get('type'),
    pagination.page,
    pagination.limit,
  ]);

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      setCsvFile(file);
    }
  };

  const handleGetData = async () => {
    console.log('first', csvFile, asin, countryCode);
    if (!csvFile && !asin) {
      setNotification({
        open: true,
        message: 'Please provide either a CSV file or a ASIN.',
        severity: 'warning',
      });
      return;
    }

    setIsCreateLoading(true);
    if (csvFile) {
      try {
        const formData = new FormData();
        formData.append('file', csvFile);

        const response = await getAxiosInstance({
          cookiesKey: 'jeff-authorization',
        }).post(API_ENDPOINTS.LEX_ASIN_BULK, formData);

        console.log('response', response);

        await getASINWithURLParams();

        setCsvFile(null);
        // Check for invalid ASINs in the response
        const { summary, invalidAsins } = response.data || {};
        if (
          response.data.success &&
          Object.prototype.hasOwnProperty.call(response.data, 'invalidAsins') &&
          Array.isArray(invalidAsins) &&
          invalidAsins.length > 0
        ) {
          // Compose message
          let message = `Total invalid ASINs: ${
            summary && summary.invalidAsins
              ? summary.invalidAsins
              : invalidAsins.length
          }`;
          message += '\n';
          message += invalidAsins
            .slice(0, 3)
            .map((item) => `• ${item.original}: ${item.reason}`)
            .join('\n');
          setNotification({
            open: true,
            message,
            severity: 'warning',
            autoHide: false,
            duration: 1500,
          });
        } else {
          setNotification({
            open: true,
            message: 'ASIN job created successfully',
            severity: 'success',
          });
        }
      } catch (error) {
        console.error('Error creating ASIN job:', error);

        // Check if error response has specific error details
        let errorMessage = 'Failed to create ASIN job. Please try again.';

        if (error.response && error.response.data) {
          const { error: apiError, provided } = error.response.data;
          if (apiError) {
            errorMessage = apiError;
            if (provided) {
              errorMessage += ` (Provided: ${provided})`;
            }
          }
        }

        setNotification({
          open: true,
          message: errorMessage,
          severity: 'error',
          autoHide: false,
          duration: 1500,
        });
      } finally {
        setIsCreateLoading(false);
        setCsvFile(null);
        setAsin('');
        setCountryCode('');
      }
    } else {
      try {
        const response = await getAxiosInstance({
          cookiesKey: 'jeff-authorization',
        }).post(API_ENDPOINTS.LEX_ASIN, {
          asin: asin,
          ...(countryCode ? { country_code: countryCode } : {}),
        });

        console.log('response', response);

        await getASINWithURLParams();
        setAsin('');
        setCountryCode('');
        setNotification({
          open: true,
          message: 'ASIN job created successfully',
          severity: 'success',
        });
      } catch (error) {
        console.error('Error creating ASIN job:', error);
        // Use the error key as the message and append provided key if present (for ASIN format errors)
        let errorMessage = 'Failed to create ASIN job. Please try again.';
        if (error.response && error.response.data) {
          const { error: apiError, provided } = error.response.data;
          if (apiError) {
            errorMessage = apiError;
            if (provided) {
              errorMessage += `\nProvided: ${provided}`;
            }
          }
        }
        setNotification({
          open: true,
          message: errorMessage,
          severity: 'error',
          autoHide: false,
          duration: 1500,
        });
      } finally {
        setIsCreateLoading(false);
        setCsvFile(null);
        setAsin('');
        setCountryCode('');
      }
    }
  };

  const handleFilterChange = (field) => (event) => {
    setFilters({
      ...filters,
      [field]: event.target.value,
    });
  };

  const handleReviewsChange = (event) => {
    const value = event.target.value;
    // Only allow positive numbers including 0
    if (value === '' || /^[0-9]+$/.test(value)) {
      setFilters({
        ...filters,
        reviewsCount: value,
      });
    }
  };

  const getASIN = async (filterParams) => {
    try {
      setIsFiltering(true);
      const params = new URLSearchParams();

      // Only add filter parameters if filterParams is provided and has values
      if (filterParams && filterParams.sellerId)
        params.append('seller_id', filterParams.sellerId);
      if (filterParams && filterParams.sellerName)
        params.append('seller_name', filterParams.sellerName);
      if (filterParams && filterParams.asin)
        params.append('asin', filterParams.asin);
      if (filterParams && filterParams.status)
        params.append('status', filterParams.status);
      if (filterParams && filterParams.type)
        params.append('type', filterParams.type);
      if (
        filterParams &&
        filterParams.reviewsCount &&
        filterParams.reviewOperator
      ) {
        params.append(
          'reviews',
          `${filterParams.reviewOperator}${filterParams.reviewsCount}`,
        );
      }

      // Add pagination parameters
      params.append('page', pagination.page);
      params.append('limit', pagination.limit);

      // Build URL based on whether any params exist
      let url = API_ENDPOINTS.LEX_ASIN;
      if ([...params].length > 0) {
        url += `?${params.toString()}`;
      }

      const response = await getAxiosInstance({
        cookiesKey: 'jeff-authorization',
      }).get(url);

      console.log('first response', response.data);

      if (response.data) {
        setFilteredAsinsData(response.data.asins || []);
        setPagination(
          response.data.pagination || {
            page: 1,
            limit: 20,
            total: 0,
            totalPages: 0,
            hasNext: false,
            hasPrev: false,
          },
        );
      } else {
        setFilteredAsinsData([]);
      }
    } catch (error) {
      console.error('Error fetching ASIN data:', error);
      setFilteredAsinsData([]);
    } finally {
      setIsFiltering(false);
    }
  };

  const handleApplyFilters = () => {
    // Clean up comma-separated fields before using them
    const clean = (val) =>
      val
        .split(',')
        .map((v) => v.trim())
        .filter((v) => v !== '')
        .join(',');

    if (
      (filters.reviewsCount && !filters.reviewOperator) ||
      (filters.reviewOperator && !filters.reviewsCount)
    ) {
      setNotification({
        open: true,
        message: 'Please provide both review count and operator.',
        severity: 'warning',
      });
      return;
    }

    // Create URL parameters
    const params = new URLSearchParams();
    if (filters.sellerId) params.set('sellerId', clean(filters.sellerId));
    if (filters.sellerName) params.set('sellerName', clean(filters.sellerName));
    if (filters.asin) params.set('asin', clean(filters.asin));
    if (filters.status) params.set('status', filters.status);
    if (filters.type) params.set('type', filters.type);
    if (filters.reviewsCount && filters.reviewOperator) {
      params.set('reviewsCount', filters.reviewsCount);
    }
    if (filters.reviewOperator) {
      params.set('reviewOperator', filters.reviewOperator);
    }

    // Update URL with filter parameters
    const newUrl = `/jeff/admin/seller?tabID=1&${params.toString()}`;
    router.push(newUrl);
  };

  const handleResetFilters = () => {
    // Reset all filters to empty values
    setFilters({
      sellerId: '',
      sellerName: '',
      asin: '',
      reviewsCount: '',
      reviewOperator: '',
      status: '',
      type: '',
    });

    // Update URL to remove all filter parameters
    router.push('/jeff/admin/seller?tabID=1');
  };

  const handleOperatorClear = (event) => {
    event.stopPropagation();
    setFilters({
      ...filters,
      reviewOperator: '',
    });
  };

  const handlePageChange = (event, newPage) => {
    setPagination((prev) => ({
      ...prev,
      page: newPage + 1, // MUI uses 0-based indexing
    }));
  };

  const handleRowsPerPageChange = (event) => {
    setPagination((prev) => ({
      ...prev,
      page: 1, // Reset to first page when changing rows per page
      limit: parseInt(event.target.value, 10),
    }));
  };

  return (
    <Box sx={{ mb: 3 }}>
      {/* Inputs and Get Button Card */}
      <Card
        sx={{
          mb: 3,
          backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        }}
      >
        <CardContent>
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              color: isDarkMode ? '#fff' : 'inherit',
              mb: 3,
            }}
          >
            Create ASIN Job
          </Typography>

          {/* ASIN and Country Code Inputs */}
          <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
            <TextField
              label="ASIN"
              variant="outlined"
              value={asin}
              onChange={(e) => setAsin(e.target.value)}
              size="small"
              required
              sx={{
                flex: 1,
                '& .MuiOutlinedInput-root': {
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  '& fieldset': {
                    borderColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.23)'
                      : 'rgba(0, 0, 0, 0.23)',
                  },
                  '&:hover fieldset': {
                    borderColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.4)'
                      : 'rgba(0, 0, 0, 0.4)',
                  },
                },
                '& .MuiInputLabel-root': {
                  color: isDarkMode
                    ? 'rgba(255, 255, 255, 0.7)'
                    : 'rgba(0, 0, 0, 0.6)',
                  '&.Mui-focused': {
                    color: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                },
                '& .MuiInputLabel-asterisk': {
                  color: isDarkMode ? '#EF5350' : '#D32F2F',
                },
              }}
            />
            <FormControl
              size="small"
              fullWidth
              sx={{
                flex: 1,
              }}
            >
              <InputLabel
                sx={{
                  color: isDarkMode
                    ? 'rgba(255, 255, 255, 0.7)'
                    : 'rgba(0, 0, 0, 0.6)',
                }}
              >
                Country Code
              </InputLabel>
              <Select
                value={countryCode}
                onChange={(e) => setCountryCode(e.target.value)}
                label="Country Code"
                sx={{
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.23)'
                      : 'rgba(0, 0, 0, 0.23)',
                  },
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.4)'
                      : 'rgba(0, 0, 0, 0.4)',
                  },
                  '& .MuiSelect-icon': {
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  },
                }}
              >
                <MenuItem value="US">US</MenuItem>
                <MenuItem value="UK">UK</MenuItem>
                <MenuItem value="CA">CA</MenuItem>
              </Select>
            </FormControl>
          </Box>

          {/* Divider with OR text */}
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <Divider
              sx={{
                flex: 1,
                borderColor: isDarkMode
                  ? 'rgba(255, 255, 255, 0.12)'
                  : 'rgba(0, 0, 0, 0.12)',
              }}
            />
            <Typography
              sx={{
                mx: 2,
                color: isDarkMode ? '#e2e8f0' : '#4a5568',
                fontWeight: 500,
              }}
            >
              OR
            </Typography>
            <Divider
              sx={{
                flex: 1,
                borderColor: isDarkMode
                  ? 'rgba(255, 255, 255, 0.12)'
                  : 'rgba(0, 0, 0, 0.12)',
              }}
            />
          </Box>

          {/* CSV Upload Section */}
          <Box
            sx={{
              border: `2px dashed ${
                isDarkMode ? 'rgba(255, 255, 255, 0.23)' : 'rgba(0, 0, 0, 0.23)'
              }`,
              borderRadius: 2,
              p: 3,
              mb: 3,
              textAlign: 'center',
              backgroundColor: isDarkMode
                ? 'rgba(255, 255, 255, 0.05)'
                : 'rgba(0, 0, 0, 0.02)',
              '&:hover': {
                borderColor: isDarkMode
                  ? 'rgba(255, 255, 255, 0.4)'
                  : 'rgba(0, 0, 0, 0.4)',
                backgroundColor: isDarkMode
                  ? 'rgba(255, 255, 255, 0.08)'
                  : 'rgba(0, 0, 0, 0.04)',
              },
            }}
          >
            <input
              type="file"
              accept=".csv"
              onChange={handleFileChange}
              style={{ display: 'none' }}
              id="csv-upload"
            />
            <label htmlFor="csv-upload">
              <Button
                component="span"
                variant="outlined"
                startIcon={<CloudUploadIcon />}
                sx={{
                  color: isDarkMode ? '#90CAF9' : '#1976d2',
                  borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  '&:hover': {
                    borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                    backgroundColor: isDarkMode
                      ? 'rgba(144, 202, 249, 0.08)'
                      : 'rgba(25, 118, 210, 0.08)',
                  },
                }}
              >
                Upload CSV File
              </Button>
            </label>
            {csvFile && (
              <Typography
                sx={{
                  mt: 1,
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                }}
              >
                Selected file: {csvFile.name}
              </Typography>
            )}
          </Box>
          {/* Sample CSV Format Section */}
          <Box sx={{ mt: 1, mb: 4 }}>
            <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
              Sample CSV Format
            </Typography>

            <Paper
              elevation={0}
              sx={{
                border: '1px solid #e0e0e0',
                borderRadius: 1,
                overflowX: 'auto',
              }}
            >
              <Table size="small">
                <TableHead>
                  <TableRow sx={{ backgroundColor: '#f9f9f9' }}>
                    <TableCell sx={{ fontWeight: 600 }}>ASIN</TableCell>
                    <TableCell sx={{ fontWeight: 600 }}>COUNTRY</TableCell>
                    <TableCell sx={{ fontWeight: 600 }}>
                      TYPE{' '}
                      <Typography variant="caption" component="span">
                        (optional, only CLIENT)
                      </Typography>
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  <TableRow>
                    <TableCell>B08L5TNJHG</TableCell>
                    <TableCell>US</TableCell>
                    <TableCell>CLIENT</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>B07FZ8S74R</TableCell>
                    <TableCell>UK</TableCell>
                    <TableCell />
                  </TableRow>
                </TableBody>
              </Table>
            </Paper>
          </Box>

          {/* Get Button */}
          <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="contained"
              onClick={handleGetData}
              disabled={isCreateLoading}
              sx={{
                backgroundColor: isDarkMode ? '#90CAF9' : '#1976d2',
                '&:hover': {
                  backgroundColor: isDarkMode ? '#64B5F6' : '#1565C0',
                },
                minWidth: '120px',
                py: 1,
                px: 4,
              }}
            >
              {isCreateLoading ? (
                <CircularProgress size={24} color="inherit" />
              ) : (
                'Create'
              )}
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Filter Section Card */}
      <Card
        sx={{
          mb: 3,
          backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        }}
      >
        <CardContent>
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              color: isDarkMode ? '#fff' : 'inherit',
              mb: 3,
            }}
          >
            Filters
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Seller ID(s) (comma separated)"
                variant="outlined"
                value={filters.sellerId}
                onChange={handleFilterChange('sellerId')}
                size="small"
                fullWidth
                multiline
                rows={2}
                placeholder="Enter comma-separated seller IDs"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    '& fieldset': {
                      borderColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.23)'
                        : 'rgba(0, 0, 0, 0.23)',
                    },
                    '&:hover fieldset': {
                      borderColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.4)'
                        : 'rgba(0, 0, 0, 0.4)',
                    },
                  },
                  '& .MuiInputLabel-root': {
                    color: isDarkMode
                      ? 'rgba(255, 255, 255, 0.7)'
                      : 'rgba(0, 0, 0, 0.6)',
                  },
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                label="Seller Name(s) (comma separated)"
                variant="outlined"
                value={filters.sellerName}
                onChange={handleFilterChange('sellerName')}
                size="small"
                fullWidth
                multiline
                rows={2}
                placeholder="Enter comma-separated seller names"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    '& fieldset': {
                      borderColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.23)'
                        : 'rgba(0, 0, 0, 0.23)',
                    },
                    '&:hover fieldset': {
                      borderColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.4)'
                        : 'rgba(0, 0, 0, 0.4)',
                    },
                  },
                  '& .MuiInputLabel-root': {
                    color: isDarkMode
                      ? 'rgba(255, 255, 255, 0.7)'
                      : 'rgba(0, 0, 0, 0.6)',
                  },
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                label="ASIN(s) (comma separated)"
                variant="outlined"
                value={filters.asin}
                onChange={handleFilterChange('asin')}
                size="small"
                fullWidth
                multiline
                rows={2}
                placeholder="Enter comma-separated ASINs"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    '& fieldset': {
                      borderColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.23)'
                        : 'rgba(0, 0, 0, 0.23)',
                    },
                    '&:hover fieldset': {
                      borderColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.4)'
                        : 'rgba(0, 0, 0, 0.4)',
                    },
                  },
                  '& .MuiInputLabel-root': {
                    color: isDarkMode
                      ? 'rgba(255, 255, 255, 0.7)'
                      : 'rgba(0, 0, 0, 0.6)',
                  },
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl
                size="small"
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    '& fieldset': {
                      borderColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.23)'
                        : 'rgba(0, 0, 0, 0.23)',
                    },
                    '&:hover fieldset': {
                      borderColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.4)'
                        : 'rgba(0, 0, 0, 0.4)',
                    },
                  },
                  '& .MuiInputLabel-root': {
                    color: isDarkMode
                      ? 'rgba(255, 255, 255, 0.7)'
                      : 'rgba(0, 0, 0, 0.6)',
                  },
                }}
              >
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status}
                  onChange={handleFilterChange('status')}
                  label="Status"
                  endAdornment={
                    filters.status && (
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          setFilters({
                            ...filters,
                            status: '',
                          });
                        }}
                        sx={{
                          position: 'absolute',
                          right: 32,
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          '&:hover': {
                            backgroundColor: isDarkMode
                              ? 'rgba(255, 255, 255, 0.08)'
                              : 'rgba(0, 0, 0, 0.04)',
                          },
                        }}
                      >
                        <CloseIcon fontSize="small" />
                      </IconButton>
                    )
                  }
                  sx={{
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    '& .MuiSelect-icon': {
                      color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    },
                  }}
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="PENDING">Pending</MenuItem>
                  <MenuItem value="SCRAPED">ASIN Scraped</MenuItem>
                  <MenuItem value="FAILED">Failed</MenuItem>
                  <MenuItem value="REVIEW_PENDING">Review Pending</MenuItem>
                  <MenuItem value="REVIEW_IN_PROGRESS">
                    Review In Progress
                  </MenuItem>
                  <MenuItem value="REVIEW_SCRAPED">Review Scraped</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <FormControl
                  size="small"
                  sx={{
                    flex: 1,
                    '& .MuiOutlinedInput-root': {
                      color: isDarkMode ? '#e2e8f0' : '#4a5568',
                      '& fieldset': {
                        borderColor: isDarkMode
                          ? 'rgba(255, 255, 255, 0.23)'
                          : 'rgba(0, 0, 0, 0.23)',
                      },
                      '&:hover fieldset': {
                        borderColor: isDarkMode
                          ? 'rgba(255, 255, 255, 0.4)'
                          : 'rgba(0, 0, 0, 0.4)',
                      },
                    },
                    '& .MuiInputLabel-root': {
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.7)'
                        : 'rgba(0, 0, 0, 0.6)',
                    },
                  }}
                >
                  <InputLabel>Operator</InputLabel>
                  <Select
                    value={filters.reviewOperator}
                    onChange={handleFilterChange('reviewOperator')}
                    label="Operator"
                    endAdornment={
                      filters.reviewOperator && (
                        <IconButton
                          size="small"
                          onClick={handleOperatorClear}
                          sx={{
                            position: 'absolute',
                            right: 32,
                            color: isDarkMode ? '#e2e8f0' : '#4a5568',
                            '&:hover': {
                              backgroundColor: isDarkMode
                                ? 'rgba(255, 255, 255, 0.08)'
                                : 'rgba(0, 0, 0, 0.04)',
                            },
                          }}
                        >
                          <CloseIcon fontSize="small" />
                        </IconButton>
                      )
                    }
                    sx={{
                      color: isDarkMode ? '#e2e8f0' : '#4a5568',
                      '& .MuiSelect-icon': {
                        color: isDarkMode ? '#e2e8f0' : '#4a5568',
                      },
                    }}
                  >
                    <MenuItem value="gt">Greater than</MenuItem>
                    <MenuItem value="gte">Greater than or equal</MenuItem>
                    <MenuItem value="lt">Less than</MenuItem>
                    <MenuItem value="lte">Less than or equal</MenuItem>
                    <MenuItem value="eq">Equal to</MenuItem>
                  </Select>
                </FormControl>
                <TextField
                  label="Review Count"
                  variant="outlined"
                  value={filters.reviewsCount}
                  onChange={handleReviewsChange}
                  size="small"
                  sx={{
                    flex: 1,
                    '& .MuiOutlinedInput-root': {
                      color: isDarkMode ? '#e2e8f0' : '#4a5568',
                      '& fieldset': {
                        borderColor: isDarkMode
                          ? 'rgba(255, 255, 255, 0.23)'
                          : 'rgba(0, 0, 0, 0.23)',
                      },
                      '&:hover fieldset': {
                        borderColor: isDarkMode
                          ? 'rgba(255, 255, 255, 0.4)'
                          : 'rgba(0, 0, 0, 0.4)',
                      },
                    },
                    '& .MuiInputLabel-root': {
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.7)'
                        : 'rgba(0, 0, 0, 0.6)',
                    },
                  }}
                  placeholder="Enter number of reviews"
                  type="text"
                  inputProps={{
                    inputMode: 'numeric',
                    pattern: '[0-9]*',
                  }}
                />
              </Box>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={filters.type === 'CLIENT'}
                    onChange={(e) =>
                      setFilters({
                        ...filters,
                        type: e.target.checked ? 'CLIENT' : '',
                      })
                    }
                    sx={{
                      color: isDarkMode ? '#90CAF9' : '#1976d2',
                      '&.Mui-checked': {
                        color: isDarkMode ? '#90CAF9' : '#1976d2',
                      },
                    }}
                  />
                }
                label="Clients"
                sx={{
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                }}
              />
            </Grid>
          </Grid>
          <Box
            sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2, gap: 2 }}
          >
            <Button
              variant="outlined"
              onClick={handleResetFilters}
              sx={{
                borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                color: isDarkMode ? '#90CAF9' : '#1976d2',
                '&:hover': {
                  borderColor: isDarkMode ? '#64B5F6' : '#1565C0',
                  backgroundColor: isDarkMode
                    ? 'rgba(144, 202, 249, 0.08)'
                    : 'rgba(25, 118, 210, 0.08)',
                },
                minWidth: '120px',
              }}
            >
              Reset Filters
            </Button>
            <Button
              variant="contained"
              onClick={handleApplyFilters}
              disabled={isFiltering}
              sx={{
                backgroundColor: isDarkMode ? '#90CAF9' : '#1976d2',
                '&:hover': {
                  backgroundColor: isDarkMode ? '#64B5F6' : '#1565C0',
                },
                minWidth: '120px',
              }}
            >
              {isFiltering ? (
                <CircularProgress size={24} color="inherit" />
              ) : (
                'Apply Filters'
              )}
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* ASIN Table */}
      <AsinTable
        asinsList={filteredAsinsData || []}
        isDarkMode={isDarkMode}
        setNotification={setNotification}
        pagination={pagination}
        onPageChange={handlePageChange}
        onRowsPerPageChange={handleRowsPerPageChange}
        filters={filters}
        getASINWithURLParams={getASINWithURLParams}
      />
    </Box>
  );
};

export default AsinTab;
