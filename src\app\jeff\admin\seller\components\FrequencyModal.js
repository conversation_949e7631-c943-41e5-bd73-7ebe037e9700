'use client';

import React from 'react';
import { Typography, Box, Button, TextField } from '@mui/material';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';

const FrequencyModal = (props) => {
  const {
    frequencyModalOpen,
    setFrequencyModalOpen,
    isDarkMode,
    useFilters,
    setUseFilters,
    selectedAsins,
    selectedFrequency,
    setSelectedFrequency,
    changeFrequencyMutation,
    handleChangeFrequency,
  } = props;

  return (
    <Dialog
      open={frequencyModalOpen}
      onClose={() => {
        setFrequencyModalOpen(false);
        setUseFilters(false);
      }}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
        },
      }}
    >
      <DialogTitle sx={{ color: isDarkMode ? '#fff' : 'inherit' }}>
        Change Run Frequency
      </DialogTitle>
      <DialogContent>
        <Box sx={{ mb: 2 }}>
          <Typography
            variant="body2"
            sx={{ color: isDarkMode ? '#e2e8f0' : '#4a5568', mb: 2 }}
          >
            {useFilters
              ? 'Update the run frequency for all filtered data.'
              : `Update the run frequency for ${selectedAsins.length} selected ASIN(s).`}
          </Typography>
        </Box>

        <Box sx={{ mb: 2 }}>
          <Typography
            variant="subtitle2"
            sx={{ color: isDarkMode ? '#fff' : 'inherit', mb: 1 }}
          >
            Selected ASINs:
          </Typography>
          <Box
            sx={{
              maxHeight: '200px',
              overflow: 'auto',
              p: 2,
              backgroundColor: isDarkMode
                ? 'rgba(255, 255, 255, 0.05)'
                : '#f5f5f5',
              borderRadius: '4px',
              border: `1px solid ${
                isDarkMode ? 'rgba(255, 255, 255, 0.12)' : '#e0e0e0'
              }`,
            }}
          >
            {useFilters ? (
              <Typography
                variant="body2"
                sx={{
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  fontStyle: 'italic',
                }}
              >
                Selected all filtered data
              </Typography>
            ) : (
              selectedAsins.map((item, index) => (
                <Typography
                  key={index}
                  variant="body2"
                  sx={{
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    mb: 0.5,
                    fontFamily: 'monospace',
                  }}
                >
                  {item.asin} ({item.countryCode})
                </Typography>
              ))
            )}
          </Box>
        </Box>

        <TextField
          fullWidth
          label="Run Frequency (days)"
          type="number"
          value={selectedFrequency}
          onChange={(e) => {
            const value = parseInt(e.target.value);
            if (value >= 0 && value <= 15) {
              setSelectedFrequency(value);
            }
          }}
          inputProps={{
            min: 0,
            max: 15,
            step: 1,
          }}
          helperText="Enter a number between 0 and 15 days"
          sx={{
            mb: 2,
            '& .MuiOutlinedInput-root': {
              backgroundColor: isDarkMode
                ? 'rgba(255, 255, 255, 0.05)'
                : 'inherit',
              color: isDarkMode ? '#fff' : 'inherit',
              '& fieldset': {
                borderColor: isDarkMode
                  ? 'rgba(255, 255, 255, 0.23)'
                  : 'inherit',
              },
              '&:hover fieldset': {
                borderColor: isDarkMode
                  ? 'rgba(255, 255, 255, 0.4)'
                  : 'inherit',
              },
              '&.Mui-focused fieldset': {
                borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
              },
            },
            '& .MuiInputLabel-root': {
              color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'inherit',
              '&.Mui-focused': {
                color: isDarkMode ? '#90CAF9' : '#1976d2',
              },
            },
            '& .MuiFormHelperText-root': {
              color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'inherit',
            },
          }}
        />
      </DialogContent>
      <DialogActions>
        <Button
          onClick={() => {
            setFrequencyModalOpen(false);
            setUseFilters(false);
          }}
          sx={{ color: isDarkMode ? '#e2e8f0' : '#4a5568' }}
        >
          Cancel
        </Button>
        <Button
          variant="contained"
          disabled={changeFrequencyMutation.isPending}
          onClick={async () => {
            await handleChangeFrequency(useFilters);
            setFrequencyModalOpen(false);
            setUseFilters(false);
          }}
          sx={{
            backgroundColor: isDarkMode ? '#7C3AED' : '#6D28D9',
            '&:hover': {
              backgroundColor: isDarkMode ? '#A78BFA' : '#8B5CF6',
            },
          }}
        >
          {changeFrequencyMutation.isPending
            ? 'Updating...'
            : 'Update Frequency'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default FrequencyModal;
