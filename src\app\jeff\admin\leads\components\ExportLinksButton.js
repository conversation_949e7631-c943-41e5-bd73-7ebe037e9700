'use client';

import React, { useState } from 'react';
import {
  IconButton,
  CircularProgress,
  Tooltip,
  Popover,
  Box,
  Typography,
  Switch,
  FormControlLabel,
  Button,
  Paper,
} from '@mui/material';
import LinkIcon from '@mui/icons-material/Link';
import InfoIcon from '@mui/icons-material/Info';

/**
 * Export Google Sheets Links Button Component
 * Provides functionality to export Google Sheets links with optional debug information
 */
const ExportLinksButton = ({
  jobId,
  onExportLinks,
  isLoading = false,
  isDarkMode = false,
  existingData = null,
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [includeDebug, setIncludeDebug] = useState(false);

  const handleInfoClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleExport = () => {
    // Always make API call to get fresh data, even if existing data is present
    onExportLinks(jobId, includeDebug);
    handleClose();
  };

  const open = Boolean(anchorEl);

  return (
    <>
      <Tooltip title="Export Google Sheets Links">
        <IconButton
          onClick={handleInfoClick}
          disabled={isLoading}
          size="small"
          sx={{
            color: isDarkMode ? '#68D391' : '#48BB78',
            '&:hover': {
              backgroundColor: isDarkMode
                ? 'rgba(104, 211, 145, 0.1)'
                : 'rgba(72, 187, 120, 0.1)',
            },
          }}
        >
          {isLoading ? (
            <CircularProgress size={20} color="inherit" />
          ) : (
            <LinkIcon fontSize="small" />
          )}
        </IconButton>
      </Tooltip>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
      >
        <Paper
          sx={{
            p: 2,
            maxWidth: 320,
            backgroundColor: isDarkMode ? '#2D3748' : '#FFFFFF',
            color: isDarkMode ? '#E2E8F0' : '#4A5568',
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <InfoIcon 
              fontSize="small" 
              sx={{ 
                mr: 1, 
                color: isDarkMode ? '#63B3ED' : '#3182CE' 
              }} 
            />
            <Typography variant="subtitle2" fontWeight={600}>
              Export Google Sheets Links
            </Typography>
          </Box>
          
          <Typography variant="body2" sx={{ mb: 2, lineHeight: 1.5 }}>
            This feature calls the Google API to generate fresh shareable links to the lead data in Google Sheets format. 
            The API processes the job data and creates organized spreadsheets for easy viewing and sharing.
            {existingData?.googleSheet?.url && (
              <> Previous export data is shown below for reference.</>
            )}
          </Typography>

          {existingData?.googleSheet?.url && (
            <Box sx={{ 
              mb: 2, 
              p: 1.5, 
              backgroundColor: isDarkMode ? 'rgba(104, 211, 145, 0.1)' : 'rgba(72, 187, 120, 0.1)',
              borderRadius: 1,
              border: `1px solid ${isDarkMode ? 'rgba(104, 211, 145, 0.3)' : 'rgba(72, 187, 120, 0.3)'}`
            }}>
              <Typography variant="body2" fontWeight={500} sx={{ mb: 0.5, color: isDarkMode ? '#68D391' : '#48BB78' }}>
                📊 Previous Export Data Available
              </Typography>
              <Typography variant="body2" fontSize="0.75rem">
                Last export: {existingData.googleSheet.sheets?.length || 0} sheets with {existingData.stats?.totalLeads || 0} leads
              </Typography>
              <Typography variant="body2" fontSize="0.7rem" sx={{ mt: 0.5, fontStyle: 'italic', color: isDarkMode ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)' }}>
                                 Clicking &quot;Export Links&quot; will generate fresh data
              </Typography>
            </Box>
          )}

          <FormControlLabel
            control={
              <Switch
                checked={includeDebug}
                onChange={(e) => setIncludeDebug(e.target.checked)}
                size="small"
                sx={{
                  '& .MuiSwitch-switchBase.Mui-checked': {
                    color: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                  '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                    backgroundColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                }}
              />
            }
            label={
              <Box>
                <Typography variant="body2" fontWeight={500}>
                  Include Debug Information
                </Typography>
                <Typography variant="body2" fontSize="0.75rem" color="text.secondary">
                  Adds technical details and processing logs to help understand the data generation process
                </Typography>
              </Box>
            }
            sx={{ mb: 2, alignItems: 'flex-start' }}
          />

          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
            <Button
              size="small"
              onClick={handleClose}
              sx={{ color: isDarkMode ? '#A0AEC0' : '#718096' }}
            >
              Cancel
            </Button>
            <Button
              size="small"
              variant="contained"
              onClick={handleExport}
              disabled={isLoading}
              sx={{
                backgroundColor: isDarkMode ? '#90CAF9' : '#1976d2',
                '&:hover': {
                  backgroundColor: isDarkMode ? '#63B3ED' : '#1565c0',
                },
              }}
            >
              {isLoading ? 'Generating...' : 'Export Links'}
            </Button>
          </Box>
        </Paper>
      </Popover>
    </>
  );
};

export default ExportLinksButton; 