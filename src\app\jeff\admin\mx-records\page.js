'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Container,
  Paper,
  Snackbar,
  Alert,
  Grid,
  Typography,
  Card,
  CardContent,
  CardHeader,
  Switch,
  FormControlLabel,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
} from '@mui/material';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import JeffAdminPanel from 'views/Jeff/JeffAdminPanel';
import { useUser } from 'views/Jeff/Utils/getUser';
import { useRouter } from 'next/navigation';
import { useAdminTheme } from 'views/Jeff/AdminThemeContext';

// Icons
import DnsIcon from '@mui/icons-material/Dns';
import EmailIcon from '@mui/icons-material/Email';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import WarningIcon from '@mui/icons-material/Warning';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import PauseIcon from '@mui/icons-material/Pause';
import RefreshIcon from '@mui/icons-material/Refresh';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ScheduleIcon from '@mui/icons-material/Schedule';
import CloudIcon from '@mui/icons-material/Cloud';
import SearchIcon from '@mui/icons-material/Search';
import HealthAndSafetyIcon from '@mui/icons-material/HealthAndSafety';
import InfoIcon from '@mui/icons-material/Info';

// API functions
import { getSellerBotAxiosInstance } from 'config/axios';

const mxAxios = getSellerBotAxiosInstance({
  cookiesKey: 'jeff-authorization',
});

const MXRecordsPage = () => {
  const { data: user, isLoading: isUserLoading } = useUser();
  const router = useRouter();
  const { mode: adminThemeMode } = useAdminTheme() || { mode: 'light' };
  const isDarkMode = adminThemeMode === 'dark';
  
  // Define theme based on dark mode
  const theme = createTheme({
    palette: {
      mode: isDarkMode ? 'dark' : 'light',
      primary: {
        main: isDarkMode ? '#90CAF9' : '#1976d2',
      },
      background: {
        default: isDarkMode ? '#1A202C' : '#F8FAFC',
        paper: isDarkMode ? '#2D3748' : '#FFFFFF',
      },
      text: {
        primary: isDarkMode ? '#E2E8F0' : '#4A5568',
        secondary: isDarkMode ? '#A0AEC0' : '#718096',
      },
    },
  });

  // State management
  const [stats, setStats] = useState(null);
  const [schedulerStatus, setSchedulerStatus] = useState(null);
  const [pendingRecords, setPendingRecords] = useState(null);
  const [healthStatus, setHealthStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [domainTestDialog, setDomainTestDialog] = useState({ open: false, domain: '', result: null });
  
  // Notifications
  const [snackBar, setSnackBar] = useState({
    isOpen: false,
    message: '',
    severity: 'success',
  });

  // Authentication check
  useEffect(() => {
    if (!isUserLoading && !user) {
      router.push('/jeff/login');
    } else if (!isUserLoading && user && user?.userType && user?.userType !== 'admin') {
      router.push('/jeff/dashboard');
    } else if (!isUserLoading && user && user?.userType && user?.userType === 'admin') {
      loadAllData();
    }
  }, [isUserLoading, user, router]);

  // Auto refresh
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(loadAllData, 30000); // Refresh every 30 seconds
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const loadAllData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchStats(),
        fetchSchedulerStatus(),
        fetchPendingRecords(),
        fetchHealthStatus(),
      ]);
    } catch (error) {
      console.error('Error loading data:', error);
      setSnackBar({
        isOpen: true,
        message: 'Failed to load MX Records data',
        severity: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await mxAxios.get('/mx/stats');
      setStats(response.data.data);
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const fetchSchedulerStatus = async () => {
    try {
      const response = await mxAxios.get('/mx/scheduler/status');
      setSchedulerStatus(response.data.data);
    } catch (error) {
      console.error('Error fetching scheduler status:', error);
    }
  };

  const fetchPendingRecords = async () => {
    try {
      const response = await mxAxios.get('/mx/pending');
      setPendingRecords(response.data.data);
    } catch (error) {
      console.error('Error fetching pending records:', error);
    }
  };

  const fetchHealthStatus = async () => {
    try {
      const response = await mxAxios.get('/mx/health');
      setHealthStatus(response.data.data);
    } catch (error) {
      console.error('Error fetching health status:', error);
    }
  };

  const handleSchedulerAction = async (action) => {
    setActionLoading(action);
    try {
      const response = await mxAxios.post(`/mx/scheduler/${action}`);
      setSnackBar({
        isOpen: true,
        message: response.data.message,
        severity: 'success',
      });
      await fetchSchedulerStatus();
    } catch (error) {
      console.error(`Error ${action}ing scheduler:`, error);
      setSnackBar({
        isOpen: true,
        message: `Failed to ${action} scheduler`,
        severity: 'error',
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleRunNow = async () => {
    setActionLoading('run');
    try {
      const response = await mxAxios.post('/mx/run');
      setSnackBar({
        isOpen: true,
        message: response.data.message,
        severity: 'success',
      });
      await loadAllData();
    } catch (error) {
      console.error('Error running MX check:', error);
      setSnackBar({
        isOpen: true,
        message: 'Failed to run MX check',
        severity: 'error',
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleDomainTest = async (domain) => {
    setDomainTestDialog({ ...domainTestDialog, result: 'loading' });
    try {
      const response = await mxAxios.post(`/mx/domain/${domain}`);
      setDomainTestDialog({ ...domainTestDialog, result: response.data.data });
    } catch (error) {
      console.error('Error testing domain:', error);
      setDomainTestDialog({ ...domainTestDialog, result: { error: error.message } });
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  if (loading) {
    return (
      <ThemeProvider theme={theme}>
        <JeffAdminPanel title="MX Records Management">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </JeffAdminPanel>
      </ThemeProvider>
    );
  }

  return (
    <ThemeProvider theme={theme}>
      <JeffAdminPanel title="MX Records Management">
        <Container maxWidth="xl" sx={{ py: 2 }}>
          {/* Header Controls */}
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h4" component="h1" sx={{ fontWeight: 600, color: 'text.primary' }}>
              MX Records Analytics
            </Typography>
            <Box display="flex" gap={2} alignItems="center">
              <FormControlLabel
                control={
                  <Switch
                    checked={autoRefresh}
                    onChange={(e) => setAutoRefresh(e.target.checked)}
                    color="primary"
                  />
                }
                label="Auto Refresh"
              />
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={loadAllData}
                disabled={loading}
              >
                Refresh
              </Button>
              <Button
                variant="contained"
                startIcon={<SearchIcon />}
                onClick={() => setDomainTestDialog({ open: true, domain: '', result: null })}
              >
                Test Domain
              </Button>
            </Box>
          </Box>

          {/* Stats Overview */}
          <Grid container spacing={3} mb={3}>
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center" gap={2}>
                    <EmailIcon color="primary" />
                    <Box>
                      <Typography variant="h4" fontWeight="bold">
                        {stats?.totalRecords?.toLocaleString() || '0'}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Total Records
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center" gap={2}>
                    <CheckCircleIcon color="success" />
                    <Box>
                      <Typography variant="h4" fontWeight="bold">
                        {stats?.totalProcessed?.toLocaleString() || '0'}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Processed
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center" gap={2}>
                    <WarningIcon color="warning" />
                    <Box>
                      <Typography variant="h4" fontWeight="bold">
                        {stats?.totalPending?.toLocaleString() || '0'}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Pending
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center" gap={2}>
                    <DnsIcon color="info" />
                    <Box>
                      <Typography variant="h4" fontWeight="bold">
                        {pendingRecords?.summary?.uniqueDomains?.toLocaleString() || '0'}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Unique Domains
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Progress Bar */}
          {stats && (
            <Paper sx={{ p: 2, mb: 3 }}>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                <Typography variant="h6">Processing Progress</Typography>
                <Typography variant="body2" color="textSecondary">
                  {stats.processedPercentage}%
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={parseFloat(stats.processedPercentage) || 0}
                sx={{ height: 8, borderRadius: 4 }}
              />
              <Box display="flex" justifyContent="space-between" mt={1}>
                <Typography variant="caption" color="textSecondary">
                  Processed: {stats.totalProcessed?.toLocaleString() || '0'}
                </Typography>
                <Typography variant="caption" color="textSecondary">
                  Total: {stats.totalRecords?.toLocaleString() || '0'}
                </Typography>
              </Box>
            </Paper>
          )}

          {/* Breakdown by Table Type */}
          {stats?.breakdown && (
            <Grid container spacing={3} mb={3}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardHeader
                    title="Prospects Table"
                    avatar={<EmailIcon />}
                  />
                  <CardContent>
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <Typography variant="h6" color="primary">
                          {stats.breakdown.prospects.total.toLocaleString()}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          Total Records
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="h6" color="success.main">
                          {stats.breakdown.prospects.processed.toLocaleString()}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          Processed
                        </Typography>
                      </Grid>
                      <Grid item xs={12}>
                        <Typography variant="h6" color="warning.main">
                          {stats.breakdown.prospects.pending.toLocaleString()}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          Pending Processing
                        </Typography>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card>
                  <CardHeader
                    title="Amazon Prospects Table"
                    avatar={<EmailIcon />}
                  />
                  <CardContent>
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <Typography variant="h6" color="primary">
                          {stats.breakdown.amazonProspects.total.toLocaleString()}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          Total Records
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="h6" color="success.main">
                          {stats.breakdown.amazonProspects.processed.toLocaleString()}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          Processed
                        </Typography>
                      </Grid>
                      <Grid item xs={12}>
                        <Typography variant="h6" color="warning.main">
                          {stats.breakdown.amazonProspects.pending.toLocaleString()}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          Pending Processing
                        </Typography>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}

          {/* Scheduler Status & Controls */}
          <Grid container spacing={3} mb={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader
                  title="Scheduler Status"
                  avatar={<ScheduleIcon />}
                  action={
                    <Chip
                      label={schedulerStatus?.enabled ? 'Enabled' : 'Disabled'}
                      color={schedulerStatus?.enabled ? 'success' : 'error'}
                      size="small"
                    />
                  }
                />
                <CardContent>
                  <Box display="flex" flexDirection="column" gap={2}>
                    <Box display="flex" justifyContent="space-between">
                      <Typography variant="body2">Environment:</Typography>
                      <Chip label={schedulerStatus?.nodeEnv || 'Unknown'} size="small" />
                    </Box>
                    <Box display="flex" justifyContent="space-between">
                      <Typography variant="body2">Status:</Typography>
                      <Chip
                        label={schedulerStatus?.isRunning ? 'Running' : 'Idle'}
                        color={schedulerStatus?.isRunning ? 'warning' : 'success'}
                        size="small"
                      />
                    </Box>
                    <Box display="flex" justifyContent="space-between">
                      <Typography variant="body2">Last Run:</Typography>
                      <Typography variant="body2" color="textSecondary">
                        {formatDate(schedulerStatus?.lastRunTime)}
                      </Typography>
                    </Box>
                    <Box display="flex" justifyContent="space-between">
                      <Typography variant="body2">Next Run:</Typography>
                      <Typography variant="body2" color="textSecondary">
                        {formatDate(schedulerStatus?.nextRunTime)}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader
                  title="Scheduler Controls"
                  avatar={<PlayArrowIcon />}
                />
                <CardContent>
                  <Box display="flex" flexDirection="column" gap={2}>
                    <Box display="flex" gap={2}>
                      <Button
                        variant="outlined"
                        startIcon={schedulerStatus?.isScheduled ? <PauseIcon /> : <PlayArrowIcon />}
                        onClick={() => handleSchedulerAction(schedulerStatus?.isScheduled ? 'stop' : 'start')}
                        disabled={actionLoading === 'start' || actionLoading === 'stop'}
                        fullWidth
                      >
                        {schedulerStatus?.isScheduled ? 'Stop' : 'Start'} Scheduler
                      </Button>
                      <Button
                        variant="contained"
                        startIcon={<RefreshIcon />}
                        onClick={handleRunNow}
                        disabled={actionLoading === 'run' || schedulerStatus?.isRunning}
                        fullWidth
                      >
                        Run Now
                      </Button>
                    </Box>
                    {schedulerStatus?.stats && (
                      <Box>
                        <Typography variant="body2" color="textSecondary" gutterBottom>
                          Run Statistics:
                        </Typography>
                        <Grid container spacing={1}>
                          <Grid item xs={4}>
                            <Typography variant="caption">Total: {schedulerStatus.stats.totalRuns}</Typography>
                          </Grid>
                          <Grid item xs={4}>
                            <Typography variant="caption" color="success.main">
                              Success: {schedulerStatus.stats.successfulRuns}
                            </Typography>
                          </Grid>
                          <Grid item xs={4}>
                            <Typography variant="caption" color="error.main">
                              Failed: {schedulerStatus.stats.failedRuns}
                            </Typography>
                          </Grid>
                        </Grid>
                      </Box>
                    )}
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Health Status */}
          {healthStatus && (
            <Paper sx={{ p: 2, mb: 3 }}>
              <Box display="flex" alignItems="center" gap={2} mb={2}>
                <HealthAndSafetyIcon color={healthStatus.healthy ? 'success' : 'error'} />
                <Typography variant="h6">
                  Health Status: {healthStatus.healthy ? 'Healthy' : 'Unhealthy'}
                </Typography>
              </Box>
              <Typography variant="body2" color="textSecondary">
                Last health check: {formatDate(healthStatus.status?.lastRunTime)}
              </Typography>
            </Paper>
          )}

          {/* Email Provider Distribution */}
          {stats?.emailProviders && (
            <Paper sx={{ p: 2, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Email Provider Distribution
              </Typography>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Provider</TableCell>
                      <TableCell align="right">Count</TableCell>
                      <TableCell align="right">Percentage</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {stats.emailProviders
                      .sort((a, b) => b._count - a._count) // Sort by count descending
                      .slice(0, 20) // Show top 20 providers
                      .map((provider, index) => {
                        const percentage = stats.totalRecords > 0 
                          ? ((provider._count / stats.totalRecords) * 100).toFixed(2)
                          : '0.00';
                        
                        return (
                          <TableRow key={index}>
                            <TableCell>
                              <Box display="flex" alignItems="center" gap={1}>
                                <CloudIcon fontSize="small" />
                                <Box>
                                  <Typography variant="body2">
                                    {provider.emailProvider || 'Unknown'}
                                  </Typography>
                                  {provider.emailProvider === 'Pending' && (
                                    <Typography variant="caption" color="textSecondary">
                                      (Not yet processed)
                                    </Typography>
                                  )}
                                </Box>
                              </Box>
                            </TableCell>
                            <TableCell align="right">
                              <Typography variant="body2" fontWeight="medium">
                                {provider._count.toLocaleString()}
                              </Typography>
                            </TableCell>
                            <TableCell align="right">
                              <Typography variant="body2" fontWeight="medium">
                                {percentage}%
                              </Typography>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                  </TableBody>
                </Table>
              </TableContainer>
              {stats.emailProviders.length > 20 && (
                <Typography variant="caption" color="textSecondary" sx={{ mt: 1, display: 'block' }}>
                  Showing top 20 providers out of {stats.emailProviders.length} total
                </Typography>
              )}
            </Paper>
          )}

          {/* Pending Records Summary */}
          {pendingRecords && (
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="h6">
                  Pending Records Summary ({pendingRecords.summary.totalProspects} records)
                </Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle1" gutterBottom>
                      By Table Type:
                    </Typography>
                    <List>
                      {Object.entries(pendingRecords.summary.byTableType || {}).map(([tableType, count]) => (
                        <ListItem key={tableType}>
                          <ListItemIcon>
                            <InfoIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary={tableType}
                            secondary={`${count.toLocaleString()} records`}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle1" gutterBottom>
                      Summary:
                    </Typography>
                    <List>
                      <ListItem>
                        <ListItemIcon>
                          <DnsIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="Unique Domains"
                          secondary={pendingRecords.summary.uniqueDomains.toLocaleString()}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <EmailIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="Total Prospects"
                          secondary={pendingRecords.summary.totalProspects.toLocaleString()}
                        />
                      </ListItem>
                    </List>
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>
          )}
        </Container>

        {/* Domain Test Dialog */}
        <Dialog
          open={domainTestDialog.open}
          onClose={() => setDomainTestDialog({ open: false, domain: '', result: null })}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>Test Domain MX Records</DialogTitle>
          <DialogContent>
            <TextField
              autoFocus
              margin="dense"
              label="Domain Name"
              fullWidth
              value={domainTestDialog.domain}
              onChange={(e) => setDomainTestDialog({ ...domainTestDialog, domain: e.target.value })}
              placeholder="example.com"
            />
            {domainTestDialog.result && (
              <Box mt={2}>
                {domainTestDialog.result === 'loading' ? (
                  <CircularProgress />
                ) : domainTestDialog.result.error ? (
                  <Alert severity="error">{domainTestDialog.result.error}</Alert>
                ) : (
                  <Box>
                    <Typography variant="h6" gutterBottom>
                      Results:
                    </Typography>
                    <Typography variant="body2">
                      <strong>Provider:</strong> {domainTestDialog.result.provider || 'Unknown'}
                    </Typography>
                    <Typography variant="body2">
                      <strong>MX Records:</strong>
                    </Typography>
                    <pre style={{ background: isDarkMode ? '#2D3748' : '#F7FAFC', padding: 8, borderRadius: 4 }}>
                      {JSON.stringify(domainTestDialog.result.mxRecords, null, 2)}
                    </pre>
                  </Box>
                )}
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDomainTestDialog({ open: false, domain: '', result: null })}>
              Cancel
            </Button>
            <Button
              onClick={() => handleDomainTest(domainTestDialog.domain)}
              disabled={!domainTestDialog.domain || domainTestDialog.result === 'loading'}
              variant="contained"
            >
              Test
            </Button>
          </DialogActions>
        </Dialog>

        {/* Snackbar for notifications */}
        <Snackbar
          open={snackBar.isOpen}
          autoHideDuration={6000}
          onClose={() => setSnackBar({ ...snackBar, isOpen: false })}
          anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <Alert onClose={() => setSnackBar({ ...snackBar, isOpen: false })} severity={snackBar.severity}>
            {snackBar.message}
          </Alert>
        </Snackbar>
      </JeffAdminPanel>
    </ThemeProvider>
  );
};

export default MXRecordsPage; 