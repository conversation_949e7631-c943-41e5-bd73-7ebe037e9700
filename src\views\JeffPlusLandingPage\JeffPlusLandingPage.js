'use client'; // This is a client component 👈🏽

import React from 'react';
import Box from '@mui/material/Box';
import { alpha } from '@mui/material/styles';
import { colors } from '@mui/material';
import { usePostHog } from 'posthog-js/react';
import { usePathname, useSearchParams } from 'next/navigation';

import { CTAStrip } from './components';
import EqualMain from 'layouts/EqualMain';
import Container from 'components/Container';
import SalesAssistantContent from '../../content/salesAssistant';
import Hero from './components/Hero';
import WhatWillJeffDo from './components/WhatWillJeffDo';
import JeffSignals from './components/JeffSignals';
import WeekWisePlan from './components/WeekWisePlan';
import FAQs from './components/FAQs';
import Customers from './components/Customers';
import JeffCaseStudies from './components/JeffCaseStudies/JeffCaseStudies';
import { ARYAN_SCHEDULING_LINK, GAURAV_SCHEDULING_LINK } from 'content/jeff';

const BACKGROUND_COLOR = alpha(colors.blue[50], 0.15);

const JeffLandingPage = () => {
  const posthog = usePostHog();
  const pathname = usePathname();
  const schedulingLink = pathname.includes('jeffGaurav')
    ? GAURAV_SCHEDULING_LINK
    : ARYAN_SCHEDULING_LINK;

  const searchParams = useSearchParams();
  const prospectIdentifier = searchParams.get('c') || 'unknown';

  return (
    <EqualMain>
      {/* Hero Section */}
      <Box
        position={'relative'}
        sx={{
          backgroundColor: BACKGROUND_COLOR,
          marginTop: -13,
          paddingTop: 13,
        }}
      >
        <Container paddingY={2}>
          <Hero schedulingLink={schedulingLink} />
        </Container>
      </Box>

      {/* Customers */}
      <Customers
        styleProps={{
          paddingTop: 2,
          paddingBottom: 4,
          fontSize: '1.6rem',
          fontWeight: 600,
          textAlign: 'center',
        }}
        text="Bookings 10s of meetings every week for them and many more..."
      />

      <Container paddingY={2} paddingTop={6} marginTop={4}>
        <WhatWillJeffDo />
      </Container>

      <JeffCaseStudies />

      <Container paddingTop={2} paddingBottom={3}>
        <JeffSignals />
      </Container>

      <Box bgcolor={BACKGROUND_COLOR}>
        <Container paddingY={'0 !important'} paddingX={'0 !important'}>
          <CTAStrip
            stripText={
              'Trained on your company data: Made to talk & sell just like you.'
            }
            ctaOnClick={() => {
              posthog?.capture('book_call_trained_on_company', {
                prospectIdentifier: prospectIdentifier,
              });
            }}
            ctaLink={schedulingLink}
            ctaText={SalesAssistantContent.HERO_SECTION.ctaText}
          />
        </Container>
      </Box>

      <Container paddingY={3}>
        <WeekWisePlan />
      </Container>

      <Container paddingY={3}>
        <FAQs />
      </Container>
      <Box bgcolor={BACKGROUND_COLOR}>
        <Container paddingY={'0 !important'} paddingX={'0 !important'}>
          <CTAStrip
            stripText={
              'Our past clients have experienced 10-15 meetings per month, highlighting Jeff\'s exceptional ROI.'
            }
            ctaLink={schedulingLink}
            ctaOnClick={() => {
              posthog?.capture('book_call_past_clients', {
                prospectIdentifier: prospectIdentifier,
              });
            }}
            ctaText={SalesAssistantContent.HERO_SECTION.ctaText}
          />
        </Container>
      </Box>
    </EqualMain>
  );
};

export default JeffLandingPage;
