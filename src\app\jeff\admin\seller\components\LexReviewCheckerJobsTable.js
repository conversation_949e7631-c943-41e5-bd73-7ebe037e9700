'use client';

import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Box,
  Chip,
  Card,
  CardContent,
  TablePagination,
  CircularProgress,
  IconButton,
  Tooltip,
} from '@mui/material';
import { useQuery, useMutation } from '@tanstack/react-query';
import { getAxiosInstance } from 'config/axios';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import RefreshIcon from '@mui/icons-material/Refresh';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import Button from '@mui/material/Button';
import PendingIcon from '@mui/icons-material/Schedule';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import AnalyticsIcon from '@mui/icons-material/Analytics';

const getStatusColor = (status, isDarkMode) => {
  switch (status?.toLowerCase()) {
    case 'pending':
      return {
        bg: isDarkMode ? 'rgba(144, 202, 249, 0.16)' : 'rgba(25, 118, 210, 0.08)',
        text: isDarkMode ? '#90CAF9' : '#1976d2',
      };
    case 'processing':
      return {
        bg: isDarkMode ? 'rgba(255, 193, 7, 0.16)' : 'rgba(237, 108, 2, 0.08)',
        text: isDarkMode ? '#FFC107' : '#ED6C02',
      };
    case 'completed':
      return {
        bg: isDarkMode ? 'rgba(129, 199, 132, 0.16)' : 'rgba(46, 125, 50, 0.08)',
        text: isDarkMode ? '#81C784' : '#2E7D32',
      };
    case 'failed':
      return {
        bg: isDarkMode ? 'rgba(239, 83, 80, 0.16)' : 'rgba(211, 47, 47, 0.08)',
        text: isDarkMode ? '#EF5350' : '#D32F2F',
      };
    case 'present':
      return {
        bg: isDarkMode ? 'rgba(76, 175, 80, 0.16)' : 'rgba(76, 175, 80, 0.08)',
        text: isDarkMode ? '#4CAF50' : '#4CAF50',
      };
    case 'removed':
      return {
        bg: isDarkMode ? 'rgba(244, 67, 54, 0.16)' : 'rgba(244, 67, 54, 0.08)',
        text: isDarkMode ? '#F44336' : '#F44336',
      };
    case 'resurrected':
      return {
        bg: isDarkMode ? 'rgba(156, 39, 176, 0.16)' : 'rgba(156, 39, 176, 0.08)',
        text: isDarkMode ? '#9C27B0' : '#9C27B0',
      };
    case 'error':
      return {
        bg: isDarkMode ? 'rgba(255, 152, 0, 0.16)' : 'rgba(255, 152, 0, 0.08)',
        text: isDarkMode ? '#FF9800' : '#FF9800',
      };
    default:
      return {
        bg: isDarkMode ? 'rgba(158, 158, 158, 0.16)' : 'rgba(158, 158, 158, 0.08)',
        text: isDarkMode ? '#9E9E9E' : '#9E9E9E',
      };
  }
};

const getStatusLabel = (status) => {
  return status?.replace(/_/g, ' ') || 'Unknown';
};

const LexReviewCheckerJobsTable = ({ isDarkMode, setNotification }) => {
  const [page, setPage] = useState(0);
  const [limit, setLimit] = useState(10);
  const [sortConfig, setSortConfig] = useState({
    key: 'createdAt',
    direction: 'desc',
  });

  // React Query for checker jobs
  const {
    data: jobsData,
    isLoading: jobsLoading,
    refetch: refetchJobs,
  } = useQuery({
    queryKey: ['lex-review-checker-jobs', page, limit],
    queryFn: async () => {
      const params = {
        page: page + 1,
        pageSize: limit,
      };

      const response = await getAxiosInstance({
        cookiesKey: 'jeff-authorization',
      }).get('/api/lex/review/checker/jobs', { params });

      return {
        jobs: response.data?.data?.jobs || [],
        total: response.data?.data?.pagination?.totalJobs || 0,
        totalPages: response.data?.data?.pagination?.totalPages || 0,
      };
    },
    staleTime: 30000, // 30 seconds
    onError: () => {
      setNotification({
        open: true,
        message: 'Failed to fetch checker jobs',
        severity: 'error',
      });
    },
  });

  // Mutation for downloading individual job data
  const downloadJobMutation = useMutation({
    mutationFn: async (jobId) => {
      const response = await getAxiosInstance({
        cookiesKey: 'jeff-authorization',
      }).get(`/api/lex/review/checker/jobs/${jobId}/download`, {
        responseType: 'blob',
      });

      return response.data;
    },
    onSuccess: (blob, jobId) => {
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.setAttribute('download', `lex_checker_job_${jobId}.csv`);
      document.body.appendChild(link);
      link.click();
      link.remove();

      setNotification({
        open: true,
        message: `Downloading job ${jobId} data...`,
        severity: 'success',
      });
    },
    onError: (error) => {
      setNotification({
        open: true,
        message: error.response?.data?.error || 'Failed to download job data',
        severity: 'error',
      });
    },
  });

  // Mutation for downloading all jobs data
  const downloadAllJobsMutation = useMutation({
    mutationFn: async () => {
      const response = await getAxiosInstance({
        cookiesKey: 'jeff-authorization',
      }).get('/api/lex/review/checker/jobs/download/all', {
        responseType: 'blob',
      });

      return response.data;
    },
    onSuccess: (blob) => {
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.setAttribute('download', 'all_lex_checker_jobs.csv');
      document.body.appendChild(link);
      link.click();
      link.remove();

      setNotification({
        open: true,
        message: 'Downloading all jobs data...',
        severity: 'success',
      });
    },
    onError: (error) => {
      setNotification({
        open: true,
        message: error.response?.data?.error || 'Failed to download all jobs data',
        severity: 'error',
      });
    },
  });

  const handleSort = (key) => {
    const newSortConfig = {
      key,
      direction:
                sortConfig.key === key && sortConfig.direction === 'asc'
                  ? 'desc'
                  : 'asc',
    };
    setSortConfig(newSortConfig);
  };

  const getSortedData = (data) => {
    if (!sortConfig.key) return data;

    return [...data].sort((a, b) => {
      let aValue = a[sortConfig.key];
      let bValue = b[sortConfig.key];

      // Handle date sorting
      if (sortConfig.key === 'createdAt' || sortConfig.key === 'updatedAt') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      // Handle numeric sorting
      if (sortConfig.key === 'id' || sortConfig.key === 'totalReviews') {
        aValue = Number(aValue) || 0;
        bValue = Number(bValue) || 0;
      }

      // Handle string sorting
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  };

  const SortableTableCell = ({ label, sortKey, width }) => (
    <TableCell
      onClick={() => handleSort(sortKey)}
      sx={{
        backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
        color: isDarkMode ? '#e2e8f0' : '#4a5568',
        fontWeight: 700,
        fontSize: '0.875rem',
        textTransform: 'uppercase',
        letterSpacing: '0.5px',
        borderBottom: `2px solid ${isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}`,
        cursor: 'pointer',
        width: width,
        py: 2,
        px: 2,
        transition: 'all 0.2s ease-in-out',
        '&:hover': {
          backgroundColor: isDarkMode ? 'rgba(144, 202, 249, 0.1)' : 'rgba(25, 118, 210, 0.05)',
          transform: 'translateY(-1px)',
        },
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
        {label}
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            ml: 0.5,
            height: '20px',
          }}
        >
          <ArrowUpwardIcon
            sx={{
              fontSize: '0.875rem',
              color:
                                sortConfig.key === sortKey && sortConfig.direction === 'asc'
                                  ? isDarkMode
                                    ? '#90CAF9'
                                    : '#1976d2'
                                  : isDarkMode
                                    ? 'rgba(255, 255, 255, 0.3)'
                                    : 'rgba(0, 0, 0, 0.3)',
              height: '10px',
              lineHeight: '10px',
            }}
          />
          <ArrowDownwardIcon
            sx={{
              fontSize: '0.875rem',
              color:
                                sortConfig.key === sortKey && sortConfig.direction === 'desc'
                                  ? isDarkMode
                                    ? '#90CAF9'
                                    : '#1976d2'
                                  : isDarkMode
                                    ? 'rgba(255, 255, 255, 0.3)'
                                    : 'rgba(0, 0, 0, 0.3)',
              height: '10px',
              lineHeight: '10px',
            }}
          />
        </Box>
      </Box>
    </TableCell>
  );

  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const handleRowsPerPageChange = (event) => {
    const newLimit = parseInt(event.target.value, 10);
    setLimit(newLimit);
    setPage(0);
  };

  // Extract data from React Query
  const jobs = jobsData?.jobs || [];
  const total = jobsData?.total || 0;
  const totalPages = jobsData?.totalPages || 0;
  const stats = jobsData?.stats || {};
  const sortedJobs = getSortedData(jobs);

  return (
    <Card
      sx={{
        backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        borderRadius: '12px',
        overflow: 'hidden',
        mb: 2,
      }}
    >
      <CardContent sx={{ p: 3 }}>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 3,
          }}
        >
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              color: isDarkMode ? '#fff' : 'inherit',
            }}
          >
                        Lex Review Checker Jobs
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <Button
              variant="contained"
              startIcon={<FileDownloadIcon />}
              onClick={() => downloadAllJobsMutation.mutate()}
              disabled={downloadAllJobsMutation.isPending || jobs.length === 0}
              sx={{
                backgroundColor: isDarkMode ? '#90CAF9' : '#1976d2',
                '&:hover': {
                  backgroundColor: isDarkMode ? '#64B5F6' : '#1565C0',
                },
              }}
            >
              {downloadAllJobsMutation.isPending ? 'Downloading...' : 'Download All'}
            </Button>
            <Tooltip title="Refresh">
              <IconButton
                onClick={() => refetchJobs()}
                disabled={jobsLoading}
                sx={{
                  color: isDarkMode ? '#90CAF9' : '#1976d2',
                  '&:hover': {
                    backgroundColor: isDarkMode
                      ? 'rgba(144, 202, 249, 0.08)'
                      : 'rgba(25, 118, 210, 0.08)',
                  },
                }}
              >
                {jobsLoading ? <CircularProgress size={20} /> : <RefreshIcon />}
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Modern Stats Dashboard */}
        {stats && Object.keys(stats).length > 0 && (
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)', md: 'repeat(4, 1fr)' },
              gap: 3,
              mb: 4,
            }}
          >
            {/* Pending Reviews Card */}
            <Card
              sx={{
                background: isDarkMode
                  ? 'linear-gradient(135deg, rgba(144, 202, 249, 0.1) 0%, rgba(144, 202, 249, 0.05) 100%)'
                  : 'linear-gradient(135deg, rgba(25, 118, 210, 0.08) 0%, rgba(25, 118, 210, 0.04) 100%)',
                border: `1px solid ${isDarkMode ? 'rgba(144, 202, 249, 0.2)' : 'rgba(25, 118, 210, 0.2)'}`,
                borderRadius: '16px',
                p: 3,
                position: 'relative',
                overflow: 'hidden',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '4px',
                  background: isDarkMode ? '#90CAF9' : '#1976d2',
                },
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Box
                  sx={{
                    width: 48,
                    height: 48,
                    borderRadius: '12px',
                    backgroundColor: isDarkMode ? 'rgba(144, 202, 249, 0.2)' : 'rgba(25, 118, 210, 0.2)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <PendingIcon
                    sx={{
                      color: isDarkMode ? '#90CAF9' : '#1976d2',
                      fontSize: 24
                    }}
                  />
                </Box>
                <Typography
                  variant="h4"
                  sx={{
                    fontWeight: 700,
                    color: isDarkMode ? '#90CAF9' : '#1976d2',
                    fontSize: '2rem',
                  }}
                >
                  {stats.pendingReviews || 0}
                </Typography>
              </Box>
              <Typography
                variant="h6"
                sx={{
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  fontWeight: 600,
                  mb: 1,
                }}
              >
                                Pending Reviews
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)',
                  fontSize: '0.875rem',
                }}
              >
                                Awaiting processing
              </Typography>
            </Card>

            {/* Completed Reviews Card */}
            <Card
              sx={{
                background: isDarkMode
                  ? 'linear-gradient(135deg, rgba(129, 199, 132, 0.1) 0%, rgba(129, 199, 132, 0.05) 100%)'
                  : 'linear-gradient(135deg, rgba(46, 125, 50, 0.08) 0%, rgba(46, 125, 50, 0.04) 100%)',
                border: `1px solid ${isDarkMode ? 'rgba(129, 199, 132, 0.2)' : 'rgba(46, 125, 50, 0.2)'}`,
                borderRadius: '16px',
                p: 3,
                position: 'relative',
                overflow: 'hidden',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '4px',
                  background: isDarkMode ? '#81C784' : '#2E7D32',
                },
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Box
                  sx={{
                    width: 48,
                    height: 48,
                    borderRadius: '12px',
                    backgroundColor: isDarkMode ? 'rgba(129, 199, 132, 0.2)' : 'rgba(46, 125, 50, 0.2)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <CheckCircleIcon
                    sx={{
                      color: isDarkMode ? '#81C784' : '#2E7D32',
                      fontSize: 24
                    }}
                  />
                </Box>
                <Typography
                  variant="h4"
                  sx={{
                    fontWeight: 700,
                    color: isDarkMode ? '#81C784' : '#2E7D32',
                    fontSize: '2rem',
                  }}
                >
                  {stats.completedReviews || 0}
                </Typography>
              </Box>
              <Typography
                variant="h6"
                sx={{
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  fontWeight: 600,
                  mb: 1,
                }}
              >
                                Completed Reviews
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)',
                  fontSize: '0.875rem',
                }}
              >
                                Successfully processed
              </Typography>
            </Card>

            {/* Failed Reviews Card */}
            <Card
              sx={{
                background: isDarkMode
                  ? 'linear-gradient(135deg, rgba(239, 83, 80, 0.1) 0%, rgba(239, 83, 80, 0.05) 100%)'
                  : 'linear-gradient(135deg, rgba(211, 47, 47, 0.08) 0%, rgba(211, 47, 47, 0.04) 100%)',
                border: `1px solid ${isDarkMode ? 'rgba(239, 83, 80, 0.2)' : 'rgba(211, 47, 47, 0.2)'}`,
                borderRadius: '16px',
                p: 3,
                position: 'relative',
                overflow: 'hidden',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '4px',
                  background: isDarkMode ? '#EF5350' : '#D32F2F',
                },
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Box
                  sx={{
                    width: 48,
                    height: 48,
                    borderRadius: '12px',
                    backgroundColor: isDarkMode ? 'rgba(239, 83, 80, 0.2)' : 'rgba(211, 47, 47, 0.2)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <ErrorIcon
                    sx={{
                      color: isDarkMode ? '#EF5350' : '#D32F2F',
                      fontSize: 24
                    }}
                  />
                </Box>
                <Typography
                  variant="h4"
                  sx={{
                    fontWeight: 700,
                    color: isDarkMode ? '#EF5350' : '#D32F2F',
                    fontSize: '2rem',
                  }}
                >
                  {stats.failedReviews || 0}
                </Typography>
              </Box>
              <Typography
                variant="h6"
                sx={{
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  fontWeight: 600,
                  mb: 1,
                }}
              >
                                Failed Reviews
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)',
                  fontSize: '0.875rem',
                }}
              >
                                Processing errors
              </Typography>
            </Card>

            {/* Total Reviews Card */}
            <Card
              sx={{
                background: isDarkMode
                  ? 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)'
                  : 'linear-gradient(135deg, rgba(0, 0, 0, 0.08) 0%, rgba(0, 0, 0, 0.04) 100%)',
                border: `1px solid ${isDarkMode ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)'}`,
                borderRadius: '16px',
                p: 3,
                position: 'relative',
                overflow: 'hidden',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '4px',
                  background: isDarkMode ? '#FFFFFF' : '#000000',
                },
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Box
                  sx={{
                    width: 48,
                    height: 48,
                    borderRadius: '12px',
                    backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <AnalyticsIcon
                    sx={{
                      color: isDarkMode ? '#FFFFFF' : '#000000',
                      fontSize: 24
                    }}
                  />
                </Box>
                <Typography
                  variant="h4"
                  sx={{
                    fontWeight: 700,
                    color: isDarkMode ? '#FFFFFF' : '#000000',
                    fontSize: '2rem',
                  }}
                >
                  {stats.totalReviews || 0}
                </Typography>
              </Box>
              <Typography
                variant="h6"
                sx={{
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  fontWeight: 600,
                  mb: 1,
                }}
              >
                                Total Reviews
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)',
                  fontSize: '0.875rem',
                }}
              >
                                All processed reviews
              </Typography>
            </Card>
          </Box>
        )}

        <TableContainer
          sx={{
            borderRadius: '16px',
            overflow: 'hidden',
            border: `1px solid ${isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}`,
            boxShadow: isDarkMode
              ? '0 4px 20px rgba(0, 0, 0, 0.3)'
              : '0 4px 20px rgba(0, 0, 0, 0.1)',
          }}
        >
          <Table sx={{ borderCollapse: 'separate', borderSpacing: 0 }}>
            <TableHead>
              <TableRow>
                <SortableTableCell label="Job ID" sortKey="id" width="100px" />
                <SortableTableCell label="Job Name" sortKey="name" />
                <SortableTableCell label="Status" sortKey="status" width="150px" />
                <SortableTableCell label="Total Reviews" sortKey="totalReviews" width="120px" />
                <SortableTableCell label="Status Breakdown" sortKey="statusBreakdown" width="200px" />
                <SortableTableCell label="Created At" sortKey="createdAt" width="180px" />
                <SortableTableCell label="Updated At" sortKey="updatedAt" width="180px" />
                <TableCell
                  sx={{
                    backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    fontWeight: 700,
                    fontSize: '0.875rem',
                    textTransform: 'uppercase',
                    letterSpacing: '0.5px',
                    borderBottom: `2px solid ${isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}`,
                    width: '100px',
                    py: 2,
                    px: 2,
                  }}
                >
                                    Actions
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {jobsLoading ? (
                <TableRow>
                  <TableCell colSpan={8} align="center">
                    <CircularProgress size={24} />
                  </TableCell>
                </TableRow>
              ) : sortedJobs.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} align="center">
                    <Typography
                      variant="body2"
                      sx={{ color: isDarkMode ? '#fff' : 'inherit' }}
                    >
                                            No checker jobs found.
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                sortedJobs.map((job) => {
                  const statusColors = getStatusColor(job.status, isDarkMode);

                  return (
                    <TableRow
                      key={job.id}
                      sx={{
                        backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
                        transition: 'all 0.2s ease-in-out',
                        '&:hover': {
                          backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
                          transform: 'scale(1.01)',
                          boxShadow: isDarkMode
                            ? '0 8px 25px rgba(0, 0, 0, 0.3)'
                            : '0 8px 25px rgba(0, 0, 0, 0.1)',
                        },
                        '&:not(:last-child)': {
                          borderBottom: `1px solid ${isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)'}`,
                        },
                      }}
                    >
                      <TableCell
                        sx={{
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          fontWeight: 600,
                          fontSize: '0.875rem',
                          py: 2.5,
                          px: 2,
                        }}
                      >
                        <Box
                          sx={{
                            display: 'inline-flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: 32,
                            height: 32,
                            borderRadius: '8px',
                            backgroundColor: isDarkMode ? 'rgba(144, 202, 249, 0.1)' : 'rgba(25, 118, 210, 0.1)',
                            color: isDarkMode ? '#90CAF9' : '#1976d2',
                            fontWeight: 700,
                            fontSize: '0.875rem',
                          }}
                        >
                          {job.id}
                        </Box>
                      </TableCell>
                      <TableCell
                        sx={{
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          borderBottom: `1px solid ${isDarkMode ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)'
                          }`,
                        }}
                      >
                        <Tooltip
                          title={job.name}
                          placement="top"
                          arrow
                          componentsProps={{
                            tooltip: {
                              sx: {
                                bgcolor: isDarkMode ? '#2d3748' : '#fff',
                                color: isDarkMode ? '#fff' : '#000',
                                maxWidth: 400,
                                fontSize: '0.875rem',
                                padding: 2,
                                boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                                '& .MuiTooltip-arrow': {
                                  color: isDarkMode ? '#2d3748' : '#fff',
                                },
                              },
                            },
                          }}
                        >
                          <Box
                            sx={{
                              maxWidth: '200px',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                              cursor: 'pointer',
                            }}
                          >
                            {job.name?.length > 30
                              ? `${job.name.substring(0, 30)}...`
                              : job.name}
                          </Box>
                        </Tooltip>
                      </TableCell>
                      <TableCell
                        sx={{
                          py: 2.5,
                          px: 2,
                        }}
                      >
                        <Chip
                          label={getStatusLabel(job.status)}
                          sx={{
                            backgroundColor: statusColors.bg,
                            color: statusColors.text,
                            fontWeight: 600,
                            borderRadius: '12px',
                            fontSize: '0.75rem',
                            textTransform: 'uppercase',
                            letterSpacing: '0.5px',
                            px: 2,
                            py: 0.5,
                            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                          }}
                          size="small"
                        />
                      </TableCell>
                      <TableCell
                        sx={{
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          fontWeight: 600,
                          fontSize: '0.875rem',
                          py: 2.5,
                          px: 2,
                        }}
                      >
                        <Box
                          sx={{
                            display: 'inline-flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            minWidth: 40,
                            height: 28,
                            borderRadius: '14px',
                            backgroundColor: isDarkMode ? 'rgba(129, 199, 132, 0.1)' : 'rgba(46, 125, 50, 0.1)',
                            color: isDarkMode ? '#81C784' : '#2E7D32',
                            fontWeight: 700,
                            fontSize: '0.875rem',
                            px: 2,
                          }}
                        >
                          {job.totalReviews || 0}
                        </Box>
                      </TableCell>
                      <TableCell
                        sx={{
                          borderBottom: `1px solid ${isDarkMode ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)'
                          }`,
                        }}
                      >
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, py: 1 }}>
                          {job.statusBreakdown && Object.keys(job.statusBreakdown).length > 0 ? (
                            Object.entries(job.statusBreakdown).map(([status, count]) => (
                              <Box
                                key={status}
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'space-between',
                                  p: 1,
                                  borderRadius: '8px',
                                  backgroundColor: getStatusColor(status, isDarkMode).bg,
                                  border: `1px solid ${getStatusColor(status, isDarkMode).text}20`,
                                }}
                              >
                                <Typography
                                  variant="caption"
                                  sx={{
                                    color: getStatusColor(status, isDarkMode).text,
                                    fontWeight: 600,
                                    fontSize: '0.7rem',
                                    textTransform: 'uppercase',
                                    letterSpacing: '0.5px',
                                  }}
                                >
                                  {status}
                                </Typography>
                                <Box
                                  sx={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    width: 20,
                                    height: 20,
                                    borderRadius: '10px',
                                    backgroundColor: getStatusColor(status, isDarkMode).text,
                                    color: '#fff',
                                    fontSize: '0.7rem',
                                    fontWeight: 700,
                                  }}
                                >
                                  {count}
                                </Box>
                              </Box>
                            ))
                          ) : (
                            <Typography
                              variant="caption"
                              sx={{
                                color: isDarkMode ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.5)',
                                fontStyle: 'italic',
                                textAlign: 'center',
                                py: 1,
                              }}
                            >
                                                            No data
                            </Typography>
                          )}
                        </Box>
                      </TableCell>
                      <TableCell
                        sx={{
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          fontWeight: 500,
                          fontSize: '0.875rem',
                          py: 2.5,
                          px: 2,
                        }}
                      >
                        <Typography
                          variant="body2"
                          sx={{
                            color: isDarkMode ? '#e2e8f0' : '#4a5568',
                            fontWeight: 500,
                            fontSize: '0.875rem',
                          }}
                        >
                          {new Date(job.createdAt).toLocaleDateString()}
                        </Typography>
                        <Typography
                          variant="caption"
                          sx={{
                            color: isDarkMode ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)',
                            fontSize: '0.75rem',
                          }}
                        >
                          {new Date(job.createdAt).toLocaleTimeString()}
                        </Typography>
                      </TableCell>
                      <TableCell
                        sx={{
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          fontWeight: 500,
                          fontSize: '0.875rem',
                          py: 2.5,
                          px: 2,
                        }}
                      >
                        <Typography
                          variant="body2"
                          sx={{
                            color: isDarkMode ? '#e2e8f0' : '#4a5568',
                            fontWeight: 500,
                            fontSize: '0.875rem',
                          }}
                        >
                          {new Date(job.updatedAt).toLocaleDateString()}
                        </Typography>
                        <Typography
                          variant="caption"
                          sx={{
                            color: isDarkMode ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)',
                            fontSize: '0.75rem',
                          }}
                        >
                          {new Date(job.updatedAt).toLocaleTimeString()}
                        </Typography>
                      </TableCell>
                      <TableCell
                        sx={{
                          py: 2.5,
                          px: 2,
                        }}
                      >
                        <Tooltip title="Download job data">
                          <IconButton
                            onClick={() => downloadJobMutation.mutate(job.id)}
                            disabled={downloadJobMutation.isPending}
                            size="medium"
                            sx={{
                              color: isDarkMode ? '#90CAF9' : '#1976d2',
                              backgroundColor: isDarkMode ? 'rgba(144, 202, 249, 0.1)' : 'rgba(25, 118, 210, 0.1)',
                              borderRadius: '12px',
                              width: 40,
                              height: 40,
                              transition: 'all 0.2s ease-in-out',
                              '&:hover': {
                                backgroundColor: isDarkMode ? 'rgba(144, 202, 249, 0.2)' : 'rgba(25, 118, 210, 0.2)',
                                transform: 'scale(1.1)',
                                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                              },
                              '&:disabled': {
                                backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
                                color: isDarkMode ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
                              },
                            }}
                          >
                            {downloadJobMutation.isPending ? (
                              <CircularProgress size={20} />
                            ) : (
                              <FileDownloadIcon fontSize="medium" />
                            )}
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </TableContainer>

        <TablePagination
          component="div"
          count={total}
          page={page}
          onPageChange={handlePageChange}
          rowsPerPage={limit}
          onRowsPerPageChange={handleRowsPerPageChange}
          rowsPerPageOptions={[5, 10, 25, 50]}
          labelDisplayedRows={({ from, to, count }) =>
            `${from}-${to} of ${count} (Page ${page + 1} of ${totalPages})`
          }
          sx={{
            color: isDarkMode ? '#fff' : 'inherit',
            '.MuiTablePagination-select': {
              color: isDarkMode ? '#fff' : 'inherit',
            },
            '.MuiTablePagination-selectIcon': {
              color: isDarkMode ? '#fff' : 'inherit',
            },
          }}
        />
      </CardContent>
    </Card>
  );
};

export default LexReviewCheckerJobsTable; 