'use client';

import React, { useState, useEffect } from 'react';
import { Box, Tabs, Tab, useTheme } from '@mui/material';

import JeffAdminPanel from 'views/Jeff/JeffAdminPanel';
import Notification from 'components/Notification';
import SellerIdTab from './components/SellerIdTab';
import AsinTab from './components/AsinTab';
import ReviewsTab from './components/ReviewsTab';
import CookieManagementTab from './components/CookieManagementTab';
import LexPromptsTab from './components/LexPromptsTab';
import LexReviewCheckerJobsTable from './components/LexReviewCheckerJobsTable';
import { useSearchParams, useRouter } from 'next/navigation';

const SellerPage = () => {
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';
  const searchParams = useSearchParams();
  const router = useRouter();

  const [activeTab, setActiveTab] = useState(
    parseInt(searchParams.get('tabID')) ?? 0,
  );
  const [loading, setLoading] = useState(false);
  const [notification, setNotification] = useState({
    open: false,
    message: '',
    severity: 'info',
    duration: 900,
    autoHide: true,
  });

  // Update active tab based on URL parameter
  useEffect(() => {
    const tabId = searchParams.get('tabID') ?? 0;

    const tabIndex = parseInt(tabId);
    if (!isNaN(tabIndex) && tabIndex >= 0 && tabIndex <= 5) {
      setActiveTab(tabIndex);
    }
  }, [searchParams]);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    // Update URL with new tab ID
    router.push(`/jeff/admin/seller?tabID=${newValue}`);
  };

  const handleCloseNotification = () => {
    setNotification({
      open: false,
      message: '',
      severity: 'info',
      duration: 900,
      autoHide: true,
    });
  };

  return (
    <JeffAdminPanel title="Seller Management">
      <Box sx={{ width: '100%' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            aria-label="seller tabs"
            sx={{
              '& .MuiTabs-indicator': {
                backgroundColor: isDarkMode ? '#90CAF9' : '#1976d2',
              },
              '& .MuiTab-root': {
                color: isDarkMode ? '#e2e8f0' : '#4a5568',
                '&.Mui-selected': {
                  color: isDarkMode ? '#90CAF9' : '#1976d2',
                },
              },
            }}
          >
            <Tab label="Seller ID" />
            <Tab label="ASIN" />
            <Tab label="Reviews" />
            <Tab label="Cookie Management" />
            <Tab label="LEX Prompts" />
            <Tab label="LEX Review Checker Jobs" />
          </Tabs>
        </Box>

        <Box sx={{ mt: 3 }}>
          {activeTab === 0 && (
            <SellerIdTab
              loading={loading}
              setLoading={setLoading}
              setNotification={setNotification}
              isDarkMode={isDarkMode}
            />
          )}

          {activeTab === 1 && (
            <AsinTab
              setLoading={setLoading}
              setNotification={setNotification}
              loading={loading}
              isDarkMode={isDarkMode}
            />
          )}

          {activeTab === 2 && (
            <ReviewsTab
              loading={loading}
              setLoading={setLoading}
              setNotification={setNotification}
              isDarkMode={isDarkMode}
            />
          )}

          {activeTab === 3 && (
            <CookieManagementTab
              loading={loading}
              setLoading={setLoading}
              setNotification={setNotification}
              isDarkMode={isDarkMode}
            />
          )}

          {activeTab === 4 && (
            <LexPromptsTab
              loading={loading}
              setLoading={setLoading}
              setNotification={setNotification}
              isDarkMode={isDarkMode}
            />
          )}

          {activeTab === 5 && (
            <LexReviewCheckerJobsTable
              loading={loading}
              setLoading={setLoading}
              setNotification={setNotification}
              isDarkMode={isDarkMode}
            />
          )}
        </Box>
      </Box>

      <Notification
        open={notification.open}
        message={notification.message}
        severity={notification.severity}
        onClose={handleCloseNotification}
        duration={notification.duration}
        autoHide={notification.autoHide}
      />
    </JeffAdminPanel>
  );
};

export default SellerPage;
