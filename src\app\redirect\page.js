import axios from 'axios';
import { redirect } from 'next/navigation';
import React from 'react';

async function RedirectPage({ searchParams }) {
  let shouldThrowError = false;
  const {
    n_uuid,
    utm_uuid,
    utm_content,
    utm_medium,
    utm_campaign,
    utm_source,
    utm_email,
    utm_sellerId,
    utm_term,
  } = searchParams;

  // If none of the parameters are present, trigger an error
  if (
    !n_uuid &&
    !utm_uuid &&
    !utm_content &&
    !utm_medium &&
    !utm_campaign &&
    !utm_source &&
    !utm_email &&
    !utm_sellerId &&
    !utm_term
  ) {
    shouldThrowError = true;
  }

  if (!n_uuid) {
    // Check required parameters when n_uuid is absent
    if (
      ['testimonials', 'ctaLink', 'caseStudies'].includes(utm_medium) &&
      (!utm_uuid || !utm_content)
    ) {
      shouldThrowError = true;
    }

    if (
      !['testimonials', 'ctaLink', 'caseStudies'].includes(utm_medium) &&
      (!utm_uuid || !utm_content || !utm_term)
    ) {
      shouldThrowError = true;
    }
  }

  if (shouldThrowError) {
    return redirect('/error');
  }

  // Prepare parameters by filtering out null, undefined, and empty string values
  const params = {
    n_uuid,
    utm_uuid,
    utm_content,
    utm_medium,
    utm_campaign,
    utm_source,
    utm_term,
    utm_sellerId,
    utm_email,
  };

  let response = null;
  try {
    response = await axios.get(
      'https://api.jeff.equalcollective.com/api/redirect',
      { params },
    );
  } catch (error) {
    console.error('Error fetching redirect URL:', error);
    return redirect('/error');
  }

  const { uuid, campaign, type, sellerId, email, clientName, url } =
    response.data.redirectUrl;

  if (url) {
    return (
      <html>
        <head>
          <script
            async
            src="https://www.googletagmanager.com/gtag/js?id=G-SB9V1VN3XP"
          ></script>
          <script
            type="text/javascript"
            dangerouslySetInnerHTML={{
              __html: `
              (function (f, b) {
                if (!b.__SV) {
                  var e, g, i, h;
                  window.mixpanel = b;
                  b._i = [];
                  b.init = function (e, f, c) {
                    function g(a, d) {
                      var b = d.split('.');
                      2 == b.length && ((a = a[b[0]]), (d = b[1]));
                      a[d] = function () {
                        a.push(
                          [d].concat(Array.prototype.slice.call(arguments, 0)),
                        );
                      };
                    }
                    var a = b;
                    'undefined' !== typeof c
                      ? (a = b[c] = [])
                      : (c = 'mixpanel');
                    a.people = a.people || [];
                    a.toString = function (a) {
                      var d = 'mixpanel';
                      'mixpanel' !== c && (d += '.' + c);
                      a || (d += ' (stub)');
                      return d;
                    };
                    a.people.toString = function () {
                      return a.toString(1) + '.people (stub)';
                    };
                    i =
                      'disable time_event track track_pageview track_links track_forms track_with_groups add_group set_group remove_group register register_once alias unregister identify name_tag set_config reset opt_in_tracking opt_out_tracking has_opted_in_tracking has_opted_out_tracking clear_opt_in_out_tracking start_batch_senders people.set people.set_once people.unset people.increment people.append people.union people.track_charge people.clear_charges people.delete_user people.remove'.split(
                        ' ',
                      );
                    for (h = 0; h < i.length; h++) g(a, i[h]);
                    var j = 'set set_once union unset remove delete'.split(' ');
                    a.get_group = function () {
                      function b(c) {
                        d[c] = function () {
                          call2_args = arguments;
                          call2 = [c].concat(
                            Array.prototype.slice.call(call2_args, 0),
                          );
                          a.push([e, call2]);
                        };
                      }
                      for (
                        var d = {},
                          e = ['get_group'].concat(
                            Array.prototype.slice.call(arguments, 0),
                          ),
                          c = 0;
                        c < j.length;
                        c++
                      )
                        b(j[c]);
                      return d;
                    };
                    b._i.push([e, f, c]);
                  };
                  b.__SV = 1.2;
                  e = f.createElement('script');
                  e.type = 'text/javascript';
                  e.async = !0;
                  e.src =
                    'undefined' !== typeof MIXPANEL_CUSTOM_LIB_URL
                      ? MIXPANEL_CUSTOM_LIB_URL
                      : 'file:' === f.location.protocol &&
                        '//cdn.mxpnl.com/libs/mixpanel-2-latest.min.js'.match(
                          /^\\/\\//,
                        )
                      ? 'https://cdn.mxpnl.com/libs/mixpanel-2-latest.min.js'
                      : '//cdn.mxpnl.com/libs/mixpanel-2-latest.min.js';
                  g = f.getElementsByTagName('script')[0];
                  g.parentNode.insertBefore(e, g);
                }
              })(document, window.mixpanel || []);

              mixpanel.init("c49ee60d7bbb6be89251a40c35e42423", {
                debug: true,
                ignore_dnt: true,
                track_pageview: false,
                persistence: "localStorage",
              });
                mixpanel.track('Redirect', {
                  uuid: ${JSON.stringify(uuid)},
                  client_name: ${JSON.stringify(clientName)},
                  type: ${JSON.stringify(type)},
                  campaign: ${JSON.stringify(campaign)},
                  seller_id: ${JSON.stringify(sellerId)},
                  source: ${JSON.stringify(utm_source)},
                  redirect_url: ${JSON.stringify(url)},
                  email: ${JSON.stringify(email)},
                });
              `,
            }}
          />
          <script
            dangerouslySetInnerHTML={{
              __html: `
                window.dataLayer = window.dataLayer || [];
                function gtag() { dataLayer.push(arguments); }
                gtag('js', new Date());
                gtag('config', 'G-SB9V1VN3XP', {
                  page_title: 'Redirect Page',
                  page_path: window.location.href,
                  custom_map: {
                    dimension1: 'uuid',
                    dimension2: 'client_name',
                    dimension3: 'type',
                    dimension4: 'campaign',
                    dimension5: 'seller_id',
                    dimension6: 'source',
                    dimension7: 'redirect_url',
                  }
                });
                
                gtag('event', 'page_view', {
                  uuid: ${JSON.stringify(uuid)},
                  client_name: ${JSON.stringify(clientName)},
                  type: ${JSON.stringify(type)},
                  campaign: ${JSON.stringify(campaign)},
                  seller_id: ${JSON.stringify(sellerId)},
                  source: ${JSON.stringify(utm_source)},
                  redirect_url: ${JSON.stringify(url)},
                  email: ${JSON.stringify(email)},
                });

                gtag('event', 'redirect', {
                  uuid: ${JSON.stringify(uuid)},
                  client_name: ${JSON.stringify(clientName)},
                  type: ${JSON.stringify(type)},
                  campaign: ${JSON.stringify(campaign)},
                  seller_id: ${JSON.stringify(sellerId)},
                  source: ${JSON.stringify(utm_source)},
                  redirect_url: ${JSON.stringify(url)},
                  email: ${JSON.stringify(email)},
                });

                setTimeout(() => {
                  window.location.href = ${JSON.stringify(url)};
                }, 5);
              `,
            }}
          />
        </head>
        <body></body>
      </html>
    );
  } else {
    console.log('No redirectUrl returned from the API');
    return redirect('/error');
  }
}

export default RedirectPage;
