'use client';

import React, { useState } from 'react';
import {
  IconButton,
  CircularProgress,
  Tooltip,
  Popover,
  Box,
  Typography,
  Switch,
  FormControlLabel,
  Button,
  Paper,
} from '@mui/material';
import DownloadIcon from '@mui/icons-material/Download';
import TableRowsIcon from '@mui/icons-material/TableRows';
import InfoIcon from '@mui/icons-material/Info';
import { getSellerBotAxiosInstance } from 'config/axios';
import { API_ENDPOINTS } from 'config/api';
import { downloadBlob } from '../utils/downloadUtils';

/**
 * Reusable download button component with local loading state and debug mode toggle
 */
const DownloadButton = ({
  jobId,
  type = 'results', // 'results' or 'input'
  isDarkMode = false,
  onError,
  fallbackHandler,
  tooltipTitle,
}) => {
  // Local loading state for this specific button
  const [isLoading, setIsLoading] = useState(false);
  // State for popover and debug toggle
  const [anchorEl, setAnchorEl] = useState(null);
  const [includeDebug, setIncludeDebug] = useState(false);

  // Handle popover open/close
  const handleInfoClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  // Handle the download
  const handleDownload = async () => {
    try {
      setIsLoading(true);

      // Determine the endpoint based on the download type
      let endpoint = '';
      let fileName = '';

      if (type === 'results') {
        // Use the export endpoint with a single jobId
        endpoint = API_ENDPOINTS.SB_EXPORT_LEADS.replace(':jobIds', jobId);
        fileName = `lead-export-${jobId}${includeDebug ? '-debug' : ''}.zip`;
      } else if (type === 'input') {
        // Use the export input endpoint with a single jobId
        endpoint = API_ENDPOINTS.SB_EXPORT_INPUT.replace(':jobIds', jobId);
        fileName = `lead-input-${jobId}${includeDebug ? '-debug' : ''}.csv`;
      }

      // Add debug parameter if enabled
      const queryParams = includeDebug ? '?includeDebug=true' : '';

      // Make the API request
      const response = await getSellerBotAxiosInstance().get(`${endpoint}${queryParams}`, {
        responseType: 'blob',
      });

      // Download the file
      downloadBlob(response.data, fileName);

      // Close popover after successful download
      handleClose();
    } catch (error) {
      console.error(`Error downloading ${type}:`, error);

      // Call the error handler if provided
      if (onError) {
        onError(error);
      }

      // Try fallback method if provided
      if (fallbackHandler) {
        try {
          fallbackHandler(jobId);
        } catch (fallbackError) {
          console.error('Fallback download also failed:', fallbackError);
        }
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Determine the icon to use based on the type
  const Icon = type === 'input' ? TableRowsIcon : DownloadIcon;

  // Determine the color based on the type and dark mode
  const getButtonColor = () => {
    if (type === 'input') {
      return isDarkMode ? '#9AE6B4' : '#48BB78';
    }
    return isDarkMode ? '#90CAF9' : '#4299E1';
  };

  // Determine the hover background color
  const getHoverBgColor = () => {
    if (type === 'input') {
      return isDarkMode
        ? 'rgba(154, 230, 180, 0.1)'
        : 'rgba(72, 187, 120, 0.1)';
    }
    return isDarkMode ? 'rgba(144, 202, 249, 0.1)' : 'rgba(66, 153, 225, 0.1)';
  };

  const open = Boolean(anchorEl);

  return (
    <>
      <Tooltip
        title={
          tooltipTitle ||
          (type === 'input' ? 'Export Input Data' : 'Export Results')
        }
      >
        <IconButton
          onClick={handleInfoClick}
          disabled={isLoading}
          size="small"
          sx={{
            color: getButtonColor(),
            '&:hover': {
              backgroundColor: getHoverBgColor(),
            },
          }}
        >
          {isLoading ? (
            <CircularProgress size={type === 'input' ? 16 : 20} color="inherit" />
          ) : (
            <Icon fontSize="small" />
          )}
        </IconButton>
      </Tooltip>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
      >
        <Paper
          sx={{
            p: 2,
            maxWidth: 320,
            backgroundColor: isDarkMode ? '#2D3748' : '#FFFFFF',
            color: isDarkMode ? '#E2E8F0' : '#4A5568',
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <InfoIcon
              fontSize="small"
              sx={{
                mr: 1,
                color: isDarkMode ? '#63B3ED' : '#3182CE'
              }}
            />
            <Typography variant="subtitle2" fontWeight={600}>
              {type === 'input' ? 'Export Input Data' : 'Export Results'}
            </Typography>
          </Box>

          <Typography variant="body2" sx={{ mb: 2, lineHeight: 1.5 }}>
            {type === 'input'
              ? 'Download the original input data used for lead generation in CSV format.'
              : 'Download the lead generation results in ZIP format containing CSV files with all discovered leads.'
            }
          </Typography>

          <FormControlLabel
            control={
              <Switch
                checked={includeDebug}
                onChange={(e) => setIncludeDebug(e.target.checked)}
                size="small"
                sx={{
                  '& .MuiSwitch-switchBase.Mui-checked': {
                    color: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                  '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                    backgroundColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                }}
              />
            }
            label={
              <Box>
                <Typography variant="body2" fontWeight={500}>
                  Include Debug Data (Raw SERP Output)
                </Typography>
                <Typography variant="body2" fontSize="0.75rem" color="text.secondary">
                  Includes the complete raw SERP API response data in the CSV export for debugging purposes
                </Typography>
                {includeDebug && (
                  <Typography variant="body2" fontSize="0.7rem" sx={{ mt: 0.5, fontStyle: 'italic', color: 'orange' }}>
                    ⚠️ Warning: Debug mode will result in larger file sizes
                  </Typography>
                )}
              </Box>
            }
            sx={{ mb: 2, alignItems: 'flex-start' }}
          />

          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
            <Button
              size="small"
              onClick={handleClose}
              sx={{ color: isDarkMode ? '#A0AEC0' : '#718096' }}
            >
              Cancel
            </Button>
            <Button
              size="small"
              variant="contained"
              onClick={handleDownload}
              disabled={isLoading}
              sx={{
                backgroundColor: isDarkMode ? '#90CAF9' : '#1976d2',
                '&:hover': {
                  backgroundColor: isDarkMode ? '#63B3ED' : '#1565c0',
                },
              }}
            >
              {isLoading
                ? (includeDebug ? 'Generating CSV with debug data...' : 'Downloading...')
                : 'Download'
              }
            </Button>
          </Box>
        </Paper>
      </Popover>
    </>
  );
};

export default DownloadButton;
