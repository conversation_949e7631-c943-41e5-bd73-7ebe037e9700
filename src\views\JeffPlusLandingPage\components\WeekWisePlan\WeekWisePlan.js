import React from 'react';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Grid from '@mui/material/Grid';
import { alpha } from '@mui/material/styles';
import { colors } from '@mui/material';
import Spacer from '../../../../components/Equal/Spacer';
import LooksOneIcon from '@mui/icons-material/LooksOne';
import LooksTwoIcon from '@mui/icons-material/LooksTwo';
import Looks3Icon from '@mui/icons-material/Looks3';
import Looks4Icon from '@mui/icons-material/Looks4';

import { List, ListItem, ListItemText } from '@mui/material';
import { styled } from '@mui/material/styles';

const WEEK_WISE_PLAN = [
  {
    icon: LooksOneIcon,
    title: 'Onboarding & Setup',
    items: [
      {
        title: 'Onboarding call',
        text: 'Gather business data; understand your USP, offer, and process.'
      },
      {
        title: 'Domains & inboxes warm-up',
        text: 'Start the warm-up process for domains and inboxes.'
      }
    ]
  },
  {
    icon: LooksTwoIcon,
    title: 'List building',
    items: [
      {
        title: 'List building',
        text: 'Jeff compiles a list of 5000 Amazon sellers and DTC brands with validated email addresses.'
      },
      {
        title: 'Training Jeff',
        text: 'Train Jeff to write emails; match your tone and successful sequences.'
      }
    ]
  },
  {
    icon: Looks3Icon,
    title: 'Email sending starts',
    items: [
      {
        title: 'Sending eMails',
        text: 'Jeff begins sending 150 emails per day.'
      },
      {
        title: 'Regular updates',
        text: 'Jeff provides regular updates on Slack.'
      }
    ]
  },
  {
    icon: Looks4Icon,
    title: 'Increase sending',
    items: [
      {
        title: 'Increase eMail Capacity',
        text: 'Jeff increases to 360 emails per day.'
      },
      {
        title: 'First meeting expected',
        text: 'You can expect the first meeting by the end of Week 4.'
      }
    ]
  }
];

const BulletList = styled(List)({
  listStyleType: 'disc',
  paddingLeft: '20px',
});

const BulletListItem = styled(ListItem)({
  display: 'list-item',
  padding: 0,
});

const Team = () => {

  return (
    <Box>
      <Typography variant={'h4'} sx={{ fontWeight: 700, textAlign: 'center' }} gutterBottom>
        {'Your '}
        <Typography
          color={'primary'}
          component={'span'}
          variant={'inherit'}
          sx={{
            fontWeight: 700,
            display: 'inline',
            background: `linear-gradient(180deg, transparent 82%, ${alpha(
              colors.blue[50],
              0.3,
            )} 0%)`,
          }}
        >
          {'first 4 weeks'}
        </Typography>
        {' with Jeff'}
      </Typography>
      <Spacer y={2} />
      <Grid container spacing={1} alignItems={'stretch'}>
        {WEEK_WISE_PLAN.map((week, key) => {
          return (
            <Grid item md={3} key={key}>
              <Box component={Card} variant='outlined' sx={{ height: '100%' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography>Week</Typography>
                    <Spacer x={0.5} />
                    <week.icon sx={{ width: 30, height: 30 }} />
                  </Box>
                  <Typography variant="h5" sx={{ fontWeight: 700 }}>{week.title}</Typography>
                  <Spacer y={1} />
                  <BulletList>
                    {week.items.map((item, itemKey) => {
                      return (
                        <BulletListItem key={itemKey}>
                          <ListItemText>
                            <Typography>
                              <Typography variant="inherit" component="span" sx={{ fontWeight: 800 }}>{`${item.title}: `}</Typography>
                              {item.text}
                            </Typography>
                          </ListItemText>
                        </BulletListItem>
                      );
                    })}
                  </BulletList>
                </CardContent>
              </Box>
            </Grid>
          );
        })}
      </Grid>
      <Spacer y={2} />

    </Box>
  );
};

export default Team;