'use client';

import React, { useEffect } from 'react';
import <PERSON><PERSON>ashboard from 'views/Jeff/JeffDashboard';
import { Providers } from '../../brandbuddy/providers';
import { useUser } from 'views/Jeff/Utils/getUser';
import { CircularProgress, Box } from '@mui/material';
import { useRouter } from 'next/navigation';

const DashboardPage = () => {
  const { data: user, isLoading } = useUser();
  const router = useRouter();
  
  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/jeff/login');
    }
  }, [isLoading, user, router]);
  
  if (isLoading) {
    return (
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
      }}>
        <CircularProgress />
      </Box>
    );
  }
  
  if (!user) {
    return null; // The useUser hook will handle redirection if no user
  }

  return (
    <Providers user={user}>
      <JeffDashboard user={user} />
    </Providers>
  );
};

export default DashboardPage;
