'use client';

import { getSellerBotAxiosInstance } from 'config/axios';
import { API_ENDPOINTS } from 'config/api';

// Helper function to get SellerBot base URL
const getSellerBotBaseURL = () => {
  const axiosInstance = getSellerBotAxiosInstance();
  return axiosInstance.defaults.baseURL || '';
};

export const fetchLeadJobs = async (page = 1, limit = 10) => {
  try {
    const response = await getSellerBotAxiosInstance().get(
      API_ENDPOINTS.SB_LIST_JOBS,
      {
        params: {
          page,
          limit,
        },
      },
    );

    if (response.data && response.data.success) {
      return {
        success: true,
        data: response.data.data || [],
        pagination: response.data.pagination,
        message: 'Lead jobs fetched successfully',
      };
    } else {
      return {
        success: false,
        data: [],
        pagination: null,
        message: response.data?.error || 'Failed to fetch lead jobs',
      };
    }
  } catch (error) {
    console.error('Error fetching lead jobs:', error);
    throw error;
  }
};

export const deleteLeadJob = async (jobId) => {
  try {
    const endpoint = API_ENDPOINTS.SB_DELETE_LEAD_JOB.replace(':jobId', jobId);
    const response = await getSellerBotAxiosInstance().delete(endpoint, {
      headers: {
        'x-api-key': '12345678', // This is the API key used in the SellerBot API
      },
    });

    if (response.data && response.data.message) {
      return {
        success: true,
        message: response.data.message,
      };
    } else {
      return {
        success: false,
        message: 'Job deletion response was invalid',
      };
    }
  } catch (error) {
    console.error('Error deleting job:', error);
    return {
      success: false,
      message: error.response?.data?.error || 'Failed to delete job',
    };
  }
};

export const exportLeads = async (jobIds, includeDebug = false) => {
  try {
    const jobIdsString = Array.isArray(jobIds) ? jobIds.join(',') : jobIds;
    const endpoint = API_ENDPOINTS.SB_EXPORT_LEADS.replace(
      ':jobIds',
      jobIdsString,
    );

    // Add debug parameter if requested
    const queryParams = includeDebug ? '?includeDebug=true' : '';

    // We return the URL for the download
    return {
      success: true,
      url: `${getSellerBotBaseURL()}${endpoint}${queryParams}`,
      message: 'Export URL generated',
    };
  } catch (error) {
    console.error('Error generating export URL:', error);
    return {
      success: false,
      message: 'Failed to generate export URL',
    };
  }
};

// New function to export the input data
export const exportLeadInput = async (jobIds, includeDebug = false) => {
  try {
    const jobIdsString = Array.isArray(jobIds) ? jobIds.join(',') : jobIds;
    const endpoint = API_ENDPOINTS.SB_EXPORT_INPUT.replace(
      ':jobIds',
      jobIdsString,
    );

    // Add debug parameter if requested
    const queryParams = includeDebug ? '?includeDebug=true' : '';

    // Return the URL for the download
    return {
      success: true,
      url: `${getSellerBotBaseURL()}${endpoint}${queryParams}`,
      message: 'Input data export URL generated',
    };
  } catch (error) {
    console.error('Error generating input data export URL:', error);
    return {
      success: false,
      message: 'Failed to generate input data export URL',
    };
  }
};

// Function to submit a single lead
export const submitSingleLead = async (leadData) => {
  try {
    // Ensure searchPattern is included in the payload
    const payload = {
      ...leadData,
      searchPattern: leadData.searchPattern || 'original',
    };

    const response = await getSellerBotAxiosInstance().post(
      API_ENDPOINTS.SB_SINGLE_LEAD,
      payload,
    );

    if (response.data && response.data.jobId) {
      return {
        success: true,
        jobId: response.data.jobId,
        message:
          response.data.message || 'Single lead job created successfully',
      };
    } else {
      return {
        success: false,
        message: response.data?.error || 'Failed to create single lead job',
      };
    }
  } catch (error) {
    console.error('Error submitting single lead:', error);
    return {
      success: false,
      message: error.response?.data?.error || 'Failed to submit single lead',
    };
  }
};

// Function to upload a CSV file for lead generation
export const uploadLeadGenerationFile = async (
  file,
  useDomain,
  mode,
  searchPattern,
) => {
  try {
    const formData = new FormData();
    formData.append('csvFile', file);
    formData.append('useDomain', useDomain);

    // Add searchPattern to form data
    if (searchPattern) {
      formData.append('searchPattern', searchPattern);
    } else {
      formData.append('searchPattern', 'original');
    }

    const queryParams = mode ? `?mode=${mode}` : '';
    const response = await getSellerBotAxiosInstance().post(
      `${API_ENDPOINTS.SB_LEAD_GENERATE}${queryParams}`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    );

    if (response.data && response.data.jobId) {
      return {
        success: true,
        jobId: response.data.jobId,
        message:
          response.data.message || 'Lead generation job created successfully',
      };
    } else {
      return {
        success: false,
        message: response.data?.error || 'Failed to create lead generation job',
      };
    }
  } catch (error) {
    console.error('Error uploading lead generation file:', error);
    return {
      success: false,
      message:
        error.response?.data?.error || 'Failed to upload lead generation file',
    };
  }
};

// Function to upload a CSV file for company data operations
export const uploadCompanyData = async (
  file,
  operation,
  type,
  skipErrorRows,
  allowBlankRows,
) => {
  try {
    const formData = new FormData();
    formData.append('csvFile', file);

    // Build query parameters
    let queryParams = '';
    if (skipErrorRows || allowBlankRows) {
      const params = [];
      if (skipErrorRows) params.push('skiperrorrows=true');
      if (allowBlankRows) params.push('allowblankrows=true');
      queryParams = `?${params.join('&')}`;
    }

    // Replace placeholders in endpoint
    const endpoint = API_ENDPOINTS.SB_UPLOAD_COMPANY_CSV.replace(
      ':operation',
      operation,
    ).replace(':type', type);

    const response = await getSellerBotAxiosInstance().post(
      `${endpoint}${queryParams}`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    );

    if (response.status === 200) {
      return {
        success: true,
        message: response.data.message || 'Company data uploaded successfully',
      };
    } else if (response.status === 403 && response.data) {
      // The API returns a CSV with error rows when there are issues
      return {
        success: false,
        hasErrorData: true,
        errorData: response.data,
        message:
          'Upload completed with errors. Please download the error file to see details.',
      };
    } else {
      return {
        success: false,
        message: response.data?.error || 'Failed to upload company data',
      };
    }
  } catch (error) {
    console.error('Error uploading company data:', error);
    return {
      success: false,
      message: error.response?.data?.error || 'Failed to upload company data',
    };
  }
};

// Function to check lead job status
export const checkLeadJobStatus = async (jobId) => {
  try {
    const endpoint = API_ENDPOINTS.SB_LEAD_JOB_STATUS.replace(':jobId', jobId);
    const response = await getSellerBotAxiosInstance().get(endpoint);

    if (response.data) {
      return {
        success: true,
        isComplete: response.data.isComplete,
        pendingLeadsCount: response.data.pendingLeadsCount,
        message: response.data.isComplete
          ? 'Job is complete'
          : `Job in progress, ${response.data.pendingLeadsCount} leads pending`,
      };
    } else {
      return {
        success: false,
        message: 'Failed to check job status',
      };
    }
  } catch (error) {
    console.error('Error checking lead job status:', error);
    return {
      success: false,
      message: error.response?.data?.error || 'Failed to check job status',
    };
  }
};

// Function to export Google Sheets links for lead jobs
export const exportLeadLinks = async (jobIds, includeDebug = false) => {
  try {
    const jobIdsString = Array.isArray(jobIds) ? jobIds.join(',') : jobIds;
    const endpoint = API_ENDPOINTS.SB_EXPORT_JOB_LINKS.replace(
      ':jobIds',
      jobIdsString,
    );

    // Add debug parameter if requested
    const queryParams = includeDebug ? '?includeDebug=true' : '';

    const response = await getSellerBotAxiosInstance().get(
      `${endpoint}${queryParams}`,
    );

    if (response.data && response.data.success) {
      return {
        success: true,
        data: response.data.data,
        message:
          response.data.message || 'Google Sheets links generated successfully',
      };
    } else {
      return {
        success: false,
        message:
          response.data?.error || 'Failed to generate Google Sheets links',
      };
    }
  } catch (error) {
    console.error('Error exporting Google Sheets links:', error);
    return {
      success: false,
      message:
        error.response?.data?.error || 'Failed to export Google Sheets links',
    };
  }
};

// Function to fetch available search patterns
export const fetchSearchPatterns = async () => {
  try {
    const response = await getSellerBotAxiosInstance().get(
      API_ENDPOINTS.SB_SEARCH_PATTERNS,
    );

    if (response.data) {
      return {
        success: true,
        patterns: response.data.patterns || [],
      };
    } else {
      return {
        success: false,
        message: 'Failed to fetch search patterns',
      };
    }
  } catch (error) {
    console.error('Error fetching search patterns:', error);
    return {
      success: false,
      message: error.response?.data?.error || 'Failed to fetch search patterns',
    };
  }
};

// Function to fetch available search columns
export const fetchSearchColumns = async () => {
  try {
    const response = await getSellerBotAxiosInstance().get(
      API_ENDPOINTS.SB_SEARCH_COLUMNS,
    );

    if (response.data) {
      return {
        success: true,
        columns: response.data.columns || [],
      };
    } else {
      return {
        success: false,
        message: 'Failed to fetch search columns',
      };
    }
  } catch (error) {
    console.error('Error fetching search columns:', error);
    return {
      success: false,
      message: error.response?.data?.error || 'Failed to fetch search columns',
    };
  }
};

// Function to upload a CSV file for lead type (prospect or matching)
export const uploadLeadTypeData = async (file, type) => {
  try {
    const formData = new FormData();
    formData.append('csvFile', file);

    // Replace placeholder in endpoint
    const endpoint = API_ENDPOINTS.SB_UPLOAD_LEAD_CSV.replace(':type', type);

    const response = await getSellerBotAxiosInstance().post(
      endpoint,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    );

    if (response.status === 200) {
      return {
        success: true,
        message: response.data.message || `${type} data uploaded successfully`,
      };
    } else if (response.status === 403 && response.data) {
      // The API returns a CSV with error rows when there are issues
      return {
        success: false,
        errorData: response.data,
        message:
          'Some rows contain errors. A CSV with error details has been generated.',
      };
    } else {
      return {
        success: false,
        message: response.data?.error || `Failed to upload ${type} data`,
      };
    }
  } catch (error) {
    console.error(`Error uploading ${type} data:`, error);
    return {
      success: false,
      message: error.response?.data?.error || `Failed to upload ${type} data`,
    };
  }
};
