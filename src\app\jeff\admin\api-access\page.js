'use client';

import React, { useEffect, useRef } from 'react';
import {
  Container,
  Grid,
  Card,
  CardContent,
  Typography,
  TextField,
  IconButton,
  Box,
} from '@mui/material';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import VpnKeyIcon from '@mui/icons-material/VpnKey';
import JeffAdminPanel from 'views/Jeff/JeffAdminPanel';
import { useUser } from 'views/Jeff/Utils/getUser';
import { useAdminTheme } from 'views/Jeff/AdminThemeContext';
import { useRouter } from 'next/navigation';
import Cookies from 'js-cookie';

const ApiAccessPage = () => {
  const { data: user, isLoading: isUserLoading } = useUser();
  const router = useRouter();
  const { mode: adminThemeMode } = useAdminTheme() || { mode: 'light' };
  const isDarkMode = adminThemeMode === 'dark';
  const tokenInputRef = useRef(null);
  
  const getBearerToken = () => {
    const token = Cookies.get('jeff-authorization') || localStorage.getItem('jeff-authorization') || '';
    return token.replace(/^Bearer\s+/i, '');
  };

  const handleCopyToken = () => {
    const token = getBearerToken();
    navigator.clipboard.writeText(token).then(
      () => {
        console.log('Token copied successfully');
      },
      (err) => {
        console.error('Failed to copy token:', err);
      }
    );
  };

  useEffect(() => {
    if (!isUserLoading && !user) {
      router.push('/jeff/login');
    } else if (!isUserLoading && user && user?.userType && user?.userType !== 'admin') {
      router.push('/jeff/dashboard');
    }
  }, [isUserLoading, user, router]);

  useEffect(() => {
    if (tokenInputRef.current) {
      tokenInputRef.current.value = '••••••••••••••••';
    }
  }, []);

  return (
    <JeffAdminPanel title="API Access">
      <Container
        maxWidth="lg"
        sx={{
          py: 4,
          backgroundColor: isDarkMode ? '#1A202C' : 'inherit',
          color: isDarkMode ? '#E2E8F0' : 'inherit',
        }}
      >
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card
              sx={{
                backgroundColor: isDarkMode ? '#2D3748' : 'white',
                color: isDarkMode ? '#E2E8F0' : 'inherit',
              }}
            >
              <CardContent>
                <Typography
                  variant="h6"
                  gutterBottom
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    mb: 2,
                  }}
                >
                  <VpnKeyIcon
                    sx={{
                      mr: 1,
                      color: isDarkMode ? '#90CAF9' : '#1976d2',
                    }}
                  />
                  Bearer Token
                </Typography>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <TextField
                    fullWidth
                    type="password"
                    inputRef={tokenInputRef}
                    InputProps={{
                      readOnly: true,
                      endAdornment: (
                        <IconButton
                          onClick={handleCopyToken}
                          edge="end"
                          sx={{ color: isDarkMode ? '#90CAF9' : '#1976d2' }}
                        >
                          <ContentCopyIcon />
                        </IconButton>
                      ),
                    }}
                    sx={{
                      '& .MuiInputBase-input': {
                        color: isDarkMode ? '#E2E8F0' : 'inherit',
                      },
                    }}
                  />
                </Box>

                <Typography
                  variant="body2"
                  sx={{
                    mt: 2,
                    color: isDarkMode
                      ? 'rgba(255, 255, 255, 0.7)'
                      : 'text.secondary',
                  }}
                >
                  Use this Bearer token to authenticate API requests for Seller Bot and Jeff. Keep this
                  token secure and do not share it with others.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Container>
    </JeffAdminPanel>
  );
};

export default ApiAccessPage;


