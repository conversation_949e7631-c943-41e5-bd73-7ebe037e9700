'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  Chip,
  LinearProgress,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
} from '@mui/material';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import SpeedIcon from '@mui/icons-material/Speed';
import AssessmentIcon from '@mui/icons-material/Assessment';
import CompareArrowsIcon from '@mui/icons-material/CompareArrows';
import { useAdminTheme } from 'views/Jeff/AdminThemeContext';
import { getAIStats } from '../utils/aiApi';

const AIStatsDashboard = () => {
  const { mode: adminThemeMode } = useAdminTheme() || { mode: 'light' };
  const isDarkMode = adminThemeMode === 'dark';
  
  const [stats, setStats] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [timeRange, setTimeRange] = useState('month');
  const [selectedModel, setSelectedModel] = useState('all');

  useEffect(() => {
    fetchStats();
  }, [timeRange, selectedModel]);

  const fetchStats = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await getAIStats(selectedModel === 'all' ? null : selectedModel, timeRange);
      if (result.success) {
        setStats(result.stats);
      } else {
        setError(result.message);
      }
    } catch (err) {
      setError('Failed to fetch AI statistics');
    } finally {
      setIsLoading(false);
    }
  };

  const getModelColor = (model) => {
    switch (model) {
      case 'openai': return '#10a37f';
      case 'claude': return '#d97706';
      case 'gemini': return '#4285f4';
      default: return '#6b7280';
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 4,
    }).format(amount);
  };

  const StatCard = ({ title, value, subtitle, icon, color, trend }) => (
    <Card
      sx={{
        height: '100%',
        backgroundColor: isDarkMode ? '#2D3748' : '#FFFFFF',
        border: `1px solid ${isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'}`,
      }}
    >
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Box
            sx={{
              width: 48,
              height: 48,
              borderRadius: '12px',
              backgroundColor: `${color}20`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            {React.cloneElement(icon, { sx: { color, fontSize: 24 } })}
          </Box>
          {trend && (
            <Chip
              label={`${trend > 0 ? '+' : ''}${trend.toFixed(1)}%`}
              size="small"
              sx={{
                backgroundColor: trend > 0 ? '#4caf50' : '#f44336',
                color: 'white',
                fontWeight: 500,
              }}
            />
          )}
        </Box>
        <Typography
          variant="h4"
          sx={{
            fontWeight: 700,
            color: isDarkMode ? '#FFFFFF' : '#000000',
            mb: 1,
          }}
        >
          {value}
        </Typography>
        <Typography
          variant="h6"
          sx={{
            color: isDarkMode ? '#e2e8f0' : '#4a5568',
            fontWeight: 600,
            mb: 1,
          }}
        >
          {title}
        </Typography>
        {subtitle && (
          <Typography
            variant="body2"
            sx={{
              color: isDarkMode ? 'rgba(255,255,255,0.6)' : 'rgba(0,0,0,0.5)',
            }}
          >
            {subtitle}
          </Typography>
        )}
      </CardContent>
    </Card>
  );

  if (isLoading && !stats) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <AssessmentIcon
            sx={{
              color: isDarkMode ? '#90CAF9' : '#1976d2',
              mr: 2,
              fontSize: 32,
            }}
          />
          <Typography
            variant="h4"
            sx={{
              fontWeight: 700,
              color: isDarkMode ? '#E2E8F0' : '#4A5568',
            }}
          >
            AI Analytics Dashboard
          </Typography>
        </Box>
        
        {/* Filters */}
        <Box sx={{ display: 'flex', gap: 2 }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Time Range</InputLabel>
            <Select
              value={timeRange}
              label="Time Range"
              onChange={(e) => setTimeRange(e.target.value)}
            >
              <MenuItem value="day">Today</MenuItem>
              <MenuItem value="week">This Week</MenuItem>
              <MenuItem value="month">This Month</MenuItem>
              <MenuItem value="all">All Time</MenuItem>
            </Select>
          </FormControl>
          
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Model</InputLabel>
            <Select
              value={selectedModel}
              label="Model"
              onChange={(e) => setSelectedModel(e.target.value)}
            >
              <MenuItem value="all">All Models</MenuItem>
              <MenuItem value="openai">OpenAI</MenuItem>
              <MenuItem value="claude">Claude</MenuItem>
              <MenuItem value="gemini">Gemini</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </Box>

      {/* Error Display */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Main Stats Grid */}
      {stats && (
        <>
          <Grid container spacing={3} sx={{ mb: 4 }}>
            {/* Total Jobs */}
            <Grid item xs={12} sm={6} md={3}>
              <StatCard
                title="Total AI Jobs"
                value={stats.totalJobs?.toLocaleString() || '0'}
                subtitle="Jobs processed"
                icon={<SmartToyIcon />}
                color="#2e7d32"
              />
            </Grid>

            {/* Total Cost */}
            <Grid item xs={12} sm={6} md={3}>
              <StatCard
                title="Total Cost"
                value={formatCurrency(stats.totalCost || 0)}
                subtitle="AI processing costs"
                icon={<MonetizationOnIcon />}
                color="#d32f2f"
              />
            </Grid>

            {/* Total Tokens */}
            <Grid item xs={12} sm={6} md={3}>
              <StatCard
                title="Total Tokens"
                value={stats.totalTokens?.toLocaleString() || '0'}
                subtitle="Tokens processed"
                icon={<SpeedIcon />}
                color="#1976d2"
              />
            </Grid>

            {/* Success Rate */}
            <Grid item xs={12} sm={6} md={3}>
              <StatCard
                title="Success Rate"
                value={`${(stats.successRate || 0).toFixed(1)}%`}
                subtitle="Job completion rate"
                icon={<TrendingUpIcon />}
                color="#4caf50"
              />
            </Grid>
          </Grid>

          {/* Model Breakdown */}
          {stats.modelBreakdown && Object.keys(stats.modelBreakdown).length > 0 && (
            <Paper
              sx={{
                p: 3,
                mb: 4,
                backgroundColor: isDarkMode ? '#2D3748' : '#FFFFFF',
                border: `1px solid ${isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'}`,
              }}
            >
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  color: isDarkMode ? '#E2E8F0' : '#4A5568',
                  mb: 3,
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                <CompareArrowsIcon sx={{ mr: 1 }} />
                Model Performance Comparison
              </Typography>
              
              <Grid container spacing={3}>
                {Object.entries(stats.modelBreakdown).map(([model, data]) => (
                  <Grid item xs={12} md={4} key={model}>
                    <Box
                      sx={{
                        p: 2,
                        borderRadius: '8px',
                        backgroundColor: isDarkMode ? '#1A202C' : '#F7FAFC',
                        border: `2px solid ${getModelColor(model)}20`,
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Box
                          sx={{
                            width: 12,
                            height: 12,
                            borderRadius: '50%',
                            backgroundColor: getModelColor(model),
                            mr: 1,
                          }}
                        />
                        <Typography
                          variant="subtitle1"
                          sx={{
                            fontWeight: 600,
                            color: isDarkMode ? '#E2E8F0' : '#4A5568',
                            textTransform: 'capitalize',
                          }}
                        >
                          {model === 'openai' ? 'OpenAI GPT-4' : 
                           model === 'claude' ? 'Claude 3.5' : 
                           model === 'gemini' ? 'Gemini Pro' : model}
                        </Typography>
                      </Box>
                      
                      <Box sx={{ mb: 1 }}>
                        <Typography variant="body2" color="textSecondary">
                          Jobs: {data.jobs || 0}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          Cost: {formatCurrency(data.cost || 0)}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          Avg Cost/Lead: {formatCurrency(data.avgCostPerLead || 0)}
                        </Typography>
                      </Box>
                      
                      <LinearProgress
                        variant="determinate"
                        value={(data.jobs / stats.totalJobs) * 100}
                        sx={{
                          height: 6,
                          borderRadius: 3,
                          backgroundColor: isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
                          '& .MuiLinearProgress-bar': {
                            backgroundColor: getModelColor(model),
                            borderRadius: 3,
                          },
                        }}
                      />
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Paper>
          )}

          {/* Additional Metrics */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Paper
                sx={{
                  p: 3,
                  backgroundColor: isDarkMode ? '#2D3748' : '#FFFFFF',
                  border: `1px solid ${isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'}`,
                }}
              >
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 600,
                    color: isDarkMode ? '#E2E8F0' : '#4A5568',
                    mb: 2,
                  }}
                >
                  Cost Efficiency
                </Typography>
                <Typography
                  variant="h4"
                  sx={{
                    fontWeight: 700,
                    color: getModelColor('openai'),
                    mb: 1,
                  }}
                >
                  {formatCurrency(stats.averageCostPerLead || 0)}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Average cost per lead
                </Typography>
              </Paper>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Paper
                sx={{
                  p: 3,
                  backgroundColor: isDarkMode ? '#2D3748' : '#FFFFFF',
                  border: `1px solid ${isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'}`,
                }}
              >
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 600,
                    color: isDarkMode ? '#E2E8F0' : '#4A5568',
                    mb: 2,
                  }}
                >
                  Processing Volume
                </Typography>
                <Typography
                  variant="h4"
                  sx={{
                    fontWeight: 700,
                    color: getModelColor('claude'),
                    mb: 1,
                  }}
                >
                  {((stats.totalTokens || 0) / 1000).toFixed(1)}K
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Tokens processed (thousands)
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        </>
      )}
    </Box>
  );
};

export default AIStatsDashboard;
