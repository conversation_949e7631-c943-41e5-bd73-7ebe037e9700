'use client';

import React, { createContext, useContext } from 'react';

export const UserContext = createContext('light');

export function Providers(props) {
  const { user, children } = props;
  return <UserContext.Provider value={user}>{children}</UserContext.Provider>;
}

export const useUser = () => {
  const user = useContext(UserContext);

  if (!user) {
    throw new Error('[UserContext.Provider]:call in UserContext Provider');
  }

  return user;
};
