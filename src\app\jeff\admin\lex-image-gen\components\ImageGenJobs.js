import React, { useState } from 'react';
import {
  Box,
  <PERSON>ton,
  <PERSON>po<PERSON>,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  Chip,
  IconButton,
  Tooltip,
  TextField,
  InputAdornment,
  useTheme,
  Checkbox,
  Modal,
} from '@mui/material';
import {
  CloudUpload,
  Download,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

const VisuallyHiddenInput = styled('input')`
  clip: rect(0 0 0 0);
  clip-path: inset(50%);
  height: 1px;
  overflow: hidden;
  position: absolute;
  bottom: 0;
  left: 0;
  white-space: nowrap;
  width: 1px;
`;

const getStatusColor = (status) => {
  switch (status?.toLowerCase()) {
    case 'completed':
      return 'success';
    case 'processing':
      return 'warning';
    case 'queued':
      return 'info';
    case 'cancelled':
      return 'error';
    case 'failed':
      return 'error';
    default:
      return 'default';
  }
};

const truncateFilename = (filename, maxLength = 40) => {
  if (!filename) return '';
  if (filename.length <= maxLength) return filename;
  const extension = filename.split('.').pop();
  const name = filename.substring(0, maxLength - 4);
  return `${name}...${extension}`;
};

const ImageGenJobs = ({
  loading,
  error,
  searchTerm,
  setSearchTerm,
  isDarkMode,
  handleFileUpload,
  handleDownload,
  fetchJobs,
  filteredJobs,
  selectedJobs,
  setSelectedJobs,
  handleCreateGoogleSheet,
}) => {
  const theme = useTheme();
  const [sheetModalOpen, setSheetModalOpen] = useState(false);
  const [sheetUrl, setSheetUrl] = useState(null);
  const [sheetError, setSheetError] = useState(null);
  const [isGeneratingSheet, setIsGeneratingSheet] = useState(false);

  const handleSelectAll = (event) => {
    if (event.target.checked) {
      setSelectedJobs(filteredJobs.map((job) => job.id));
    } else {
      setSelectedJobs([]);
    }
  };

  const handleSelectJob = (jobId) => {
    setSelectedJobs((prev) =>
      prev.includes(jobId)
        ? prev.filter((id) => id !== jobId)
        : [...prev, jobId],
    );
  };

  const handleGenerateSheet = async () => {
    try {
      setIsGeneratingSheet(true);
      setSheetError(null);
      const response = await handleCreateGoogleSheet();
      setSheetUrl(response?.data?.sheetUrl);
      setSheetModalOpen(true);
    } catch (err) {
      setSheetError(err.message);
    } finally {
      setIsGeneratingSheet(false);
    }
  };

  return (
    <>
      <Card
        sx={{
          mb: 3,
          backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        }}
      >
        <CardContent>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mb: 2,
            }}
          >
            <Typography
              variant="h6"
              sx={{
                fontWeight: 600,
                color: isDarkMode ? '#fff' : 'inherit',
              }}
            >
              Upload New CSV File
            </Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="contained"
                onClick={handleGenerateSheet}
                disabled={selectedJobs.length === 0 || isGeneratingSheet}
                sx={{
                  backgroundColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  '&:hover': {
                    backgroundColor: isDarkMode ? '#64B5F6' : '#1565C0',
                  },
                }}
              >
                {isGeneratingSheet ? (
                  <CircularProgress size={24} sx={{ color: 'white' }} />
                ) : (
                  'Create Google Sheet'
                )}
              </Button>
              <Button
                component="label"
                variant="contained"
                startIcon={<CloudUpload />}
                sx={{
                  backgroundColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  '&:hover': {
                    backgroundColor: isDarkMode ? '#64B5F6' : '#1565C0',
                  },
                }}
              >
                Upload CSV
                <VisuallyHiddenInput
                  type="file"
                  accept=".csv"
                  onChange={handleFileUpload}
                />
              </Button>
            </Box>
          </Box>
          {error && (
            <Alert
              severity="error"
              sx={{
                mb: 2,
                backgroundColor: isDarkMode
                  ? 'rgba(211, 47, 47, 0.1)'
                  : undefined,
                color: isDarkMode ? '#ff5252' : undefined,
                '& .MuiAlert-icon': {
                  color: isDarkMode ? '#ff5252' : undefined,
                },
              }}
            >
              {error}
            </Alert>
          )}
        </CardContent>
      </Card>

      <Card
        sx={{
          backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        }}
      >
        <CardContent>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mb: 3,
            }}
          >
            <Typography
              variant="h6"
              sx={{
                fontWeight: 600,
                color: isDarkMode ? '#fff' : 'inherit',
              }}
            >
              Image Generation Jobs
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              <TextField
                size="small"
                placeholder="Search jobs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon
                        sx={{
                          color: isDarkMode
                            ? 'rgba(255, 255, 255, 0.7)'
                            : 'inherit',
                        }}
                      />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  width: '250px',
                  '& .MuiOutlinedInput-root': {
                    backgroundColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.05)'
                      : 'inherit',
                    color: isDarkMode ? '#fff' : 'inherit',
                    '& fieldset': {
                      borderColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.23)'
                        : 'inherit',
                    },
                    '&:hover fieldset': {
                      borderColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.4)'
                        : 'inherit',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: isDarkMode
                        ? '#90CAF9'
                        : theme.palette.primary.main,
                    },
                  },
                  '& .MuiInputBase-input::placeholder': {
                    color: isDarkMode ? 'rgba(255, 255, 255, 0.5)' : 'inherit',
                  },
                }}
              />
              <Tooltip title="Refresh jobs">
                <IconButton
                  onClick={fetchJobs}
                  disabled={loading}
                  sx={{
                    color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'inherit',
                    '&:hover': {
                      backgroundColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.08)'
                        : undefined,
                    },
                  }}
                >
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell padding="checkbox">
                    <Checkbox
                      checked={
                        filteredJobs.length > 0 &&
                        selectedJobs.length === filteredJobs.length
                      }
                      indeterminate={
                        selectedJobs.length > 0 &&
                        selectedJobs.length < filteredJobs.length
                      }
                      onChange={handleSelectAll}
                      sx={{
                        color: isDarkMode
                          ? 'rgba(255, 255, 255, 0.7)'
                          : 'inherit',
                      }}
                    />
                  </TableCell>
                  <TableCell
                    sx={{
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.7)'
                        : 'inherit',
                      borderBottomColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.12)'
                        : undefined,
                    }}
                  >
                    Job ID
                  </TableCell>
                  <TableCell
                    sx={{
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.7)'
                        : 'inherit',
                      borderBottomColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.12)'
                        : undefined,
                    }}
                  >
                    Name
                  </TableCell>
                  <TableCell
                    sx={{
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.7)'
                        : 'inherit',
                      borderBottomColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.12)'
                        : undefined,
                    }}
                  >
                    Status
                  </TableCell>
                  <TableCell
                    sx={{
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.7)'
                        : 'inherit',
                      borderBottomColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.12)'
                        : undefined,
                    }}
                  >
                    Created At
                  </TableCell>
                  <TableCell
                    align="right"
                    sx={{
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.7)'
                        : 'inherit',
                      borderBottomColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.12)'
                        : undefined,
                    }}
                  >
                    Actions
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell
                      colSpan={6}
                      align="center"
                      sx={{
                        borderBottomColor: isDarkMode
                          ? 'rgba(255, 255, 255, 0.12)'
                          : undefined,
                      }}
                    >
                      <CircularProgress size={24} sx={{ my: 2 }} />
                    </TableCell>
                  </TableRow>
                ) : filteredJobs.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={6}
                      align="center"
                      sx={{
                        borderBottomColor: isDarkMode
                          ? 'rgba(255, 255, 255, 0.12)'
                          : undefined,
                      }}
                    >
                      <Typography
                        variant="body2"
                        sx={{
                          py: 2,
                          color: isDarkMode
                            ? 'rgba(255, 255, 255, 0.5)'
                            : 'text.secondary',
                        }}
                      >
                        {searchTerm
                          ? 'No matching jobs found'
                          : 'No jobs found'}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredJobs.map((job) => (
                    <TableRow key={job.id}>
                      <TableCell padding="checkbox">
                        <Checkbox
                          checked={selectedJobs.includes(job.id)}
                          onChange={() => handleSelectJob(job.id)}
                          sx={{
                            color: isDarkMode
                              ? 'rgba(255, 255, 255, 0.7)'
                              : 'inherit',
                          }}
                        />
                      </TableCell>
                      <TableCell
                        sx={{
                          color: isDarkMode ? '#fff' : 'inherit',
                          borderBottomColor: isDarkMode
                            ? 'rgba(255, 255, 255, 0.12)'
                            : undefined,
                        }}
                      >
                        {job.id}
                      </TableCell>
                      <TableCell
                        sx={{
                          color: isDarkMode ? '#fff' : 'inherit',
                          borderBottomColor: isDarkMode
                            ? 'rgba(255, 255, 255, 0.12)'
                            : undefined,
                        }}
                      >
                        <Tooltip title={job.name}>
                          <a
                            href={job.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            style={{
                              color: isDarkMode ? '#90CAF9' : '#1976d2',
                              textDecoration: 'none',
                              cursor: 'pointer',
                              '&:hover': {
                                textDecoration: 'underline',
                              },
                            }}
                          >
                            {truncateFilename(job.name)}
                          </a>
                        </Tooltip>
                      </TableCell>
                      <TableCell
                        sx={{
                          borderBottomColor: isDarkMode
                            ? 'rgba(255, 255, 255, 0.12)'
                            : undefined,
                        }}
                      >
                        <Chip
                          label={job.status}
                          color={getStatusColor(job.status)}
                          size="small"
                          sx={{
                            textTransform: 'capitalize',
                            color: isDarkMode ? '#fff' : undefined,
                          }}
                        />
                      </TableCell>
                      <TableCell
                        sx={{
                          color: isDarkMode ? '#fff' : 'inherit',
                          borderBottomColor: isDarkMode
                            ? 'rgba(255, 255, 255, 0.12)'
                            : undefined,
                        }}
                      >
                        {new Date(job.createdAt).toLocaleString()}
                      </TableCell>
                      <TableCell
                        align="right"
                        sx={{
                          borderBottomColor: isDarkMode
                            ? 'rgba(255, 255, 255, 0.12)'
                            : undefined,
                        }}
                      >
                        {job.status === 'COMPLETED' && (
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={<Download />}
                            onClick={() => handleDownload(job.id)}
                            sx={{
                              borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                              color: isDarkMode ? '#90CAF9' : '#1976d2',
                              '&:hover': {
                                borderColor: isDarkMode ? '#64B5F6' : '#1565C0',
                                backgroundColor: isDarkMode
                                  ? 'rgba(144, 202, 249, 0.08)'
                                  : 'rgba(25, 118, 210, 0.08)',
                              },
                            }}
                          >
                            Download
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      <Modal open={sheetModalOpen} onClose={() => setSheetModalOpen(false)}>
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: 400,
            bgcolor: isDarkMode ? '#1a2035' : 'background.paper',
            boxShadow: 24,
            p: 4,
            borderRadius: 2,
            textAlign: 'center',
          }}
        >
          <IconButton
            onClick={() => setSheetModalOpen(false)}
            sx={{
              position: 'absolute',
              top: 8,
              right: 8,
              color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'inherit',
            }}
          >
            <CloseIcon />
          </IconButton>

          {sheetError && (
            <Alert
              severity="error"
              sx={{
                mt: 3,
                backgroundColor: isDarkMode
                  ? 'rgba(211, 47, 47, 0.1)'
                  : undefined,
                color: isDarkMode ? '#ff5252' : undefined,
                '& .MuiAlert-icon': {
                  color: isDarkMode ? '#ff5252' : undefined,
                },
              }}
            >
              {sheetError}
            </Alert>
          )}

          {sheetUrl && (
            <>
              <Typography
                variant="h6"
                gutterBottom
                sx={{
                  color: isDarkMode ? '#fff' : 'inherit',
                }}
              >
                ✅ Sheet Created Successfully!
              </Typography>

              <Button
                variant="contained"
                href={sheetUrl}
                target="_blank"
                sx={{
                  mt: 2,
                  backgroundColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  '&:hover': {
                    backgroundColor: isDarkMode ? '#64B5F6' : '#1565C0',
                  },
                }}
              >
                View Google Sheet
              </Button>
            </>
          )}

          <Alert
            severity="warning"
            sx={{
              mt: 3,
              backgroundColor: isDarkMode
                ? 'rgba(255, 152, 0, 0.1)'
                : undefined,
              color: isDarkMode ? '#ffa726' : undefined,
              '& .MuiAlert-icon': {
                color: isDarkMode ? '#ffa726' : undefined,
              },
            }}
          >
            ⚠️ The sheet URL will not be saved. If you close this window,
            you&apos;ll need to generate a new sheet.
          </Alert>
        </Box>
      </Modal>
    </>
  );
};

export default ImageGenJobs;
