'use client';

import React, { useState } from 'react';
import {
  Button,
  CircularProgress,
  Popover,
  Box,
  Typography,
  Switch,
  FormControlLabel,
  Paper,
} from '@mui/material';
import BusinessIcon from '@mui/icons-material/Business';
import InfoIcon from '@mui/icons-material/Info';

/**
 * Bulk Export Button Component with debug mode toggle
 */
const BulkExportButton = ({
  selectedJobs,
  onBulkExport,
  isLoading = false,
  isDarkMode = false,
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [includeDebug, setIncludeDebug] = useState(false);

  const handleInfoClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleExport = () => {
    onBulkExport(includeDebug);
    handleClose();
  };

  const open = Boolean(anchorEl);

  return (
    <>
      <Button
        startIcon={<BusinessIcon />}
        variant="contained"
        onClick={handleInfoClick}
        disabled={selectedJobs.length === 0 || isLoading}
        sx={{
          backgroundColor: isDarkMode ? '#90CAF9' : '#1976d2',
          '&:hover': {
            backgroundColor: isDarkMode ? '#63B3ED' : '#1565c0',
          },
        }}
      >
        {isLoading ? (
          <>
            <CircularProgress size={16} color="inherit" sx={{ mr: 1 }} />
            Exporting...
          </>
        ) : (
          'Export Selected'
        )}
      </Button>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
      >
        <Paper
          sx={{
            p: 2,
            maxWidth: 320,
            backgroundColor: isDarkMode ? '#2D3748' : '#FFFFFF',
            color: isDarkMode ? '#E2E8F0' : '#4A5568',
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <InfoIcon 
              fontSize="small" 
              sx={{ 
                mr: 1, 
                color: isDarkMode ? '#63B3ED' : '#3182CE' 
              }} 
            />
            <Typography variant="subtitle2" fontWeight={600}>
              Bulk Export Selected Jobs
            </Typography>
          </Box>
          
          <Typography variant="body2" sx={{ mb: 2, lineHeight: 1.5 }}>
            Export {selectedJobs.length} selected job{selectedJobs.length !== 1 ? 's' : ''} as a ZIP file containing CSV files with all lead generation results.
          </Typography>

          <FormControlLabel
            control={
              <Switch
                checked={includeDebug}
                onChange={(e) => setIncludeDebug(e.target.checked)}
                size="small"
                sx={{
                  '& .MuiSwitch-switchBase.Mui-checked': {
                    color: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                  '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                    backgroundColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                }}
              />
            }
            label={
              <Box>
                <Typography variant="body2" fontWeight={500}>
                  Include Debug Data (Raw SERP Output)
                </Typography>
                <Typography variant="body2" fontSize="0.75rem" color="text.secondary">
                  Includes the complete raw SERP API response data in the CSV export for debugging purposes
                </Typography>
                {includeDebug && (
                  <Typography variant="body2" fontSize="0.7rem" sx={{ mt: 0.5, fontStyle: 'italic', color: 'orange' }}>
                    ⚠️ Warning: Debug mode will result in significantly larger file sizes
                  </Typography>
                )}
              </Box>
            }
            sx={{ mb: 2, alignItems: 'flex-start' }}
          />

          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
            <Button
              size="small"
              onClick={handleClose}
              sx={{ color: isDarkMode ? '#A0AEC0' : '#718096' }}
            >
              Cancel
            </Button>
            <Button
              size="small"
              variant="contained"
              onClick={handleExport}
              disabled={isLoading}
              sx={{
                backgroundColor: isDarkMode ? '#90CAF9' : '#1976d2',
                '&:hover': {
                  backgroundColor: isDarkMode ? '#63B3ED' : '#1565c0',
                },
              }}
            >
              {isLoading 
                ? (includeDebug ? 'Generating bulk export with debug data...' : 'Exporting...') 
                : 'Export'
              }
            </Button>
          </Box>
        </Paper>
      </Popover>
    </>
  );
};

export default BulkExportButton;
