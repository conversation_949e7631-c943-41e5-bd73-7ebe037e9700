'use client';

import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  Box,
  Typography,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  CircularProgress,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import { useAdminTheme } from 'views/Jeff/AdminThemeContext';

const BatchUploadForm = ({
  open,
  onClose,
  uploadType,
  setUploadType,
  uploadFile,
  setUploadFile,
  skipErrorRows,
  setSkipErrorRows,
  uploadOperation,
  setUploadOperation,
  onFileChange,
  onUpload,
  isUploading,
}) => {
  const { mode: adminThemeMode } = useAdminTheme() || { mode: 'light' };
  const isDarkMode = adminThemeMode === 'dark';

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="md"
      PaperProps={{
        sx: {
          backgroundColor: isDarkMode ? '#1A202C' : '#FFFFFF',
          backgroundImage: 'none'
        }
      }}
    >
      <DialogTitle sx={{ 
        backgroundColor: isDarkMode ? '#2D3748' : '#F7FAFC',
        color: isDarkMode ? '#E2E8F0' : '#2D3748',
        borderBottom: isDarkMode ? '1px solid rgba(255, 255, 255, 0.1)' : '1px solid rgba(0, 0, 0, 0.1)',
        px: 3,
        py: 2
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6" component="div">
            Upload Leads
          </Typography>
          <IconButton 
            onClick={onClose}
            size="small"
            sx={{ 
              color: isDarkMode ? '#A0AEC0' : '#718096',
              '&:hover': {
                backgroundColor: isDarkMode ? 'rgba(160, 174, 192, 0.1)' : 'rgba(113, 128, 150, 0.1)',
              }
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent sx={{ p: 3 }}>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
              <InputLabel id="upload-type-label" sx={{ 
                color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)'
              }}>
                Upload Type
              </InputLabel>
              <Select
                labelId="upload-type-label"
                id="upload-type-select"
                value={uploadType}
                onChange={(e) => setUploadType(e.target.value)}
                label="Upload Type"
                sx={{
                  color: isDarkMode ? '#E2E8F0' : '#4A5568',
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.23)' : 'rgba(0, 0, 0, 0.23)',
                  },
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.4)' : 'rgba(0, 0, 0, 0.4)',
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                }}
              >
                <MenuItem value="company">Company Data</MenuItem>
                <MenuItem value="person">Person Data</MenuItem>
                <MenuItem value="website">Website URLs</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
              <InputLabel id="upload-operation-label" sx={{ 
                color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)'
              }}>
                Operation
              </InputLabel>
              <Select
                labelId="upload-operation-label"
                id="upload-operation-select"
                value={uploadOperation}
                onChange={(e) => setUploadOperation(e.target.value)}
                label="Operation"
                sx={{
                  color: isDarkMode ? '#E2E8F0' : '#4A5568',
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.23)' : 'rgba(0, 0, 0, 0.23)',
                  },
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.4)' : 'rgba(0, 0, 0, 0.4)',
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                }}
              >
                <MenuItem value="insert">Insert Only</MenuItem>
                <MenuItem value="update">Update Existing</MenuItem>
                <MenuItem value="upsert">Insert & Update</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={skipErrorRows}
                  onChange={(e) => setSkipErrorRows(e.target.checked)}
                  sx={{
                    color: isDarkMode ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.4)',
                    '&.Mui-checked': {
                      color: isDarkMode ? '#90CAF9' : '#1976d2',
                    },
                  }}
                />
              }
              label="Skip rows with errors"
              sx={{
                color: isDarkMode ? '#E2E8F0' : '#4A5568',
              }}
            />
          </Grid>
          
          <Grid item xs={12} sx={{ textAlign: 'center', border: isDarkMode ? '1px dashed rgba(255, 255, 255, 0.2)' : '1px dashed rgba(0, 0, 0, 0.2)', borderRadius: '8px', p: 4 }}>
            <Box component="label" htmlFor="file-upload" sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', cursor: 'pointer' }}>
              <input
                id="file-upload"
                type="file"
                accept=".csv,.xlsx"
                onChange={onFileChange}
                style={{ display: 'none' }}
              />
              <UploadFileIcon 
                sx={{ 
                  fontSize: '3rem', 
                  color: isDarkMode ? '#90CAF9' : '#1976d2',
                  mb: 2,
                  opacity: 0.8
                }} 
              />
              <Typography 
                variant="h6" 
                color={isDarkMode ? '#e2e8f0' : '#334155'} 
                sx={{ mb: 1, fontWeight: 500 }}
              >
                Upload CSV with business data
              </Typography>
              <Typography 
                variant="body2" 
                color={isDarkMode ? 'rgba(226, 232, 240, 0.6)' : 'rgba(51, 65, 85, 0.6)'}
                sx={{ mb: 2, textAlign: 'center' }}
              >
                CSV should include business names, addresses, and other identifying information
              </Typography>
              <Button
                variant="outlined"
                size="small"
                component="span"
                sx={{
                  color: isDarkMode ? '#90CAF9' : '#1976d2',
                  borderColor: isDarkMode ? 'rgba(144, 202, 249, 0.5)' : 'rgba(25, 118, 210, 0.5)',
                  '&:hover': {
                    borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                    backgroundColor: isDarkMode ? 'rgba(144, 202, 249, 0.1)' : 'rgba(25, 118, 210, 0.08)',
                  },
                  fontWeight: 500,
                  textTransform: 'none'
                }}
              >
                Select File
              </Button>
            </Box>
            <Typography 
              variant="caption" 
              color={isDarkMode ? 'rgba(226, 232, 240, 0.6)' : 'rgba(51, 65, 85, 0.6)'} 
              sx={{ mt: 1, display: 'block', fontStyle: 'italic', textAlign: 'center' }}
            >
              Accepted file formats: CSV, XLSX (max size: 5MB)
            </Typography>
          </Grid>

          {uploadFile && (
            <Grid item xs={12}>
              <Box 
                sx={{ 
                  p: 2,
                  borderRadius: '8px',
                  backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.01)',
                  border: isDarkMode ? '1px solid rgba(255, 255, 255, 0.05)' : '1px solid rgba(0, 0, 0, 0.05)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box sx={{ mr: 2 }}>
                    <UploadFileIcon sx={{ color: isDarkMode ? '#90CAF9' : '#1976d2' }} />
                  </Box>
                  <Box>
                    <Typography 
                      variant="body2" 
                      color={isDarkMode ? '#e2e8f0' : '#334155'} 
                      sx={{ fontWeight: 500 }}
                    >
                      {uploadFile.name}
                    </Typography>
                    <Typography 
                      variant="caption" 
                      color={isDarkMode ? 'rgba(226, 232, 240, 0.6)' : 'rgba(51, 65, 85, 0.6)'}
                    >
                      {(uploadFile.size / 1024).toFixed(2)} KB
                    </Typography>
                  </Box>
                </Box>
                <IconButton 
                  onClick={() => setUploadFile(null)} 
                  size="small"
                  sx={{ 
                    color: isDarkMode ? '#FC8181' : '#E53E3E',
                    '&:hover': {
                      backgroundColor: isDarkMode ? 'rgba(252, 129, 129, 0.1)' : 'rgba(229, 62, 62, 0.1)',
                    }
                  }}
                >
                  <CloseIcon />
                </IconButton>
              </Box>
            </Grid>
          )}
        </Grid>
      </DialogContent>
      <DialogActions sx={{ 
        px: 3, 
        py: 2,
        backgroundColor: isDarkMode ? '#2D3748' : '#F7FAFC',
        borderTop: isDarkMode ? '1px solid rgba(255, 255, 255, 0.1)' : '1px solid rgba(0, 0, 0, 0.1)',
      }}>
        <Button 
          onClick={onClose} 
          sx={{ 
            color: isDarkMode ? '#E2E8F0' : '#4A5568',
            '&:hover': {
              backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
            }
          }}
        >
          Cancel
        </Button>
        <Button 
          onClick={onUpload}
          disabled={!uploadFile || isUploading}
          variant="contained"
          startIcon={isUploading ? <CircularProgress size={20} color="inherit" /> : null}
          sx={{
            backgroundColor: isDarkMode ? '#2B6CB0' : '#3182CE',
            color: '#FFFFFF',
            '&:hover': {
              backgroundColor: isDarkMode ? '#4299E1' : '#2C5282',
            },
            '&:disabled': {
              backgroundColor: isDarkMode ? 'rgba(44, 82, 130, 0.5)' : 'rgba(49, 130, 206, 0.5)',
              color: '#FFFFFF',
            }
          }}
        >
          {isUploading ? 'Uploading...' : 'Upload and Process'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default BatchUploadForm;