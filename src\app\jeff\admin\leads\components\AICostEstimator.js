'use client';

import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Divider,
  LinearProgress,
  Chip,
  Alert,
} from '@mui/material';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import SpeedIcon from '@mui/icons-material/Speed';
import { useAdminTheme } from 'views/Jeff/AdminThemeContext';

const AICostEstimator = ({
  selectedModel,
  promptLength = 0,
  csvRowCount = 0,
  showDetailedBreakdown = true,
}) => {
  const { mode: adminThemeMode } = useAdminTheme() || { mode: 'light' };
  const isDarkMode = adminThemeMode === 'dark';

  // Model configurations
  const modelConfigs = {
    openai: {
      name: 'OpenAI GPT-4',
      costPer1k: 0.03,
      tokensPerSecond: 50,
      maxTokens: 8192,
      color: '#10a37f',
    },
    claude: {
      name: 'Claude 3.5 Sonnet',
      costPer1k: 0.025,
      tokensPerSecond: 60,
      maxTokens: 8192,
      color: '#d97706',
    },
    gemini: {
      name: 'Gemini Pro',
      costPer1k: 0.02,
      tokensPerSecond: 45,
      maxTokens: 8192,
      color: '#4285f4',
    },
  };

  const currentModel = modelConfigs[selectedModel];

  // Calculate estimations
  const calculateEstimations = () => {
    if (!currentModel || !csvRowCount || !promptLength) {
      return null;
    }

    // Rough token estimation (4 characters per token)
    const promptTokens = Math.ceil(promptLength / 4);
    const responseTokensPerRow = 150; // Estimated response tokens per lead
    const totalTokensPerRow = promptTokens + responseTokensPerRow;
    const totalTokens = totalTokensPerRow * csvRowCount;
    
    // Cost calculations
    const totalCost = (totalTokens / 1000) * currentModel.costPer1k;
    const costPerRow = totalCost / csvRowCount;
    
    // Time estimation
    const estimatedTimeSeconds = totalTokens / currentModel.tokensPerSecond;
    const estimatedTimeMinutes = estimatedTimeSeconds / 60;
    
    return {
      promptTokens,
      responseTokensPerRow,
      totalTokensPerRow,
      totalTokens,
      totalCost,
      costPerRow,
      estimatedTimeSeconds,
      estimatedTimeMinutes,
    };
  };

  const estimations = calculateEstimations();

  // Cost level indicator
  const getCostLevel = (totalCost) => {
    if (totalCost < 1) return { level: 'low', color: '#4caf50', label: 'Low Cost' };
    if (totalCost < 10) return { level: 'medium', color: '#ff9800', label: 'Medium Cost' };
    return { level: 'high', color: '#f44336', label: 'High Cost' };
  };

  const formatTime = (minutes) => {
    if (minutes < 1) return `${Math.round(minutes * 60)}s`;
    if (minutes < 60) return `${Math.round(minutes)}m`;
    return `${Math.round(minutes / 60)}h ${Math.round(minutes % 60)}m`;
  };

  if (!selectedModel || !currentModel) {
    return (
      <Paper
        sx={{
          p: 3,
          mb: 2,
          backgroundColor: isDarkMode ? '#2D3748' : '#FFFFFF',
          border: `1px solid ${isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'}`,
          borderRadius: '8px',
        }}
      >
        <Typography
          variant="body2"
          sx={{
            color: isDarkMode ? 'rgba(255,255,255,0.6)' : 'rgba(0,0,0,0.5)',
            textAlign: 'center',
            fontStyle: 'italic',
          }}
        >
          Select an AI model to see cost estimation
        </Typography>
      </Paper>
    );
  }

  if (!estimations) {
    return (
      <Paper
        sx={{
          p: 3,
          mb: 2,
          backgroundColor: isDarkMode ? '#2D3748' : '#FFFFFF',
          border: `1px solid ${isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'}`,
          borderRadius: '8px',
        }}
      >
        <Typography
          variant="body2"
          sx={{
            color: isDarkMode ? 'rgba(255,255,255,0.6)' : 'rgba(0,0,0,0.5)',
            textAlign: 'center',
            fontStyle: 'italic',
          }}
        >
          Upload a CSV file and configure your prompt to see cost estimation
        </Typography>
      </Paper>
    );
  }

  const costLevel = getCostLevel(estimations.totalCost);

  return (
    <Paper
      sx={{
        p: 3,
        mb: 2,
        backgroundColor: isDarkMode ? '#2D3748' : '#FFFFFF',
        border: `1px solid ${isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'}`,
        borderRadius: '8px',
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <MonetizationOnIcon
          sx={{
            color: currentModel.color,
            mr: 1,
          }}
        />
        <Typography
          variant="h6"
          sx={{
            fontWeight: 600,
            color: isDarkMode ? '#E2E8F0' : '#4A5568',
          }}
        >
          Cost Estimation
        </Typography>
        <Chip
          label={costLevel.label}
          size="small"
          sx={{
            ml: 'auto',
            backgroundColor: costLevel.color,
            color: 'white',
            fontWeight: 500,
          }}
        />
      </Box>

      {/* Main Cost Display */}
      <Box sx={{ textAlign: 'center', mb: 3 }}>
        <Typography
          variant="h4"
          sx={{
            fontWeight: 700,
            color: currentModel.color,
            mb: 1,
          }}
        >
          ${estimations.totalCost.toFixed(4)}
        </Typography>
        <Typography
          variant="body2"
          sx={{
            color: isDarkMode ? 'rgba(255,255,255,0.7)' : 'rgba(0,0,0,0.6)',
          }}
        >
          Total estimated cost for {csvRowCount.toLocaleString()} leads
        </Typography>
      </Box>

      {/* Quick Stats */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={4}>
          <Box sx={{ textAlign: 'center' }}>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 600,
                color: isDarkMode ? '#E2E8F0' : '#4A5568',
              }}
            >
              ${estimations.costPerRow.toFixed(4)}
            </Typography>
            <Typography
              variant="caption"
              sx={{
                color: isDarkMode ? 'rgba(255,255,255,0.6)' : 'rgba(0,0,0,0.5)',
              }}
            >
              Per Lead
            </Typography>
          </Box>
        </Grid>
        <Grid item xs={4}>
          <Box sx={{ textAlign: 'center' }}>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 600,
                color: isDarkMode ? '#E2E8F0' : '#4A5568',
              }}
            >
              {estimations.totalTokens.toLocaleString()}
            </Typography>
            <Typography
              variant="caption"
              sx={{
                color: isDarkMode ? 'rgba(255,255,255,0.6)' : 'rgba(0,0,0,0.5)',
              }}
            >
              Total Tokens
            </Typography>
          </Box>
        </Grid>
        <Grid item xs={4}>
          <Box sx={{ textAlign: 'center' }}>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 600,
                color: isDarkMode ? '#E2E8F0' : '#4A5568',
              }}
            >
              {formatTime(estimations.estimatedTimeMinutes)}
            </Typography>
            <Typography
              variant="caption"
              sx={{
                color: isDarkMode ? 'rgba(255,255,255,0.6)' : 'rgba(0,0,0,0.5)',
              }}
            >
              Est. Time
            </Typography>
          </Box>
        </Grid>
      </Grid>

      {showDetailedBreakdown && (
        <>
          <Divider sx={{ my: 2 }} />
          
          {/* Detailed Breakdown */}
          <Typography
            variant="subtitle2"
            sx={{
              fontWeight: 600,
              color: isDarkMode ? '#E2E8F0' : '#4A5568',
              mb: 2,
            }}
          >
            Breakdown
          </Typography>
          
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Typography
                variant="body2"
                sx={{
                  color: isDarkMode ? 'rgba(255,255,255,0.8)' : 'rgba(0,0,0,0.7)',
                  mb: 0.5,
                }}
              >
                Prompt tokens: {estimations.promptTokens.toLocaleString()}
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: isDarkMode ? 'rgba(255,255,255,0.8)' : 'rgba(0,0,0,0.7)',
                  mb: 0.5,
                }}
              >
                Response tokens per lead: ~{estimations.responseTokensPerRow}
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: isDarkMode ? 'rgba(255,255,255,0.8)' : 'rgba(0,0,0,0.7)',
                }}
              >
                Total tokens per lead: ~{estimations.totalTokensPerRow}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography
                variant="body2"
                sx={{
                  color: isDarkMode ? 'rgba(255,255,255,0.8)' : 'rgba(0,0,0,0.7)',
                  mb: 0.5,
                }}
              >
                Model: {currentModel.name}
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: isDarkMode ? 'rgba(255,255,255,0.8)' : 'rgba(0,0,0,0.7)',
                  mb: 0.5,
                }}
              >
                Rate: ${currentModel.costPer1k}/1K tokens
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: isDarkMode ? 'rgba(255,255,255,0.8)' : 'rgba(0,0,0,0.7)',
                }}
              >
                Speed: ~{currentModel.tokensPerSecond} tokens/sec
              </Typography>
            </Grid>
          </Grid>
        </>
      )}

      {/* Warning for high costs */}
      {costLevel.level === 'high' && (
        <Alert
          severity="warning"
          sx={{
            mt: 2,
            backgroundColor: isDarkMode ? 'rgba(255, 152, 0, 0.1)' : 'rgba(255, 152, 0, 0.1)',
            color: isDarkMode ? '#ffb74d' : '#e65100',
          }}
        >
          High cost detected. Consider reducing the number of leads or optimizing your prompt.
        </Alert>
      )}
    </Paper>
  );
};

export default AICostEstimator;
