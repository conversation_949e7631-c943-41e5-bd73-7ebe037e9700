'use client';

import React from 'react';
import {
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Chip,
  Avatar,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ScheduleIcon from '@mui/icons-material/Schedule';
import { useAdminTheme } from 'views/Jeff/AdminThemeContext';

const AIModelSelector = ({ 
  selectedModel, 
  onModelChange, 
  disabled = false,
  showDescription = true 
}) => {
  const { mode: adminThemeMode } = useAdminTheme() || { mode: 'light' };
  const isDarkMode = adminThemeMode === 'dark';

  const models = [
    {
      id: 'openai',
      name: 'OpenAI GPT-4',
      provider: 'OpenAI',
      status: 'active',
      description: 'Advanced language model with excellent website analysis capabilities',
      costPer1k: 0.03,
      avatar: '🤖',
      color: '#10a37f',
    },
    
  ];

  const getStatusChip = (status) => {
    switch (status) {
      case 'active':
        return (
          <Chip
            icon={<CheckCircleIcon sx={{ fontSize: 16 }} />}
            label="Active"
            size="small"
            sx={{
              backgroundColor: '#4caf50',
              color: 'white',
              height: 20,
              fontSize: '0.7rem',
            }}
          />
        );
      case 'coming_soon':
        return (
          <Chip
            icon={<ScheduleIcon sx={{ fontSize: 16 }} />}
            label="Coming Soon"
            size="small"
            sx={{
              backgroundColor: '#ff9800',
              color: 'white',
              height: 20,
              fontSize: '0.7rem',
            }}
          />
        );
      default:
        return null;
    }
  };

  const selectedModelData = models.find(model => model.id === selectedModel);

  return (
    <Box sx={{ mb: 2 }}>
      <FormControl 
        fullWidth 
        disabled={disabled}
        sx={{
          '& .MuiOutlinedInput-root': {
            color: isDarkMode ? '#e2e8f0' : '#4a5568',
            '& fieldset': {
              borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.23)' : 'rgba(0, 0, 0, 0.23)',
            },
            '&:hover fieldset': {
              borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.4)' : 'rgba(0, 0, 0, 0.4)',
            },
            '&.Mui-focused fieldset': {
              borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
            },
          },
          '& .MuiInputLabel-root': {
            color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)',
          },
        }}
      >
        <InputLabel id="ai-model-select-label">AI Model</InputLabel>
        <Select
          labelId="ai-model-select-label"
          id="ai-model-select"
          value={selectedModel || ''}
          label="AI Model"
          onChange={(e) => onModelChange(e.target.value)}
          sx={{
            '& .MuiSelect-select': {
              display: 'flex',
              alignItems: 'center',
              gap: 1,
            },
          }}
        >
          {models.map((model) => (
            <MenuItem 
              key={model.id} 
              value={model.id}
              disabled={model.status !== 'active'}
              sx={{
                opacity: model.status === 'active' ? 1 : 0.6,
                '&.Mui-disabled': {
                  opacity: 0.5,
                },
              }}
            >
              <ListItemIcon sx={{ minWidth: 40 }}>
                <Avatar
                  sx={{
                    width: 24,
                    height: 24,
                    fontSize: '0.8rem',
                    backgroundColor: model.color,
                  }}
                >
                  {model.avatar}
                </Avatar>
              </ListItemIcon>
              <ListItemText
                primary={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="body2" fontWeight={500}>
                      {model.name}
                    </Typography>
                    {getStatusChip(model.status)}
                  </Box>
                }
                secondary={
                  <Typography variant="caption" color="textSecondary">
                    {model.provider} • ${model.costPer1k}/1K tokens
                  </Typography>
                }
              />
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {showDescription && selectedModelData && (
        <Box
          sx={{
            mt: 2,
            p: 2,
            backgroundColor: isDarkMode ? '#1A202C' : '#F7FAFC',
            borderRadius: '6px',
            border: `1px solid ${isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'}`,
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Avatar
              sx={{
                width: 20,
                height: 20,
                fontSize: '0.7rem',
                backgroundColor: selectedModelData.color,
                mr: 1,
              }}
            >
              {selectedModelData.avatar}
            </Avatar>
            <Typography
              variant="subtitle2"
              sx={{
                fontWeight: 600,
                color: isDarkMode ? '#E2E8F0' : '#4A5568',
              }}
            >
              {selectedModelData.name}
            </Typography>
            <Box sx={{ ml: 'auto' }}>
              {getStatusChip(selectedModelData.status)}
            </Box>
          </Box>
          <Typography
            variant="body2"
            sx={{
              color: isDarkMode ? 'rgba(255,255,255,0.8)' : 'rgba(0,0,0,0.7)',
              lineHeight: 1.4,
            }}
          >
            {selectedModelData.description}
          </Typography>
          <Typography
            variant="caption"
            sx={{
              color: isDarkMode ? 'rgba(255,255,255,0.6)' : 'rgba(0,0,0,0.5)',
              mt: 1,
              display: 'block',
            }}
          >
            Cost: ${selectedModelData.costPer1k} per 1,000 tokens
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default AIModelSelector;
