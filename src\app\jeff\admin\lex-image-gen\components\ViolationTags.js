import React, { useState } from 'react';
import {
  Box,
  Button,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  //  IconButton,
  //  Tooltip,
} from '@mui/material';
import {
  //  Add as AddIcon,
  //  Delete as DeleteIcon,
  Image as ImageIcon,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

const VisuallyHiddenInput = styled('input')`
  clip: rect(0 0 0 0);
  clip-path: inset(50%);
  height: 1px;
  overflow: hidden;
  position: absolute;
  bottom: 0;
  left: 0;
  white-space: nowrap;
  width: 1px;
`;

const ViolationTags = ({
  tags,
  loading,
  error,
  isDarkMode,
  handleAddTag,
  //  fetchTags,
}) => {
  const [openDialog, setOpenDialog] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    tagImage: null,
  });
  const [previewUrl, setPreviewUrl] = useState(null);

  //  const handleOpenDialog = () => {
  //    setOpenDialog(true);
  //  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setFormData({
      name: '',
      description: '',
      tagImage: null,
    });
    setPreviewUrl(null);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFormData((prev) => ({
        ...prev,
        tagImage: file,
      }));
      setPreviewUrl(URL.createObjectURL(file));
    }
  };

  const handleSubmit = async () => {
    const formDataToSend = new FormData();
    formDataToSend.append('name', formData.name);
    formDataToSend.append('description', formData.description);
    formDataToSend.append('tagImage', formData.tagImage);

    await handleAddTag(formDataToSend);
    handleCloseDialog();
  };

  return (
    <>
      <Card
        sx={{
          mb: 3,
          backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        }}
      >
        <CardContent>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mb: 2,
            }}
          >
            <Typography
              variant="h6"
              sx={{
                fontWeight: 600,
                color: isDarkMode ? '#fff' : 'inherit',
              }}
            >
              Violation Tags
            </Typography>
            {/* <Button
                            variant="contained"
                            startIcon={<AddIcon />}
                            onClick={handleOpenDialog}
                            sx={{
                                backgroundColor: isDarkMode ? '#90CAF9' : '#1976d2',
                                '&:hover': {
                                    backgroundColor: isDarkMode ? '#64B5F6' : '#1565C0',
                                },
                            }}
                        >
                            Add Tag
                        </Button> */}
          </Box>
          {error && (
            <Alert
              severity="error"
              sx={{
                mb: 2,
                backgroundColor: isDarkMode
                  ? 'rgba(211, 47, 47, 0.1)'
                  : undefined,
                color: isDarkMode ? '#ff5252' : undefined,
                '& .MuiAlert-icon': {
                  color: isDarkMode ? '#ff5252' : undefined,
                },
              }}
            >
              {error}
            </Alert>
          )}
        </CardContent>
      </Card>

      <Card
        sx={{
          backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        }}
      >
        <CardContent>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell
                    sx={{
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.7)'
                        : 'inherit',
                      borderBottomColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.12)'
                        : undefined,
                    }}
                  >
                    Name
                  </TableCell>
                  <TableCell
                    sx={{
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.7)'
                        : 'inherit',
                      borderBottomColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.12)'
                        : undefined,
                    }}
                  >
                    Description
                  </TableCell>
                  <TableCell
                    sx={{
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.7)'
                        : 'inherit',
                      borderBottomColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.12)'
                        : undefined,
                    }}
                  >
                    Image
                  </TableCell>
                  {/* <TableCell
                                        align="right"
                                        sx={{
                                            color: isDarkMode
                                                ? 'rgba(255, 255, 255, 0.7)'
                                                : 'inherit',
                                            borderBottomColor: isDarkMode
                                                ? 'rgba(255, 255, 255, 0.12)'
                                                : undefined,
                                        }}
                                    >
                                        Actions
                                    </TableCell> */}
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell
                      colSpan={4}
                      align="center"
                      sx={{
                        borderBottomColor: isDarkMode
                          ? 'rgba(255, 255, 255, 0.12)'
                          : undefined,
                      }}
                    >
                      <CircularProgress size={24} sx={{ my: 2 }} />
                    </TableCell>
                  </TableRow>
                ) : tags.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={4}
                      align="center"
                      sx={{
                        borderBottomColor: isDarkMode
                          ? 'rgba(255, 255, 255, 0.12)'
                          : undefined,
                      }}
                    >
                      <Typography
                        variant="body2"
                        sx={{
                          py: 2,
                          color: isDarkMode
                            ? 'rgba(255, 255, 255, 0.5)'
                            : 'text.secondary',
                        }}
                      >
                        No violation tags found
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  tags.map((tag) => (
                    <TableRow key={tag.id}>
                      <TableCell
                        sx={{
                          color: isDarkMode ? '#fff' : 'inherit',
                          borderBottomColor: isDarkMode
                            ? 'rgba(255, 255, 255, 0.12)'
                            : undefined,
                        }}
                      >
                        {tag.name}
                      </TableCell>
                      <TableCell
                        sx={{
                          color: isDarkMode ? '#fff' : 'inherit',
                          borderBottomColor: isDarkMode
                            ? 'rgba(255, 255, 255, 0.12)'
                            : undefined,
                        }}
                      >
                        {tag.description}
                      </TableCell>
                      <TableCell
                        sx={{
                          color: isDarkMode ? '#fff' : 'inherit',
                          borderBottomColor: isDarkMode
                            ? 'rgba(255, 255, 255, 0.12)'
                            : undefined,
                        }}
                      >
                        <a
                          href={tag.imageUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          style={{
                            display: 'inline-block',
                            cursor: 'pointer',
                          }}
                        >
                          <img
                            src={tag.imageUrl}
                            alt={tag.name}
                            style={{
                              width: '100px',
                              height: '100px',
                              objectFit: 'cover',
                              borderRadius: '4px',
                              transition: 'transform 0.2s',
                            }}
                            onMouseOver={(e) => {
                              e.currentTarget.style.transform = 'scale(1.1)';
                            }}
                            onMouseOut={(e) => {
                              e.currentTarget.style.transform = 'scale(1)';
                            }}
                          />
                        </a>
                      </TableCell>
                      {/* <TableCell
                                                align="right"
                                                sx={{
                                                    borderBottomColor: isDarkMode
                                                        ? 'rgba(255, 255, 255, 0.12)'
                                                        : undefined,
                                                }}
                                            >
                                                <Tooltip title="Delete tag">
                                                    <IconButton
                                                        size="small"
                                                        sx={{
                                                            color: isDarkMode
                                                                ? 'rgba(255, 255, 255, 0.7)'
                                                                : 'inherit',
                                                            '&:hover': {
                                                                color: '#f44336',
                                                            },
                                                        }}
                                                    >
                                                        <DeleteIcon />
                                                    </IconButton>
                                                </Tooltip>
                                            </TableCell> */}
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Add New Violation Tag</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                multiline
                rows={3}
              />
            </Grid>
            <Grid item xs={12}>
              <Button
                component="label"
                variant="outlined"
                startIcon={<ImageIcon />}
                fullWidth
              >
                Upload Image
                <VisuallyHiddenInput
                  type="file"
                  accept="image/*"
                  onChange={handleImageChange}
                />
              </Button>
              {previewUrl && (
                <Box sx={{ mt: 2, textAlign: 'center' }}>
                  <img
                    src={previewUrl}
                    alt="Preview"
                    style={{
                      maxWidth: '100%',
                      maxHeight: '200px',
                      objectFit: 'contain',
                    }}
                  />
                </Box>
              )}
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={!formData.name || !formData.tagImage}
          >
            Add Tag
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ViolationTags;
