import React from 'react';
import GigaBrains from 'views/GigaBrains';
import axios from 'axios';
import { API_ENDPOINTS } from 'config/api';

const Page = async ({ params }) => {
  let data = null;

  try {
    const response = await axios.get(
      `https://api.hunter.equalcollective.com${API_ENDPOINTS.AMAZON_AUDIT_REPORT}/${params.companyName}`,
    );

    data = await response.data;
  } catch (error) {
    console.error('Failed to fetch data:>>>>>>>>>>>>>>>>>>>>>', error);
  }

  return <GigaBrains slug={params.companyName} companyData={data} />;
};

export default Page;
