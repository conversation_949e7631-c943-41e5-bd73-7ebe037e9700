'use client';

import React, { useState, useEffect } from 'react';
import {
  Container,
  Grid,
} from '@mui/material';
import JeffAdminPanel from 'views/Jeff/JeffAdminPanel';
import { useUser } from 'views/Jeff/Utils/getUser';
import { useCampaigns, useCreateCampaign } from 'views/Jeff/Utils/campaigns';
import { useRouter } from 'next/navigation';
import { useAdminTheme } from 'views/Jeff/AdminThemeContext';
import { useQueryClient } from '@tanstack/react-query';

// Import our custom components
import CampaignForm from './components/CampaignForm';
import CampaignSearch from './components/CampaignSearch';
import CampaignsTable from './components/CampaignsTable';
import Notification from 'components/Notification';

// Import utils
import { filterCampaigns, calculateCampaignStats } from './utils/formHandlers';

const CampaignsPage = () => {
  const { data: user, isLoading: isUserLoading } = useUser();
  const { mutateAsync, isPending } = useCreateCampaign();
  const { data: campaignsData, isLoading: isCampaignsLoading, refetch } = useCampaigns();
  const router = useRouter();
  const { mode: adminThemeMode } = useAdminTheme() || { mode: 'light' };
  const isDarkMode = adminThemeMode === 'dark';
  const queryClient = useQueryClient();

  // State management
  const [searchTerm, setSearchTerm] = useState('');
  const [snackBar, setSnackBar] = useState({
    isOpen: false,
    message: '',
    severity: 'success',
  });

  useEffect(() => {
    if (!isUserLoading && !user) {
      router.push('/jeff/login');
    } else if (!isUserLoading && user && user?.userType && user?.userType !== 'admin') {
      router.push('/jeff/dashboard');
    }
  }, [isUserLoading, user, router]);

  const handleCreateCampaign = async (event) => {
    event.preventDefault();
    const form = event.currentTarget;
    const data = new FormData(form);

    try {
      await mutateAsync(data.get('campaign'));
      form.reset();
      
      // Invalidate and refetch campaigns
      queryClient.invalidateQueries(['jeff-campaigns']);
      await refetch();
      
      setSnackBar({
        isOpen: true,
        message: 'Campaign added successfully!',
        severity: 'success',
      });
    } catch (error) {
      setSnackBar({
        isOpen: true,
        message: 'Something went wrong!',
        severity: 'error',
      });
      console.error(error);
    }
  };

  // Filter campaigns using our utility function
  const filteredCampaigns = filterCampaigns(campaignsData?.campaigns, searchTerm);
  
  // Calculate campaign stats
  const campaignStats = calculateCampaignStats(filteredCampaigns);

  if (isUserLoading) {
    return <div>Loading...</div>;
  }

  return (
    <JeffAdminPanel title="Campaign Management">
      <Container maxWidth="lg" sx={{ 
        py: 4,
        backgroundColor: isDarkMode ? '#1A202C' : 'inherit',
        color: isDarkMode ? '#E2E8F0' : 'inherit' 
      }}>
        <Grid container spacing={3} mb={6}>
          <Grid item xs={12} md={6}>
            <CampaignForm 
              onSubmit={handleCreateCampaign}
              isPending={isPending}
              campaignStats={campaignStats}
              isDarkMode={isDarkMode}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <CampaignSearch 
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              isDarkMode={isDarkMode}
            />
          </Grid>
        </Grid>

        <CampaignsTable 
          campaigns={filteredCampaigns}
          isLoading={isCampaignsLoading}
          searchTerm={searchTerm}
          isDarkMode={isDarkMode}
        />
      </Container>

      <Notification 
        snackBar={snackBar}
        setSnackBar={setSnackBar}
        isDarkMode={isDarkMode}
      />
    </JeffAdminPanel>
  );
};

export default CampaignsPage; 