'use client';

import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  List,
  ListItem,
  ListItemText,
  Link,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import LinkIcon from '@mui/icons-material/Link';
import BugReportIcon from '@mui/icons-material/BugReport';

/**
 * Dialog component to display Google Sheets export links results
 */
const ExportLinksResultDialog = ({
  open,
  onClose,
  data,
  jobId,
  includeDebug = false,
  isDarkMode = false,
}) => {
  if (!data) return null;

  // Handle both API response format and existing resultJson format
  let links = [];
  let debug = null;
  let message = '';
  let stats = {};
  
  if (data.googleSheet) {
    // This is existing resultJson format
    links = [{
      url: data.googleSheet.url,
      title: 'Google Sheets Export',
      description: `${data.googleSheet.sheets?.length || 0} sheets available`,
      sheets: data.googleSheet.sheets
    }];
    message = data.message || 'Existing Google Sheets links';
    stats = data.stats || {};
    debug = data.debug || null;
  } else {
    // This is API response format
    links = data.links || [];
    debug = data.debug || null;
    message = data.message || '';
    stats = data.stats || {};
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          backgroundColor: isDarkMode ? '#2D3748' : '#FFFFFF',
          color: isDarkMode ? '#E2E8F0' : '#4A5568',
        }
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: isDarkMode ? '#1A202C' : '#F7FAFC',
          borderBottom: `1px solid ${isDarkMode ? '#4A5568' : '#E2E8F0'}`,
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <LinkIcon sx={{ mr: 1, color: isDarkMode ? '#68D391' : '#48BB78' }} />
          <Typography variant="h6">
            Google Sheets Links - Job #{jobId}
          </Typography>
        </Box>
        <Button
          onClick={onClose}
          size="small"
          sx={{ minWidth: 'auto', p: 0.5 }}
        >
          <CloseIcon />
        </Button>
      </DialogTitle>

      <DialogContent sx={{ p: 3 }}>
        {message && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="body1" color="success.main">
              {message}
            </Typography>
          </Box>
        )}

        {/* Statistics */}
        {Object.keys(stats).length > 0 && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1 }}>
              Export Statistics
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              {Object.entries(stats).map(([key, value]) => (
                <Chip
                  key={key}
                  label={`${key}: ${value}`}
                  size="small"
                  variant="outlined"
                  sx={{
                    borderColor: isDarkMode ? '#4A5568' : '#E2E8F0',
                    color: isDarkMode ? '#E2E8F0' : '#4A5568',
                  }}
                />
              ))}
            </Box>
          </Box>
        )}

        {/* Links List */}
        {links && links.length > 0 && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 2 }}>
              Generated Google Sheets ({links.length})
            </Typography>
            <List sx={{ p: 0 }}>
              {links.map((linkItem, index) => (
                <ListItem
                  key={index}
                  sx={{
                    border: `1px solid ${isDarkMode ? '#4A5568' : '#E2E8F0'}`,
                    borderRadius: 1,
                    mb: 1,
                    backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)',
                  }}
                >
                  <ListItemText
                    primary={
                      <Link
                        href={linkItem.url || linkItem}
                        target="_blank"
                        rel="noopener noreferrer"
                        sx={{
                          color: isDarkMode ? '#63B3ED' : '#3182CE',
                          textDecoration: 'none',
                          '&:hover': { textDecoration: 'underline' },
                          display: 'flex',
                          alignItems: 'center',
                        }}
                      >
                        <LinkIcon fontSize="small" sx={{ mr: 1 }} />
                        {linkItem.title || linkItem.name || `Sheet ${index + 1}`}
                      </Link>
                    }
                    secondary={
                      <Box>
                        {linkItem.description && (
                          <Typography variant="body2" sx={{ mb: 0.5 }}>
                            {linkItem.description}
                          </Typography>
                        )}
                        {linkItem.sheets?.length > 0 && (
                          <Box>
                            <Typography variant="body2" fontWeight={500} sx={{ mb: 0.5 }}>
                              Sheets ({linkItem.sheets.length}):
                            </Typography>
                            {linkItem.sheets.map((sheet, sheetIndex) => (
                              <Typography 
                                key={sheetIndex} 
                                variant="body2" 
                                fontSize="0.75rem"
                                sx={{ 
                                  color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)',
                                  ml: 1
                                }}
                              >
                                • {sheet.name}: {sheet.rows} rows
                              </Typography>
                            ))}
                          </Box>
                        )}
                      </Box>
                    }
                  />
                </ListItem>
              ))}
            </List>
          </Box>
        )}

        {/* Debug Information */}
        {includeDebug && debug && (
          <Box sx={{ mb: 2 }}>
            <Accordion
              sx={{
                backgroundColor: isDarkMode ? '#1A202C' : '#F7FAFC',
                '&:before': { display: 'none' },
              }}
            >
              <AccordionSummary
                expandIcon={<ExpandMoreIcon />}
                sx={{
                  backgroundColor: isDarkMode ? '#2D3748' : '#FFFFFF',
                  borderRadius: 1,
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <BugReportIcon 
                    fontSize="small" 
                    sx={{ mr: 1, color: isDarkMode ? '#F6E05E' : '#ECC94B' }} 
                  />
                  <Typography variant="subtitle2">Debug Information</Typography>
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <Box
                  component="pre"
                  sx={{
                    backgroundColor: isDarkMode ? '#1A202C' : '#F7FAFC',
                    p: 2,
                    borderRadius: 1,
                    overflow: 'auto',
                    fontSize: '0.75rem',
                    fontFamily: 'monospace',
                    whiteSpace: 'pre-wrap',
                    maxHeight: 300,
                    border: `1px solid ${isDarkMode ? '#4A5568' : '#E2E8F0'}`,
                  }}
                >
                  {typeof debug === 'string' ? debug : JSON.stringify(debug, null, 2)}
                </Box>
              </AccordionDetails>
            </Accordion>
          </Box>
        )}

        {/* Empty State */}
        {(!links || links.length === 0) && (
          <Box
            sx={{
              textAlign: 'center',
              py: 4,
              color: isDarkMode ? '#A0AEC0' : '#718096',
            }}
          >
            <LinkIcon sx={{ fontSize: 48, mb: 2, opacity: 0.5 }} />
            <Typography variant="body1">
              No Google Sheets links were generated
            </Typography>
            <Typography variant="body2" sx={{ mt: 1 }}>
              The job may not have completed or there might be no data to export
            </Typography>
          </Box>
        )}
      </DialogContent>

      <DialogActions
        sx={{
          backgroundColor: isDarkMode ? '#1A202C' : '#F7FAFC',
          borderTop: `1px solid ${isDarkMode ? '#4A5568' : '#E2E8F0'}`,
          p: 2,
        }}
      >
        <Button
          onClick={onClose}
          variant="contained"
          sx={{
            backgroundColor: isDarkMode ? '#90CAF9' : '#1976d2',
            '&:hover': {
              backgroundColor: isDarkMode ? '#63B3ED' : '#1565c0',
            },
          }}
        >
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ExportLinksResultDialog; 