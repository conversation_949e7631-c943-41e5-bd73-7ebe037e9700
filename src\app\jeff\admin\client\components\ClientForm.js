import React, { useState } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  Grid,
  TextField,
  Typography,
  InputAdornment,
  IconButton,
  useTheme,
} from '@mui/material';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';

const ClientForm = ({ onSubmit, isCreating, isDarkMode }) => {
  const theme = useTheme();
  const [showPassword, setShowPassword] = useState(false);

  const handleSubmit = async (event) => {
    event.preventDefault();
    const form = event.currentTarget;
    const data = new FormData(form);

    const clientData = {
      email: data.get('email'),
      password: data.get('password'),
      name: data.get('name'),
    };

    try {
      await onSubmit(clientData);
      form.reset();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <Box>
      <Typography
        variant="h4"
        mb={2}
        color={isDarkMode ? '#90CAF9' : '#1976d2'}
        sx={{
          fontWeight: 600,
          letterSpacing: '0.25px',
          textShadow: isDarkMode ? '0px 1px 2px rgba(0,0,0,0.3)' : 'none',
        }}
      >
        Add New Client
      </Typography>
      <Card
        elevation={isDarkMode ? 2 : 1}
        sx={{
          borderColor: 'divider',
          backgroundColor: isDarkMode ? '#2D3748' : '#ffffff',
          boxShadow: isDarkMode
            ? '0px 3px 3px -2px rgba(0,0,0,0.2),0px 3px 4px 0px rgba(0,0,0,0.14),0px 1px 8px 0px rgba(0,0,0,0.12)'
            : '0px 2px 1px -1px rgba(0,0,0,0.1),0px 1px 1px 0px rgba(0,0,0,0.07),0px 1px 3px 0px rgba(0,0,0,0.06)',
        }}
      >
        <CardContent sx={{ color: isDarkMode ? '#E2E8F0' : 'inherit' }}>
          <Box component="form" onSubmit={handleSubmit} sx={{ mt: 1 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  autoComplete="given-name"
                  name="name"
                  required
                  fullWidth
                  id="name"
                  label="Name"
                  placeholder="Enter client name"
                  autoFocus
                  size="small"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <PersonAddIcon
                          sx={{
                            color: isDarkMode ? '#90CAF9' : '#1976d2',
                          }}
                        />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      color: isDarkMode ? '#e2e8f0' : '#4a5568',
                      '& fieldset': {
                        borderColor: isDarkMode
                          ? 'rgba(255, 255, 255, 0.23)'
                          : 'rgba(0, 0, 0, 0.23)',
                      },
                      '&:hover fieldset': {
                        borderColor: isDarkMode
                          ? 'rgba(255, 255, 255, 0.4)'
                          : 'rgba(0, 0, 0, 0.4)',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                    '& .MuiInputLabel-root': {
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.7)'
                        : 'rgba(0, 0, 0, 0.6)',
                    },
                    '& .MuiInputAdornment-root': {
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.7)'
                        : 'rgba(0, 0, 0, 0.54)',
                    },
                    '& .MuiInputBase-input::placeholder': {
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.5)'
                        : 'rgba(0, 0, 0, 0.4)',
                      opacity: 1,
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  required
                  fullWidth
                  id="email"
                  label="Email Address"
                  name="email"
                  placeholder="Enter client email"
                  autoComplete="email"
                  size="small"
                  aria-required="true"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      color: isDarkMode ? '#e2e8f0' : '#4a5568',
                      '& fieldset': {
                        borderColor: isDarkMode
                          ? 'rgba(255, 255, 255, 0.23)'
                          : 'rgba(0, 0, 0, 0.23)',
                      },
                      '&:hover fieldset': {
                        borderColor: isDarkMode
                          ? 'rgba(255, 255, 255, 0.4)'
                          : 'rgba(0, 0, 0, 0.4)',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                    '& .MuiInputLabel-root': {
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.7)'
                        : 'rgba(0, 0, 0, 0.6)',
                    },
                    '& .MuiInputBase-input::placeholder': {
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.5)'
                        : 'rgba(0, 0, 0, 0.4)',
                      opacity: 1,
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  required
                  fullWidth
                  name="password"
                  label="Password"
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  placeholder="Enter client password"
                  autoComplete="new-password"
                  size="small"
                  aria-required="true"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle password visibility"
                          onClick={handleTogglePasswordVisibility}
                          edge="end"
                          sx={{
                            color: isDarkMode
                              ? 'rgba(255, 255, 255, 0.7)'
                              : 'rgba(0, 0, 0, 0.54)',
                            '&:hover': {
                              color: isDarkMode ? '#90CAF9' : '#1976d2',
                            },
                          }}
                        >
                          {showPassword ? (
                            <VisibilityOffIcon />
                          ) : (
                            <VisibilityIcon />
                          )}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      color: isDarkMode ? '#e2e8f0' : '#4a5568',
                      '& fieldset': {
                        borderColor: isDarkMode
                          ? 'rgba(255, 255, 255, 0.23)'
                          : 'rgba(0, 0, 0, 0.23)',
                      },
                      '&:hover fieldset': {
                        borderColor: isDarkMode
                          ? 'rgba(255, 255, 255, 0.4)'
                          : 'rgba(0, 0, 0, 0.4)',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                    '& .MuiInputLabel-root': {
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.7)'
                        : 'rgba(0, 0, 0, 0.6)',
                    },
                    '& .MuiInputAdornment-root': {
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.7)'
                        : 'rgba(0, 0, 0, 0.54)',
                    },
                    '& .MuiInputBase-input::placeholder': {
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.5)'
                        : 'rgba(0, 0, 0, 0.4)',
                      opacity: 1,
                    },
                  }}
                />
              </Grid>
            </Grid>
            <Button
              type="submit"
              fullWidth
              variant="contained"
              disabled={isCreating}
              sx={{
                mt: 3,
                boxShadow: isDarkMode
                  ? '0px 2px 4px rgba(0,0,0,0.3)'
                  : '0px 1px 2px rgba(0,0,0,0.1)',
                backgroundColor: isDarkMode ? '#3182CE' : '#2f6ad9',
                '&:hover': {
                  boxShadow: isDarkMode
                    ? '0px 3px 6px rgba(0,0,0,0.4)'
                    : '0px 2px 4px rgba(0,0,0,0.2)',
                  backgroundColor: isDarkMode ? '#4299E1' : '#3b7be8',
                },
                transition: 'all 0.2s ease-in-out',
                fontWeight: 500,
                textTransform: 'none',
                borderRadius: '4px',
              }}
            >
              {isCreating ? 'Creating...' : 'Create Client'}
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default ClientForm;
