'use client';
import React from 'react';
import { Box, Typography, Grid, useTheme, alpha, colors } from '@mui/material';
import ReactPlayer from 'react-player/youtube';

const AgencySection = ({
  blueText,
  blackText,
  stats,
  videoUrl,
  reverse = false,
  bgColor,
}) => {
  const theme = useTheme();

  return (
    <Box
      sx={{
        backgroundColor: bgColor,
        py: 8,
        width: '100%',
        boxShadow: theme.shadows[3],
        '&:not(:last-child)': {
          pb: 8,
        },
      }}
    >
      <Grid
        container
        sx={{
          flexDirection: { xs: 'column', md: reverse ? 'row-reverse' : 'row' },
          alignItems: 'center',
          justifyContent: 'center',
          maxWidth: 1280,
          mx: 'auto',
          px: { xs: 2, md: 4 },
          gap: { xs: 6, md: 0}
        }}
      >
        {/* Stats Column */}
        <Grid item xs={12} md={6}>
          <Typography
            sx={{
              fontSize: '2.3rem',
              fontWeight: 800,
              mb: 6,
              textAlign: blackText ? { xs: 'center', md: 'left' } : 'center',
              color: theme.palette.text.primary,
              ...(reverse ? { paddingLeft: 4 } : {}),
            }}
          >
            <Typography
              color={'primary'}
              component={'span'}
              variant={'inherit'}
              sx={{
                fontWeight: 700,
                display: 'inline',
                background: `linear-gradient(180deg, transparent 82%, ${alpha(
                  colors.purple[200],
                  0.3,
                )} 0%)`,
              }}
            >
              {blueText}
            </Typography>
            {blackText}
          </Typography>

          <Grid container spacing={3}>
            {stats.map((stat, index) => (
              <Grid item xs={6} key={index}>
                <Box
                  sx={{
                    textAlign: 'center',
                    p: 2,
                    borderRadius: 2,
                  }}
                >
                  <Typography
                    variant="h2"
                    sx={{
                      fontSize: { xs: '2.75rem', md: '3.5rem' },
                      fontWeight: 700,
                      lineHeight: 1,
                      mb: 1,
                      color: theme.palette.primary.main,
                    }}
                  >
                    {stat.value}
                  </Typography>
                  <Typography
                    variant="body1"
                    sx={{
                      fontSize: { xs: '0.9rem', md: '1rem' },
                      fontWeight: 500,
                      color: theme.palette.text.secondary,
                      lineHeight: 1.3,
                    }}
                  >
                    {stat.label}
                  </Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
        </Grid>

        {/* Video Column */}
        <Grid item xs={12} md={6} sx={{ p: { xs: 0, md: 2 } }}>
          <Box
            sx={{
              borderRadius: 4,
              overflow: 'hidden',
              boxShadow: theme.shadows[3],
              border: '1px solid',
              borderColor: theme.palette.divider,
              mx: 'auto',
              maxWidth: 640,
              width: '100%',
              aspectRatio: '16/9',
            }}
          >
            <ReactPlayer
              url={videoUrl}
              width="100%"
              height="100%"
              controls
              light={true}
              style={{
                display: 'flex',
                overflow: 'hidden',
                borderRadius: 4,
                minWidth: '340px',
                aspectRatio: 1
              }}
            />
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

// AgencySections component remains the same

const AgencySections = () => {
  return (
    <Box
      sx={{
        py: 4,
        bgcolor: 'background.default',
        display: 'flex',
        alignItems: 'center',
        flexDirection: 'column',
      }}
    >
      <Box
        sx={{
          maxWidth: 800,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          py: 6,
        }}
      >
        <Typography
          variant="h3"
          color="text.primary"
          align="center"
          sx={{
            fontWeight: 700,
          }}
        >
          {'Real Results: '}
          <Typography
            color={'primary'}
            component={'span'}
            variant={'inherit'}
            sx={{
              fontWeight: 700,
              display: 'inline',
              background: `linear-gradient(180deg, transparent 82%, ${alpha(
                colors.purple[200],
                0.3,
              )} 0%)`,
            }}
          >
            {' $1M+ LTV Added'}
          </Typography>
        </Typography>
      </Box>

      {/* Brand Buddy */}
      <AgencySection
        blueText="Brand Buddy"
        // blackText=" Amazon Agency"
        videoUrl="https://youtu.be/vhAdh4_tDhA"
        bgColor={`${alpha(colors.blue[200], 0.2)}`}
        stats={[
          { value: '11+', label: 'Client Closed & Counting' },
          { value: '100+', label: 'High quality meetings booked' },
          { value: '6', label: 'Months working together & counting' },
          { value: '$100k+', label: 'LTV Generated' },
        ]}
      />

      {/* Mr. Prime */}
      <AgencySection
        blueText="Mr. Prime:"
        blackText=" Amazon Agency"
        videoUrl="https://youtu.be/yURwINMR7J4"
        bgColor="#FFFFFF"
        reverse={true}
        stats={[
          { value: '6', label: 'Client Closed & Counting' },
          { value: '60+', label: 'High quality meetings booked' },
          { value: '5', label: 'Months working together & counting' },
          { value: '$100k+', label: 'LTV Generated' },
        ]}
      />

      {/* AMZ */}
      <AgencySection
        blueText="AMZ:"
        blackText=" Amazon Agency"
        videoUrl="https://youtu.be/LPOPlf7FEw4"
        bgColor={`${alpha(colors.blue[200], 0.2)}`}
        stats={[
          { value: '3+', label: 'Client Closed & Counting' },
          { value: '17+', label: 'High quality meetings booked' },
          { value: '$35K+', label: 'LTV Generated' },
          { value: '10.6X', label: 'Return on Investment' },
        ]}
      />
    </Box>
  );
};

export default AgencySections;
