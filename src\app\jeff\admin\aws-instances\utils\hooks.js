import { useState, useEffect, useCallback } from 'react';
import { fetchAwsInstances, performInstanceAction } from './api';
import { filterInstances, calculateInstanceStats } from './helpers';

export const useAwsInstances = (user) => {
  const [instances, setInstances] = useState([]);
  const [filteredInstances, setFilteredInstances] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState({
    total: 0,
    running: 0,
    stopped: 0,
    other: 0
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [actionLoading, setActionLoading] = useState(null);
  const [notification, setNotification] = useState({
    open: false,
    message: '',
    severity: 'info'
  });

  const loadInstances = useCallback(async () => {
    if (!user) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const data = await fetchAwsInstances();
      setInstances(data);
      setFilteredInstances(data);
      setStats(calculateInstanceStats(data));
    } catch (err) {
      console.error('Error fetching AWS instances:', err);
      setError(err.message || 'Failed to fetch AWS instances');
      showNotification('Failed to load AWS instances', 'error');
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  const handleInstanceAction = useCallback(async (instanceId, action) => {
    if (!user) return;
    
    setActionLoading(instanceId);
    
    try {
      const result = await performInstanceAction(instanceId, action);
      
      // Show success notification
      const actionText = action === 'start' ? 'started' : 
        action === 'stop' ? 'stopped' : 'rebooted';
      
      // Check if the action was successful
      if (result && result.success) {
        showNotification(`Instance ${instanceId} ${actionText} successfully`, 'success');
      } else {
        throw new Error(result?.error || `Failed to ${action} instance`);
      }
      
      // Reload instances after a short delay
      setTimeout(() => {
        loadInstances();
      }, 1000);
    } catch (err) {
      console.error(`Error ${action}ing instance:`, err);
      showNotification(`Failed to ${action} instance ${instanceId}`, 'error');
    } finally {
      setActionLoading(null);
    }
  }, [user, loadInstances]);

  const showNotification = (message, severity = 'info') => {
    setNotification({
      open: true,
      message,
      severity
    });
  };

  const closeNotification = () => {
    setNotification(prev => ({
      ...prev,
      open: false
    }));
  };

  // Filter instances when searchTerm changes
  useEffect(() => {
    if (instances.length > 0) {
      setFilteredInstances(filterInstances(instances, searchTerm));
    }
  }, [instances, searchTerm]);

  // Load instances when user is available
  useEffect(() => {
    if (user) {
      loadInstances();
    }
  }, [user, loadInstances]);

  return {
    instances: filteredInstances,
    isLoading,
    error,
    stats,
    searchTerm,
    setSearchTerm,
    actionLoading,
    handleInstanceAction,
    loadInstances,
    notification,
    closeNotification,
    showNotification
  };
}; 