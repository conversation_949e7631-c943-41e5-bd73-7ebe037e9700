'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import {
  Box,
  TextField,
  Button,
  Typography,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Collapse,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import VisibilityIcon from '@mui/icons-material/Visibility';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import { getAxiosInstance } from 'config/axios';
import { API_ENDPOINTS } from 'config/api';
//import step1 from ""

const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return dateString;
  return date.toLocaleString();
};

const CookieManagementTab = ({ isDarkMode, setNotification }) => {
  const [cookies, setCookies] = useState([]);
  const [loading, setLoading] = useState(false);
  const [instructionsOpen, setInstructionsOpen] = useState(false);
  const [formData, setFormData] = useState({
    emailId: '',
    cookiesData: '',
    countryCode: '',
  });
  const [editDialog, setEditDialog] = useState({
    open: false,
    cookie: null,
  });
  const [viewCookieDialog, setViewCookieDialog] = useState({
    open: false,
    data: '',
  });

  useEffect(() => {
    fetchCookies();
  }, []);

  const fetchCookies = async () => {
    try {
      setLoading(true);
      const response = await getAxiosInstance({
        cookiesKey: 'jeff-authorization',
      }).get(API_ENDPOINTS.LEX_COOKIES);
      setCookies(response.data.cookies);
    } catch (error) {
      console.error('Error fetching cookies:', error);
      setNotification({
        open: true,
        message: 'Error fetching cookies. Please try again.',
        severity: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const parseCookiesData = (cookiesDataStr) => {
    try {
      // Try to parse as JSON if it's a string
      if (typeof cookiesDataStr === 'string') {
        return JSON.parse(cookiesDataStr);
      }
      return cookiesDataStr;
    } catch (error) {
      console.error('Error parsing cookies data:', error);
      return [];
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!formData.emailId || !formData.cookiesData || !formData.countryCode) {
      setNotification({
        open: true,
        message: 'Please fill in all fields',
        severity: 'warning',
      });
      return;
    }

    try {
      setLoading(true);
      const cookiesData = parseCookiesData(formData.cookiesData);

      await getAxiosInstance({ cookiesKey: 'jeff-authorization' }).post(
        API_ENDPOINTS.LEX_REVIEW_SCRAPE_ADD_COOKIES,
        {
          emailId: formData.emailId,
          cookiesData,
          countryCode: formData.countryCode,
        },
      );

      setNotification({
        open: true,
        message: 'Cookies added successfully',
        severity: 'success',
      });
      setFormData({ emailId: '', cookiesData: '', countryCode: '' });
      fetchCookies();
    } catch (error) {
      console.error('Error adding cookies:', error);
      setNotification({
        open: true,
        message: 'Error adding cookies. Please try again.',
        severity: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (cookie) => {
    setEditDialog({
      open: true,
      cookie: {
        ...cookie,
        cookiesJson: JSON.stringify(cookie.cookies || [], null, 2),
        countryCode: cookie.countryCode || '',
      },
    });
  };

  const handleEditSubmit = async () => {
    try {
      setLoading(true);
      const cookiesData = JSON.parse(editDialog.cookie.cookiesJson);

      await getAxiosInstance({ cookiesKey: 'jeff-authorization' }).put(
        API_ENDPOINTS.LEX_REVIEW_SCRAPE_UPDATE_COOKIES,
        {
          emailId: editDialog.cookie.emailId,
          cookiesData,
          countryCode: editDialog.cookie.countryCode,
        },
      );

      setNotification({
        open: true,
        message: 'Cookies updated successfully',
        severity: 'success',
      });
      setEditDialog({ open: false, cookie: null });
      fetchCookies();
    } catch (error) {
      console.error('Error updating cookies:', error);
      setNotification({
        open: true,
        message: 'Error updating cookies. Please try again.',
        severity: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCopyCookies = async (textToCopy) => {
    try {
      await navigator.clipboard.writeText(textToCopy);
      setNotification({
        open: true,
        message: 'Cookies data copied to clipboard!',
        severity: 'success',
      });
    } catch (err) {
      console.error('Failed to copy text: ', err);
      setNotification({
        open: true,
        message: 'Failed to copy cookies data.',
        severity: 'error',
      });
    }
  };

  const handleViewCookies = (cookiesArray) => {
    setViewCookieDialog({
      open: true,
      data: JSON.stringify(cookiesArray, null, 2),
    });
  };

  return (
    <Box sx={{ mb: 3 }}>
      {/* Add Cookie Form */}
      <Card
        sx={{
          mb: 3,
          backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        }}
      >
        <CardContent>
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              color: isDarkMode ? '#fff' : 'inherit',
              mb: 3,
            }}
          >
            Add New Cookies
          </Typography>
          <Box component="form" onSubmit={handleSubmit}>
            <Box
              sx={{ display: 'flex', flexDirection: 'column', gap: 2, mb: 3 }}
            >
              <TextField
                label="Email ID"
                name="emailId"
                value={formData.emailId}
                onChange={handleInputChange}
                fullWidth
                size="small"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    '& fieldset': {
                      borderColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.23)'
                        : 'rgba(0, 0, 0, 0.23)',
                    },
                    '&:hover fieldset': {
                      borderColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.4)'
                        : 'rgba(0, 0, 0, 0.4)',
                    },
                  },
                  '& .MuiInputLabel-root': {
                    color: isDarkMode
                      ? 'rgba(255, 255, 255, 0.7)'
                      : 'rgba(0, 0, 0, 0.6)',
                  },
                }}
              />
              <FormControl fullWidth size="small">
                <InputLabel
                  id="country-code-label"
                  sx={{
                    color: isDarkMode
                      ? 'rgba(255, 255, 255, 0.7)'
                      : 'rgba(0, 0, 0, 0.6)',
                  }}
                >
                  Country Code
                </InputLabel>
                <Select
                  labelId="country-code-label"
                  name="countryCode"
                  value={formData.countryCode}
                  onChange={handleInputChange}
                  label="Country Code"
                  sx={{
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.23)'
                        : 'rgba(0, 0, 0, 0.23)',
                    },
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.4)'
                        : 'rgba(0, 0, 0, 0.4)',
                    },
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  }}
                >
                  <MenuItem value="US">US</MenuItem>
                  <MenuItem value="UK">UK</MenuItem>
                  <MenuItem value="CA">CA</MenuItem>
                </Select>
              </FormControl>
              <TextField
                label="Cookies Data"
                name="cookiesData"
                value={formData.cookiesData}
                onChange={handleInputChange}
                fullWidth
                multiline
                rows={4}
                placeholder='[{"name": "cookie1", "value": "value1"}, {"name": "cookie2", "value": "value2"}]'
                sx={{
                  '& .MuiOutlinedInput-root': {
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    '& fieldset': {
                      borderColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.23)'
                        : 'rgba(0, 0, 0, 0.23)',
                    },
                    '&:hover fieldset': {
                      borderColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.4)'
                        : 'rgba(0, 0, 0, 0.4)',
                    },
                  },
                  '& .MuiInputLabel-root': {
                    color: isDarkMode
                      ? 'rgba(255, 255, 255, 0.7)'
                      : 'rgba(0, 0, 0, 0.6)',
                  },
                }}
              />
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                type="submit"
                variant="contained"
                disabled={loading}
                sx={{
                  backgroundColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  '&:hover': {
                    backgroundColor: isDarkMode ? '#64B5F6' : '#1565C0',
                  },
                  minWidth: '120px',
                }}
              >
                {loading ? <CircularProgress size={24} /> : 'Add Cookies'}
              </Button>
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* Instructions Card */}
      <Card
        sx={{
          mb: 3,
          backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        }}
      >
        <CardContent>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              cursor: 'pointer',
              mb: instructionsOpen ? 3 : 0,
            }}
            onClick={() => setInstructionsOpen(!instructionsOpen)}
          >
            <Typography
              variant="h6"
              sx={{
                fontWeight: 600,
                color: isDarkMode ? '#fff' : 'inherit',
                fontSize: '1.5rem',
              }}
            >
              How to Get Your Cookies
            </Typography>
            <IconButton>
              {instructionsOpen ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </Box>
          <Collapse in={instructionsOpen}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
              <Box
                sx={{
                  p: 3,
                  borderRadius: 2,
                  backgroundColor: isDarkMode
                    ? 'rgba(255, 255, 255, 0.05)'
                    : 'rgba(0, 0, 0, 0.02)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    backgroundColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.08)'
                      : 'rgba(0, 0, 0, 0.04)',
                  },
                }}
              >
                <Typography
                  variant="subtitle1"
                  sx={{
                    fontWeight: 600,
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    mb: 2,
                    fontSize: '1.1rem',
                  }}
                >
                  1. Download and Install the &quot;EditThisCookie&quot;
                  Extension
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    color: isDarkMode
                      ? 'rgba(255, 255, 255, 0.7)'
                      : 'text.secondary',
                    mb: 2,
                    lineHeight: 1.6,
                  }}
                >
                  First, you add a special tool to your Chrome browser called
                  &quot;EditThisCookie.&quot; This tool lets you look at and
                  manage the small data files (cookies) that websites save on
                  your computer.
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 3,
                    alignItems: 'center',
                    width: '80%',
                    margin: '0 auto',
                  }}
                >
                  <div
                    style={{
                      position: 'relative',
                      width: '100%',
                      height: 'auto',
                    }}
                  >
                    <Image
                      src="/images/cookie-copy-steps/1.png"
                      alt="Step 1 - Install Extension"
                      width={0}
                      height={0}
                      sizes="100vw"
                      style={{ width: '100%', height: 'auto' }}
                      quality={100}
                    />
                  </div>
                  <div
                    style={{
                      position: 'relative',
                      width: '100%',
                      height: 'auto',
                    }}
                  >
                    <Image
                      src="/images/cookie-copy-steps/2.png"
                      alt="Step 1 - Extension Icon"
                      width={0}
                      height={0}
                      sizes="100vw"
                      style={{ width: '100%', height: 'auto' }}
                      quality={100}
                    />
                  </div>
                </Box>
              </Box>

              <Box
                sx={{
                  p: 3,
                  borderRadius: 2,
                  backgroundColor: isDarkMode
                    ? 'rgba(255, 255, 255, 0.05)'
                    : 'rgba(0, 0, 0, 0.02)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    backgroundColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.08)'
                      : 'rgba(0, 0, 0, 0.04)',
                  },
                }}
              >
                <Typography
                  variant="subtitle1"
                  sx={{
                    fontWeight: 600,
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    mb: 2,
                    fontSize: '1.1rem',
                  }}
                >
                  2. Go to Amazon.com and Sign In
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    color: isDarkMode
                      ? 'rgba(255, 255, 255, 0.7)'
                      : 'text.secondary',
                    lineHeight: 1.6,
                  }}
                >
                  Next, you visit the Amazon website and log into your account.
                </Typography>
              </Box>

              <Box
                sx={{
                  p: 3,
                  borderRadius: 2,
                  backgroundColor: isDarkMode
                    ? 'rgba(255, 255, 255, 0.05)'
                    : 'rgba(0, 0, 0, 0.02)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    backgroundColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.08)'
                      : 'rgba(0, 0, 0, 0.04)',
                  },
                }}
              >
                <Typography
                  variant="subtitle1"
                  sx={{
                    fontWeight: 600,
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    mb: 2,
                    fontSize: '1.1rem',
                  }}
                >
                  3. Change Your Location
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    color: isDarkMode
                      ? 'rgba(255, 255, 255, 0.7)'
                      : 'text.secondary',
                    mb: 2,
                    lineHeight: 1.6,
                  }}
                >
                  You then update your delivery location on Amazon to a specific
                  address (like New York, 10001).
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 3,
                    alignItems: 'center',
                    width: '80%',
                    margin: '0 auto',
                  }}
                >
                  <div
                    style={{
                      position: 'relative',
                      width: '100%',
                      height: 'auto',
                    }}
                  >
                    <Image
                      src="/images/cookie-copy-steps/3.png"
                      alt="Step 3 - Location Change 1"
                      width={0}
                      height={0}
                      sizes="100vw"
                      style={{ width: '100%', height: 'auto' }}
                      quality={100}
                    />
                  </div>
                  <div
                    style={{
                      position: 'relative',
                      width: '100%',
                      height: 'auto',
                    }}
                  >
                    <Image
                      src="/images/cookie-copy-steps/4.png"
                      alt="Step 3 - Location Change 2"
                      width={0}
                      height={0}
                      sizes="100vw"
                      style={{ width: '100%', height: 'auto' }}
                      quality={100}
                    />
                  </div>
                </Box>
              </Box>

              <Box
                sx={{
                  p: 3,
                  borderRadius: 2,
                  backgroundColor: isDarkMode
                    ? 'rgba(255, 255, 255, 0.05)'
                    : 'rgba(0, 0, 0, 0.02)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    backgroundColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.08)'
                      : 'rgba(0, 0, 0, 0.04)',
                  },
                }}
              >
                <Typography
                  variant="subtitle1"
                  sx={{
                    fontWeight: 600,
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    mb: 2,
                    fontSize: '1.1rem',
                  }}
                >
                  4. Open the Extension and Copy the Cookies
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    color: isDarkMode
                      ? 'rgba(255, 255, 255, 0.7)'
                      : 'text.secondary',
                    mb: 2,
                    lineHeight: 1.6,
                  }}
                >
                  Finally, you click on the &quot;EditThisCookie&quot; extension
                  icon in your browser. It will show you all the cookies Amazon
                  has stored. You then use the export or copy button to copy
                  this entire block of text. This text contains all the session,
                  location, and user data that Amazon is using to customize your
                  experience.
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 3,
                    alignItems: 'center',
                    width: '80%',
                    margin: '0 auto',
                  }}
                >
                  <div
                    style={{
                      position: 'relative',
                      width: '100%',
                      height: 'auto',
                    }}
                  >
                    <Image
                      src="/images/cookie-copy-steps/5.png"
                      alt="Step 4 - Cookie View 1"
                      width={0}
                      height={0}
                      sizes="100vw"
                      style={{ width: '100%', height: 'auto' }}
                      quality={100}
                    />
                  </div>
                  <div
                    style={{
                      position: 'relative',
                      width: '100%',
                      height: 'auto',
                    }}
                  >
                    <Image
                      src="/images/cookie-copy-steps/6.png"
                      alt="Step 4 - Cookie View 2"
                      width={0}
                      height={0}
                      sizes="100vw"
                      style={{ width: '100%', height: 'auto' }}
                      quality={100}
                    />
                  </div>
                </Box>
              </Box>

              <Box
                sx={{
                  p: 3,
                  borderRadius: 2,
                  backgroundColor: isDarkMode
                    ? 'rgba(255, 255, 255, 0.05)'
                    : 'rgba(0, 0, 0, 0.02)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    backgroundColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.08)'
                      : 'rgba(0, 0, 0, 0.04)',
                  },
                }}
              >
                <Typography
                  variant="subtitle1"
                  sx={{
                    fontWeight: 600,
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    mb: 2,
                    fontSize: '1.1rem',
                  }}
                >
                  5. Add Your Credentials to the Frontend
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    color: isDarkMode
                      ? 'rgba(255, 255, 255, 0.7)'
                      : 'text.secondary',
                    mb: 2,
                    lineHeight: 1.6,
                  }}
                >
                  Go to our frontend dashboard at
                  https://www.equalcollective.com/jeff/dashboard. Then navigate
                  to the Lex Reviews Scraper section → Cookies Management. Paste
                  your Amazon Email ID and the copied cookies into the
                  respective fields, and click the &quot;Add&quot; button to
                  save them.
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    width: '80%',
                    margin: '0 auto',
                  }}
                >
                  <div
                    style={{
                      position: 'relative',
                      width: '100%',
                      height: 'auto',
                    }}
                  >
                    <Image
                      src="/images/cookie-copy-steps/7.png"
                      alt="Step 5 - Frontend Dashboard"
                      width={0}
                      height={0}
                      sizes="100vw"
                      style={{ width: '100%', height: 'auto' }}
                      quality={100}
                    />
                  </div>
                </Box>
              </Box>
            </Box>
          </Collapse>
        </CardContent>
      </Card>

      {/* Cookie List */}
      <Card
        sx={{
          backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        }}
      >
        <CardContent>
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              color: isDarkMode ? '#fff' : 'inherit',
              mb: 3,
            }}
          >
            Cookie List
          </Typography>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell
                    sx={{
                      backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                      color: isDarkMode ? '#e2e8f0' : '#4a5568',
                      fontWeight: 600,
                      borderBottom: `1px solid ${
                        isDarkMode
                          ? 'rgba(255, 255, 255, 0.12)'
                          : 'rgba(0, 0, 0, 0.12)'
                      }`,
                    }}
                  >
                    Email ID
                  </TableCell>
                  <TableCell
                    sx={{
                      backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                      color: isDarkMode ? '#e2e8f0' : '#4a5568',
                      fontWeight: 600,
                      borderBottom: `1px solid ${
                        isDarkMode
                          ? 'rgba(255, 255, 255, 0.12)'
                          : 'rgba(0, 0, 0, 0.12)'
                      }`,
                    }}
                  >
                    Country Code
                  </TableCell>
                  <TableCell
                    sx={{
                      backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                      color: isDarkMode ? '#e2e8f0' : '#4a5568',
                      fontWeight: 600,
                      borderBottom: `1px solid ${
                        isDarkMode
                          ? 'rgba(255, 255, 255, 0.12)'
                          : 'rgba(0, 0, 0, 0.12)'
                      }`,
                    }}
                  >
                    Cookie Count
                  </TableCell>
                  <TableCell
                    sx={{
                      backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                      color: isDarkMode ? '#e2e8f0' : '#4a5568',
                      fontWeight: 600,
                      borderBottom: `1px solid ${
                        isDarkMode
                          ? 'rgba(255, 255, 255, 0.12)'
                          : 'rgba(0, 0, 0, 0.12)'
                      }`,
                    }}
                  >
                    Status
                  </TableCell>
                  <TableCell
                    sx={{
                      backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                      color: isDarkMode ? '#e2e8f0' : '#4a5568',
                      fontWeight: 600,
                      borderBottom: `1px solid ${
                        isDarkMode
                          ? 'rgba(255, 255, 255, 0.12)'
                          : 'rgba(0, 0, 0, 0.12)'
                      }`,
                    }}
                  >
                    Created At
                  </TableCell>
                  <TableCell
                    sx={{
                      backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                      color: isDarkMode ? '#e2e8f0' : '#4a5568',
                      fontWeight: 600,
                      borderBottom: `1px solid ${
                        isDarkMode
                          ? 'rgba(255, 255, 255, 0.12)'
                          : 'rgba(0, 0, 0, 0.12)'
                      }`,
                    }}
                  >
                    Updated At
                  </TableCell>
                  <TableCell
                    sx={{
                      backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                      color: isDarkMode ? '#e2e8f0' : '#4a5568',
                      fontWeight: 600,
                      borderBottom: `1px solid ${
                        isDarkMode
                          ? 'rgba(255, 255, 255, 0.12)'
                          : 'rgba(0, 0, 0, 0.12)'
                      }`,
                    }}
                  >
                    Raw Cookies Data
                  </TableCell>
                  <TableCell
                    align="right"
                    sx={{
                      backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                      color: isDarkMode ? '#e2e8f0' : '#4a5568',
                      fontWeight: 600,
                      borderBottom: `1px solid ${
                        isDarkMode
                          ? 'rgba(255, 255, 255, 0.12)'
                          : 'rgba(0, 0, 0, 0.12)'
                      }`,
                    }}
                  >
                    Actions
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell
                      colSpan={8}
                      align="center"
                      sx={{
                        borderBottom: `1px solid ${
                          isDarkMode
                            ? 'rgba(255, 255, 255, 0.12)'
                            : 'rgba(0, 0, 0, 0.12)'
                        }`,
                      }}
                    >
                      <CircularProgress size={24} sx={{ my: 2 }} />
                    </TableCell>
                  </TableRow>
                ) : cookies.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={8}
                      align="center"
                      sx={{
                        borderBottom: `1px solid ${
                          isDarkMode
                            ? 'rgba(255, 255, 255, 0.12)'
                            : 'rgba(0, 0, 0, 0.12)'
                        }`,
                      }}
                    >
                      <Typography
                        variant="body2"
                        sx={{
                          py: 2,
                          color: isDarkMode
                            ? 'rgba(255, 255, 255, 0.5)'
                            : 'text.secondary',
                        }}
                      >
                        No cookies found
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  cookies.map((cookie) => (
                    <TableRow
                      key={cookie.id}
                      sx={{
                        '&:nth-of-type(odd)': {
                          backgroundColor: isDarkMode
                            ? 'rgba(255, 255, 255, 0.05)'
                            : '#f7fafc',
                        },
                        '&:hover': {
                          backgroundColor: isDarkMode
                            ? 'rgba(255, 255, 255, 0.08)'
                            : '#edf2f7',
                        },
                      }}
                    >
                      <TableCell
                        sx={{
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          borderBottom: `1px solid ${
                            isDarkMode
                              ? 'rgba(255, 255, 255, 0.12)'
                              : 'rgba(0, 0, 0, 0.12)'
                          }`,
                        }}
                      >
                        {cookie.emailId}
                      </TableCell>
                      <TableCell
                        sx={{
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          borderBottom: `1px solid ${
                            isDarkMode
                              ? 'rgba(255, 255, 255, 0.12)'
                              : 'rgba(0, 0, 0, 0.12)'
                          }`,
                        }}
                      >
                        {cookie.countryCode || '-'}
                      </TableCell>
                      <TableCell
                        sx={{
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          borderBottom: `1px solid ${
                            isDarkMode
                              ? 'rgba(255, 255, 255, 0.12)'
                              : 'rgba(0, 0, 0, 0.12)'
                          }`,
                        }}
                      >
                        {cookie.cookieCount}
                      </TableCell>
                      <TableCell
                        sx={{
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          borderBottom: `1px solid ${
                            isDarkMode
                              ? 'rgba(255, 255, 255, 0.12)'
                              : 'rgba(0, 0, 0, 0.12)'
                          }`,
                        }}
                      >
                        <Box
                          sx={{
                            display: 'inline-block',
                            px: 1,
                            py: 0.5,
                            borderRadius: 1,
                            backgroundColor:
                              cookie.status === 'ACTIVE'
                                ? isDarkMode
                                  ? 'rgba(72, 187, 120, 0.2)'
                                  : 'rgba(72, 187, 120, 0.1)'
                                : isDarkMode
                                  ? 'rgba(245, 101, 101, 0.2)'
                                  : 'rgba(245, 101, 101, 0.1)',
                            color:
                              cookie.status === 'ACTIVE'
                                ? isDarkMode
                                  ? '#9AE6B4'
                                  : '#2F855A'
                                : isDarkMode
                                  ? '#FEB2B2'
                                  : '#C53030',
                            fontWeight: 500,
                          }}
                        >
                          {cookie.status}
                        </Box>
                      </TableCell>
                      <TableCell
                        sx={{
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          borderBottom: `1px solid ${
                            isDarkMode
                              ? 'rgba(255, 255, 255, 0.12)'
                              : 'rgba(0, 0, 0, 0.12)'
                          }`,
                        }}
                      >
                        {formatDate(cookie.createdAt)}
                      </TableCell>
                      <TableCell
                        sx={{
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          borderBottom: `1px solid ${
                            isDarkMode
                              ? 'rgba(255, 255, 255, 0.12)'
                              : 'rgba(0, 0, 0, 0.12)'
                          }`,
                        }}
                      >
                        {formatDate(cookie.updatedAt)}
                      </TableCell>
                      <TableCell
                        sx={{
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          borderBottom: `1px solid ${
                            isDarkMode
                              ? 'rgba(255, 255, 255, 0.12)'
                              : 'rgba(0, 0, 0, 0.12)'
                          }`,
                        }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 0.5,
                          }}
                        >
                          <IconButton
                            size="small"
                            onClick={() => handleViewCookies(cookie.cookies)}
                            sx={{
                              color: isDarkMode ? '#90CAF9' : '#1976d2',
                            }}
                          >
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() =>
                              handleCopyCookies(
                                JSON.stringify(cookie.cookies, null, 2),
                              )
                            }
                            sx={{
                              color: isDarkMode ? '#9AE6B4' : '#2F855A',
                            }}
                          >
                            <ContentCopyIcon fontSize="small" />
                          </IconButton>
                        </Box>
                      </TableCell>
                      <TableCell
                        align="right"
                        sx={{
                          borderBottom: `1px solid ${
                            isDarkMode
                              ? 'rgba(255, 255, 255, 0.12)'
                              : 'rgba(0, 0, 0, 0.12)'
                          }`,
                        }}
                      >
                        <IconButton
                          onClick={() => handleEdit(cookie)}
                          sx={{
                            color: isDarkMode ? '#90CAF9' : '#1976d2',
                            '&:hover': {
                              backgroundColor: isDarkMode
                                ? 'rgba(144, 202, 249, 0.08)'
                                : 'rgba(25, 118, 210, 0.08)',
                            },
                          }}
                        >
                          <EditIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog
        open={editDialog.open}
        onClose={() => setEditDialog({ open: false, cookie: null })}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle
          sx={{
            color: isDarkMode ? '#fff' : 'inherit',
            backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          }}
        >
          Edit Cookies
        </DialogTitle>
        <DialogContent
          sx={{
            backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          }}
        >
          <Box sx={{ mt: 2 }}>
            <TextField
              label="Email ID"
              fullWidth
              value={editDialog.cookie?.emailId || ''}
              disabled
              sx={{
                mb: 2,
                '& .MuiOutlinedInput-root': {
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  '& fieldset': {
                    borderColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.23)'
                      : 'rgba(0, 0, 0, 0.23)',
                  },
                },
                '& .MuiInputLabel-root': {
                  color: isDarkMode
                    ? 'rgba(255, 255, 255, 0.7)'
                    : 'rgba(0, 0, 0, 0.6)',
                },
              }}
            />
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel
                id="edit-country-code-label"
                sx={{
                  color: isDarkMode
                    ? 'rgba(255, 255, 255, 0.7)'
                    : 'rgba(0, 0, 0, 0.6)',
                }}
              >
                Country Code
              </InputLabel>
              <Select
                labelId="edit-country-code-label"
                value={editDialog.cookie?.countryCode || ''}
                onChange={(e) =>
                  setEditDialog((edit) => ({
                    ...edit,
                    cookie: {
                      ...edit.cookie,
                      countryCode: e.target.value,
                    },
                  }))
                }
                label="Country Code"
                sx={{
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.23)'
                      : 'rgba(0, 0, 0, 0.23)',
                  },
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.4)'
                      : 'rgba(0, 0, 0, 0.4)',
                  },
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                }}
              >
                <MenuItem value="US">US</MenuItem>
                <MenuItem value="UK">UK</MenuItem>
                <MenuItem value="CA">CA</MenuItem>
              </Select>
            </FormControl>
            <TextField
              label="Cookies Data"
              fullWidth
              multiline
              rows={6}
              value={editDialog.cookie?.cookiesJson || ''}
              onChange={(e) =>
                setEditDialog({
                  ...editDialog,
                  cookie: {
                    ...editDialog.cookie,
                    cookiesJson: e.target.value,
                  },
                })
              }
              sx={{
                '& .MuiOutlinedInput-root': {
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  '& fieldset': {
                    borderColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.23)'
                      : 'rgba(0, 0, 0, 0.23)',
                  },
                },
                '& .MuiInputLabel-root': {
                  color: isDarkMode
                    ? 'rgba(255, 255, 255, 0.7)'
                    : 'rgba(0, 0, 0, 0.6)',
                },
              }}
            />
          </Box>
        </DialogContent>
        <DialogActions
          sx={{
            backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          }}
        >
          <Button
            onClick={() => setEditDialog({ open: false, cookie: null })}
            sx={{
              color: isDarkMode ? '#e2e8f0' : '#4a5568',
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleEditSubmit}
            variant="contained"
            disabled={loading}
            sx={{
              backgroundColor: isDarkMode ? '#90CAF9' : '#1976d2',
              '&:hover': {
                backgroundColor: isDarkMode ? '#64B5F6' : '#1565C0',
              },
            }}
          >
            {loading ? <CircularProgress size={24} /> : 'Save'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* View Cookies Dialog */}
      <Dialog
        open={viewCookieDialog.open}
        onClose={() => setViewCookieDialog({ open: false, data: '' })}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle
          sx={{
            color: isDarkMode ? '#fff' : 'inherit',
            backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          }}
        >
          Raw Cookies Data
        </DialogTitle>
        <DialogContent
          sx={{
            backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          }}
        >
          <TextField
            fullWidth
            multiline
            rows={15}
            value={viewCookieDialog.data}
            InputProps={{
              readOnly: true,
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                color: isDarkMode ? '#e2e8f0' : '#4a5568',
                '& fieldset': {
                  borderColor: isDarkMode
                    ? 'rgba(255, 255, 255, 0.23)'
                    : 'rgba(0, 0, 0, 0.23)',
                },
              },
              '& .MuiInputBase-input': {
                fontFamily: 'monospace',
              },
            }}
          />
        </DialogContent>
        <DialogActions
          sx={{
            backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          }}
        >
          <Button
            onClick={() => setViewCookieDialog({ open: false, data: '' })}
            sx={{
              color: isDarkMode ? '#e2e8f0' : '#4a5568',
            }}
          >
            Close
          </Button>
          <Button
            onClick={() => handleCopyCookies(viewCookieDialog.data)}
            variant="contained"
            sx={{
              backgroundColor: isDarkMode ? '#9AE6B4' : '#2F855A',
              '&:hover': {
                backgroundColor: isDarkMode ? '#82E2B4' : '#287453',
              },
            }}
          >
            Copy
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CookieManagementTab;
