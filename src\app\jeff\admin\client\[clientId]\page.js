'use client';

import React, { useState, useEffect } from 'react';
import JeffClientDetails from 'views/Jeff/JeffClientDetails/JeffClientDetails';
import { getClientById } from 'views/Jeff/Utils/getClients';
import { useRouter } from 'next/navigation';
import { useUser } from 'views/Jeff/Utils/getUser';
import { CircularProgress, Box } from '@mui/material';

const Page = ({ params }) => {
  const router = useRouter();
  const { data: user, isLoading: isUserLoading } = useUser();
  const clientId = params.clientId;
  const [clientData, setClientData] = useState({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!isUserLoading) {
      if (!user) {
        router.push('/jeff/login');
      } else if (user?.userType !== 'admin') {
        router.push('/jeff/dashboard');
      }
    }
  }, [isUserLoading, user, router]);

  const fetchData = async () => {
    setLoading(true);
    try {
      const data = await getClientById(clientId);
      setClientData(data);
    } catch (err) {
      console.error('Error fetching client data:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (clientId) {
      fetchData();
    }
  }, [clientId]);

  return (
    <Box sx={{ position: 'relative', minHeight: '100vh' }}>
      <JeffClientDetails
        fetchData={fetchData}
        clientData={clientData}
        params={params}
        setLoading={setLoading}
        loading={loading}
      />
      {(loading || isUserLoading) && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: 'rgba(255, 255, 255, 0.7)',
            zIndex: 9999,
            pointerEvents: 'none',
          }}
        >
          <CircularProgress color="primary" style={{ zIndex: '1000' }} />
        </Box>
      )}
    </Box>
  );
};

export default Page;
