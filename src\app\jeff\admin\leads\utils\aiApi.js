import { getSellerBotAxiosInstance } from 'config/axios';
import { API_ENDPOINTS } from 'config/api';

/**
 * Create a new AI lead generation job
 * @param {FormData} formData - Form data containing CSV file and job parameters
 * @param {string} model - AI model to use (open<PERSON>, claude, gemini)
 * @param {string} prompt - Custom prompt for AI processing
 * @returns {Promise<Object>} API response with job details
 */
export const createAILeadJob = async (formData, model, prompt) => {
  try {
    // Add AI-specific parameters to form data
    formData.append('model', model);
    formData.append('prompt', prompt);

    const response = await getSellerBotAxiosInstance().post(
      API_ENDPOINTS.SB_AI_LEAD_GENERATE,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    if (response.data && response.data.success) {
      return {
        success: true,
        jobId: response.data.jobId,
        message: response.data.message || 'AI job created successfully',
        data: response.data.data,
      };
    } else {
      return {
        success: false,
        message: response.data?.error || 'Failed to create AI job',
      };
    }
  } catch (error) {
    console.error('Error creating AI lead job:', error);
    return {
      success: false,
      message: error.response?.data?.error || 'Failed to create AI job',
    };
  }
};

/**
 * Get AI job progress
 * @param {string} jobId - Job ID to check progress for
 * @returns {Promise<Object>} Progress information
 */
export const getAIJobProgress = async (jobId) => {
  try {
    const endpoint = API_ENDPOINTS.SB_AI_JOB_PROGRESS.replace(':jobId', jobId);
    const response = await getSellerBotAxiosInstance().get(endpoint);

    if (response.data) {
      return {
        success: true,
        progress: response.data.progress || 0,
        status: response.data.status,
        processedCount: response.data.processedCount || 0,
        totalCount: response.data.totalCount || 0,
        tokensUsed: response.data.tokensUsed || 0,
        costAccumulated: response.data.costAccumulated || 0,
        estimatedTimeRemaining: response.data.estimatedTimeRemaining,
        message: response.data.message,
      };
    } else {
      return {
        success: false,
        message: 'Failed to get job progress',
      };
    }
  } catch (error) {
    console.error('Error getting AI job progress:', error);
    return {
      success: false,
      message: error.response?.data?.error || 'Failed to get job progress',
    };
  }
};

/**
 * Download AI job results
 * @param {string} jobId - Job ID to download results for
 * @returns {Promise<Object>} Download URL or file data
 */
export const downloadAIJobResults = async (jobId) => {
  try {
    const endpoint = API_ENDPOINTS.SB_AI_JOB_RESULTS.replace(':jobId', jobId);
    const response = await getSellerBotAxiosInstance().get(endpoint);

    if (response.data && response.data.success) {
      return {
        success: true,
        url: response.data.url,
        filename: response.data.filename,
        message: 'Results ready for download',
      };
    } else {
      return {
        success: false,
        message: response.data?.error || 'Failed to get download URL',
      };
    }
  } catch (error) {
    console.error('Error downloading AI job results:', error);
    return {
      success: false,
      message: error.response?.data?.error || 'Failed to download results',
    };
  }
};

/**
 * Get AI usage statistics
 * @param {string} model - Optional model filter (openai, claude, gemini)
 * @param {string} timeRange - Optional time range (day, week, month, all)
 * @returns {Promise<Object>} Usage statistics
 */
export const getAIStats = async (model = null, timeRange = 'month') => {
  try {
    const params = {};
    if (model) params.model = model;
    if (timeRange) params.timeRange = timeRange;

    const response = await getSellerBotAxiosInstance().get(
      API_ENDPOINTS.SB_AI_STATS,
      { params }
    );

    if (response.data && response.data.success) {
      return {
        success: true,
        stats: {
          totalJobs: response.data.totalJobs || 0,
          totalCost: response.data.totalCost || 0,
          totalTokens: response.data.totalTokens || 0,
          successRate: response.data.successRate || 0,
          averageCostPerLead: response.data.averageCostPerLead || 0,
          modelBreakdown: response.data.modelBreakdown || {},
          dailyUsage: response.data.dailyUsage || [],
          topPrompts: response.data.topPrompts || [],
        },
        message: 'Statistics retrieved successfully',
      };
    } else {
      return {
        success: false,
        message: response.data?.error || 'Failed to get statistics',
      };
    }
  } catch (error) {
    console.error('Error getting AI statistics:', error);
    return {
      success: false,
      message: error.response?.data?.error || 'Failed to get statistics',
    };
  }
};

/**
 * Get default prompt for a specific AI model
 * @param {string} model - AI model (openai, claude, gemini)
 * @returns {Promise<Object>} Default prompt text
 */
export const getDefaultPrompt = async (model) => {
  try {
    const response = await getSellerBotAxiosInstance().get(
      API_ENDPOINTS.SB_AI_DEFAULT_PROMPT,
      { params: { model } }
    );

    if (response.data && response.data.success) {
      return {
        success: true,
        prompt: response.data.prompt,
        message: 'Default prompt retrieved successfully',
      };
    } else {
      return {
        success: false,
        message: response.data?.error || 'Failed to get default prompt',
      };
    }
  } catch (error) {
    console.error('Error getting default prompt:', error);
    return {
      success: false,
      message: error.response?.data?.error || 'Failed to get default prompt',
    };
  }
};

/**
 * Manually trigger AI job processing
 * @param {string} jobId - Job ID to process
 * @returns {Promise<Object>} Processing status
 */
export const processAIJob = async (jobId) => {
  try {
    const endpoint = API_ENDPOINTS.SB_AI_JOB_PROCESS.replace(':jobId', jobId);
    const response = await getSellerBotAxiosInstance().post(endpoint);

    if (response.data && response.data.success) {
      return {
        success: true,
        message: response.data.message || 'Job processing started',
      };
    } else {
      return {
        success: false,
        message: response.data?.error || 'Failed to start job processing',
      };
    }
  } catch (error) {
    console.error('Error processing AI job:', error);
    return {
      success: false,
      message: error.response?.data?.error || 'Failed to start job processing',
    };
  }
};

/**
 * Helper function to format AI job data for display
 * @param {Object} job - Raw job data from API
 * @returns {Object} Formatted job data
 */
export const formatAIJobData = (job) => {
  return {
    ...job,
    mode: job.mode || 'ai',
    aiModel: job.aiModel || job.model,
    tokensUsed: job.tokensUsed || 0,
    costAccumulated: job.costAccumulated || 0,
    prompt: job.prompt || '',
    isAIJob: true,
  };
};

/**
 * Helper function to calculate token estimation
 * @param {string} text - Text to estimate tokens for
 * @returns {number} Estimated token count
 */
export const estimateTokens = (text) => {
  if (!text) return 0;
  // Rough estimation: 4 characters per token
  return Math.ceil(text.length / 4);
};

/**
 * Helper function to calculate cost estimation
 * @param {number} tokens - Number of tokens
 * @param {string} model - AI model
 * @returns {number} Estimated cost in USD
 */
export const estimateCost = (tokens, model) => {
  const modelCosts = {
    openai: 0.03,
    claude: 0.025,
    gemini: 0.02,
  };
  
  const costPer1k = modelCosts[model] || 0.03;
  return (tokens / 1000) * costPer1k;
};
