/**
 * Filter AWS instances based on search term
 * @param {Array} instances - Array of AWS instance objects
 * @param {string} searchTerm - Search term to filter by
 * @returns {Array} Filtered array of instances
 */
export const filterInstances = (instances, searchTerm) => {
  if (!instances || !Array.isArray(instances)) return [];
  
  return instances.filter(instance => {
    const instanceId = instance.InstanceId?.toLowerCase() || '';
    const instanceType = instance.InstanceType?.toLowerCase() || '';
    const instanceName = instance.Name?.toLowerCase() || '';
    const searchLower = searchTerm.toLowerCase();
    
    return instanceId.includes(searchLower) || 
           instanceType.includes(searchLower) || 
           instanceName.includes(searchLower);
  });
};

/**
 * Calculate instance statistics
 * @param {Array} instances - Array of AWS instance objects
 * @returns {Object} Object containing instance stats
 */
export const calculateInstanceStats = (instances) => {
  if (!instances || !Array.isArray(instances)) {
    return {
      total: 0,
      running: 0,
      stopped: 0,
      other: 0,
    };
  }
  
  return {
    total: instances.length || 0,
    running: instances.filter(i => i.State === 'running').length || 0,
    stopped: instances.filter(i => i.State === 'stopped').length || 0,
    other: instances.filter(i => !['running', 'stopped'].includes(i.State)).length || 0,
  };
}; 