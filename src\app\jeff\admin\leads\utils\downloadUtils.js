/**
 * Utility function to download a file from a URL
 * @param {string} fileName - The name to save the file as
 * @param {string} url - The URL to download from
 */
export const downloadByUrl = (fileName, url) => {
  const aTag = document.createElement('a');
  aTag.href = url;
  aTag.download = fileName || 'download';
  document.body.appendChild(aTag);
  aTag.click();
  document.body.removeChild(aTag);
};

/**
 * Utility function to download a file from a blob response
 * @param {Blob} data - The blob data to download
 * @param {string} fileName - The name to save the file as
 */
export const downloadBlob = (data, fileName) => {
  const blob = new Blob([data]);
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = fileName;
  document.body.appendChild(a);
  a.click();
  window.URL.revokeObjectURL(url);
  document.body.removeChild(a);
};
