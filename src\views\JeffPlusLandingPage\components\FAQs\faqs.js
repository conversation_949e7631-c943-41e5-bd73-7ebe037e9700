import React from 'react';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import { alpha } from '@mui/material/styles';
import { colors } from '@mui/material';
import Spacer from '../../../../components/Equal/Spacer';

import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';


const FAQS = [
  {
    question: 'What’s the price?',
    answer: '<PERSON> offers a custom solution that handles everything, including replies. It’s more affordable than your sales rep, even if they’re in a low-cost country. Book a demo, and we can walk you through pricing based on your use case.'
  },
  {
    question: 'What size of companies do you work with?',
    answer: 'We work with companies of all sizes, tailoring our approach to fit your needs.'
  },
  {
    question: 'Do you work with freelancers & amazon coaches?',
    answer: 'Yes, <PERSON> supports freelancers looking to enhance their sales outreach. We understand your bandwidth is extremely limited, so <PERSON> can manage auto-responders and more, allowing you to focus on what you do best—building growth and delivering value to your clients.',
  },
  {
    question: 'Do you work with freelancers & amazon coaches?',
    answer: 'Yes, <PERSON> supports freelancers looking to enhance their sales outreach. We understand your bandwidth is extremely limited, so <PERSON> can manage auto-responders and more, allowing you to focus on what you do best—building growth and delivering value to your clients.'
  },
  {
    question: 'Where are you getting this data from?',
    answer: 'Our data is powered by a real-time database of over 200k Amazon sellers, sourced from diverse and verified sources. This ensures that our information is always up-to-date and reliable.'
  },
  {
    question: 'Will the emails sound “AI written”?',
    answer: 'No, Jeff crafts personalized emails in your brand voice, making them sound natural.'
  },
  {
    question: 'Do you do outreach on other channels like LinkedIn?',
    answer: 'Yes, Jeff also supports outreach on LinkedIn with personalized messages.'
  },
  {
    question: 'Are there any additional fees?',
    answer: 'If you don’t have an outbound infrastructure set up, such as multiple domains or services like Instantly or Smartlead, there may be additional costs. We can help you set up this infrastructure, ensuring best practices to handle deliverability issues effectively.'
  }
];

const Team = () => {

  const [openFAQIndex, setOpenFAQIndex] = React.useState(0);

  const handleFAQClick = (index) => {
    setOpenFAQIndex(openFAQIndex === index ? null : index);
  };

  return (
    <Box>
      <Typography variant={'h4'} sx={{ fontWeight: 700, textAlign: 'center' }} gutterBottom>
        {'Your '}
        <Typography
          color={'primary'}
          component={'span'}
          variant={'inherit'}
          sx={{
            fontWeight: 700,
            display: 'inline',
            background: `linear-gradient(180deg, transparent 82%, ${alpha(
              colors.purple[200],
              0.3,
            )} 0%)`,
          }}
        >
          {'questions answered'}
        </Typography>
        {' about Jeff'}
      </Typography>
      <Spacer y={2} />
      {FAQS.map((faq, key) => {
        return (
          <Box key={key} sx={{ backgroundColor: colors.blue[200], padding: 2, marginBottom: 1, borderRadius: 2 }}>
            <Box
              key={key}
              sx={{ display: 'flex', justifyContent: 'space-between', cursor: 'pointer' }}
              onClick={() => handleFAQClick(key)}
            >
              <Typography sx={{ fontWeight: 700 }}>{faq.question}</Typography>
              {openFAQIndex === key ? <KeyboardArrowUpIcon /> :  <KeyboardArrowDownIcon />}
              
            </Box>
            {openFAQIndex === key && (
              <>
                <Spacer y={2} />
                <Box>
                  <Typography>{faq.answer}</Typography>
                </Box>
              </>
            )}
          </Box>
        );
      })}
    </Box>
  );
};

export default Team;