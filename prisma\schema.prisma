generator client {
  provider = "prisma-client-js"
}
 
datasource db {
  provider = "postgresql"
  // Uses connection pooling
  url = env("POSTGRES_PRISMA_URL")
  // Uses direct connection, ⚠️ make sure to keep this to `POSTGRES_URL_NON_POOLING`
  // or you'll have dangling databases from migrations
  directUrl = env("POSTGRES_URL_NON_POOLING")
}
 
model User {
  id        Int      @id @default(autoincrement())
  email     String
  createdAt DateTime @default(now())
}