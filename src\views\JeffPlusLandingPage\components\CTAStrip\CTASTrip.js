/* eslint-disable react/no-unescaped-entities */
import React from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Spacer from '../../../../components/Equal/Spacer';

const CTAStrip = ({ stripText, ctaLink, ctaOnClick, ctaText }) => {
  return (
    <Box padding={2}>
      <Box>
        <Typography
          variant="h5"
          color="text.primary"
          align={'center'}
          sx={{ fontWeight: 700 }}
        >
          {stripText}
        </Typography>
        <Spacer y={1} />
        <Box style={{ display: 'flex', justifyContent: 'center' }}>
          <Button
            component={'a'}
            variant="contained"
            color="primary"
            size="large"
            fullWidth={false}
            href={ctaLink}
            onClick={ctaOnClick}
            target={'_blank'}
          >
            {ctaText}
          </Button>
        </Box>
      </Box>
    </Box>
  );
};

export default CTAStrip;
