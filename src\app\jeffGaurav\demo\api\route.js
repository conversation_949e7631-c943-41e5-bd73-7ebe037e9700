import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET() {
  return NextResponse.json({ testing: 'ok' });
}

export async function POST(request) {
  const res = await request.json();
  const data = res.data;
  const newUser = await prisma.user.create({
    data: {
      email: data.email,
    },
  });
  return NextResponse.json({ message: 'done', newUser });
}
