import React from 'react';
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  TextField,
  Typography,
  InputAdornment,
  useTheme
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';

const ClientSearch = ({ 
  searchTerm, 
  onSearch, 
  isDarkMode,
  clientCount
}) => {
  const theme = useTheme();

  return (
    <Box>
      <Typography 
        variant="h4" 
        mb={2}
        color={isDarkMode ? '#90CAF9' : '#1976d2'}
        sx={{ 
          fontWeight: 600,
          letterSpacing: '0.25px',
          textShadow: isDarkMode ? '0px 1px 2px rgba(0,0,0,0.3)' : 'none',
        }}
      >
        Search Clients
      </Typography>
      <Card 
        elevation={isDarkMode ? 2 : 1}
        sx={{ 
          borderColor: 'divider',
          backgroundColor: isDarkMode ? '#2D3748' : '#ffffff',
          boxShadow: isDarkMode 
            ? '0px 3px 3px -2px rgba(0,0,0,0.2),0px 3px 4px 0px rgba(0,0,0,0.14),0px 1px 8px 0px rgba(0,0,0,0.12)'
            : '0px 2px 1px -1px rgba(0,0,0,0.1),0px 1px 1px 0px rgba(0,0,0,0.07),0px 1px 3px 0px rgba(0,0,0,0.06)'
        }}
      >
        <CardContent sx={{ color: isDarkMode ? '#E2E8F0' : 'inherit' }}>
          <Box sx={{ mt: 1 }}>
            <TextField
              fullWidth
              id="search-clients"
              placeholder="Search by name or slug"
              variant="outlined"
              size="small"
              value={searchTerm}
              onChange={(e) => onSearch(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon 
                      sx={{ 
                        color: isDarkMode ? '#90CAF9' : '#1976d2',
                      }} 
                    />
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  '& fieldset': {
                    borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.23)' : 'rgba(0, 0, 0, 0.23)',
                  },
                  '&:hover fieldset': {
                    borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.4)' : 'rgba(0, 0, 0, 0.4)',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: theme.palette.primary.main,
                  },
                },
                '& .MuiInputAdornment-root': {
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.54)',
                },
                '& .MuiInputBase-input::placeholder': {
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.4)',
                  opacity: 1,
                },
              }}
            />
            <Box sx={{ 
              mt: 2,
              border: '1px solid', 
              borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)', 
              borderRadius: 1, 
              p: 1, 
              display: 'flex', 
              alignItems: 'center',
              justifyContent: 'center',
              bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.04)',
              boxShadow: isDarkMode ? '0px 1px 3px rgba(0, 0, 0, 0.2)' : 'none'
            }}>
              <Typography 
                variant="body1" 
                fontWeight="500"
                color={isDarkMode ? '#e2e8f0' : '#333333'}
                sx={{ 
                  '& strong': {
                    color: isDarkMode ? '#90CAF9' : '#1976d2',
                    fontWeight: 600
                  }
                }}
              >
                Total Clients: <strong>{clientCount}</strong>
              </Typography>
            </Box>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default ClientSearch; 