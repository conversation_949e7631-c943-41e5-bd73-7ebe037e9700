'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  LinearProgress,
  Grid,
  Chip,
  Divider,
  IconButton,
  Toolt<PERSON>,
  Alert,
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import SpeedIcon from '@mui/icons-material/Speed';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import { useAdminTheme } from 'views/Jeff/AdminThemeContext';
import { getAIJobProgress } from '../utils/aiApi';
  
const AIProgressTracker = ({
  jobId,
  jobData,
  onProgressUpdate,
  autoRefresh = true,
  refreshInterval = 5000,
}) => {
  const { mode: adminThemeMode } = useAdminTheme() || { mode: 'light' };
  const isDarkMode = adminThemeMode === 'dark';
  
  const [progress, setProgress] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  // Auto-refresh progress
  useEffect(() => {
    if (!autoRefresh || !jobId || jobData?.status === 'completed') return;

    const interval = setInterval(() => {
      fetchProgress();
    }, refreshInterval);

    // Initial fetch
    fetchProgress();

    return () => clearInterval(interval);
  }, [jobId, autoRefresh, refreshInterval, jobData?.status]);

  const fetchProgress = async () => {
    if (!jobId) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await getAIJobProgress(jobId);
      if (result.success) {
        setProgress(result);
        setLastUpdated(new Date());
        if (onProgressUpdate) {
          onProgressUpdate(result);
        }
      } else {
        setError(result.message);
      }
    } catch (err) {
      setError('Failed to fetch progress');
    } finally {
      setIsLoading(false);
    }
  };

  const formatTime = (seconds) => {
    if (!seconds) return 'N/A';
    if (seconds < 60) return `${Math.round(seconds)}s`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}m`;
    return `${Math.round(seconds / 3600)}h ${Math.round((seconds % 3600) / 60)}m`;
  };

  const getModelColor = (model) => {
    switch (model) {
      case 'openai': return '#10a37f';
      case 'claude': return '#d97706';
      case 'gemini': return '#4285f4';
      default: return '#6b7280';
    }
  };

  const progressData = progress || jobData;
  const progressPercentage = progressData?.progress || 0;
  const isCompleted = progressData?.status === 'completed';
  const isFailed = progressData?.status === 'failed';

  return (
    <Paper
      sx={{
        p: 3,
        backgroundColor: isDarkMode ? '#2D3748' : '#FFFFFF',
        border: `1px solid ${isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'}`,
        borderRadius: '8px',
      }}
    >
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <SmartToyIcon
          sx={{
            color: getModelColor(jobData?.aiModel),
            mr: 1,
          }}
        />
        <Typography
          variant="h6"
          sx={{
            fontWeight: 600,
            color: isDarkMode ? '#E2E8F0' : '#4A5568',
          }}
        >
          AI Job Progress
        </Typography>
        <Box sx={{ ml: 'auto', display: 'flex', alignItems: 'center' }}>
          {jobData?.aiModel && (
            <Chip
              label={jobData.aiModel === 'openai' ? 'GPT-4' : 
                     jobData.aiModel === 'claude' ? 'Claude' : 
                     jobData.aiModel === 'gemini' ? 'Gemini' : jobData.aiModel}
              size="small"
              sx={{
                backgroundColor: getModelColor(jobData.aiModel),
                color: 'white',
                mr: 1,
              }}
            />
          )}
          <Tooltip title="Refresh Progress">
            <IconButton
              size="small"
              onClick={fetchProgress}
              disabled={isLoading}
              sx={{
                color: isDarkMode ? '#90CAF9' : '#1976d2',
              }}
            >
              <RefreshIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Error Display */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Progress Bar */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          <Typography
            variant="body2"
            sx={{
              color: isDarkMode ? 'rgba(255,255,255,0.8)' : 'rgba(0,0,0,0.7)',
            }}
          >
            Progress
          </Typography>
          <Typography
            variant="body2"
            sx={{
              fontWeight: 600,
              color: isDarkMode ? '#E2E8F0' : '#4A5568',
            }}
          >
            {progressPercentage.toFixed(1)}%
          </Typography>
        </Box>
        <LinearProgress
          variant="determinate"
          value={progressPercentage}
          sx={{
            height: 8,
            borderRadius: 4,
            backgroundColor: isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
            '& .MuiLinearProgress-bar': {
              backgroundColor: isCompleted ? '#4caf50' : 
                             isFailed ? '#f44336' : 
                             getModelColor(jobData?.aiModel),
              borderRadius: 4,
            },
          }}
        />
      </Box>

      {/* Metrics Grid */}
      <Grid container spacing={2}>
        {/* Processed Count */}
        <Grid item xs={6} sm={3}>
          <Box sx={{ textAlign: 'center' }}>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 600,
                color: isDarkMode ? '#E2E8F0' : '#4A5568',
              }}
            >
              {progressData?.processedCount || 0}
            </Typography>
            <Typography
              variant="caption"
              sx={{
                color: isDarkMode ? 'rgba(255,255,255,0.6)' : 'rgba(0,0,0,0.5)',
              }}
            >
              Processed
            </Typography>
          </Box>
        </Grid>

        {/* Total Count */}
        <Grid item xs={6} sm={3}>
          <Box sx={{ textAlign: 'center' }}>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 600,
                color: isDarkMode ? '#E2E8F0' : '#4A5568',
              }}
            >
              {progressData?.totalCount || 0}
            </Typography>
            <Typography
              variant="caption"
              sx={{
                color: isDarkMode ? 'rgba(255,255,255,0.6)' : 'rgba(0,0,0,0.5)',
              }}
            >
              Total
            </Typography>
          </Box>
        </Grid>

        {/* Tokens Used */}
        <Grid item xs={6} sm={3}>
          <Box sx={{ textAlign: 'center' }}>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 600,
                color: isDarkMode ? '#E2E8F0' : '#4A5568',
              }}
            >
              {progressData?.tokensUsed ? progressData.tokensUsed.toLocaleString() : '0'}
            </Typography>
            <Typography
              variant="caption"
              sx={{
                color: isDarkMode ? 'rgba(255,255,255,0.6)' : 'rgba(0,0,0,0.5)',
              }}
            >
              Tokens
            </Typography>
          </Box>
        </Grid>

        {/* Cost */}
        <Grid item xs={6} sm={3}>
          <Box sx={{ textAlign: 'center' }}>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 600,
                color: getModelColor(jobData?.aiModel),
              }}
            >
              ${progressData?.costAccumulated ? progressData.costAccumulated.toFixed(4) : '0.0000'}
            </Typography>
            <Typography
              variant="caption"
              sx={{
                color: isDarkMode ? 'rgba(255,255,255,0.6)' : 'rgba(0,0,0,0.5)',
              }}
            >
              Cost
            </Typography>
          </Box>
        </Grid>
      </Grid>

      {/* Additional Info */}
      {progressData?.estimatedTimeRemaining && !isCompleted && (
        <>
          <Divider sx={{ my: 2 }} />
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <SpeedIcon
                sx={{
                  fontSize: 16,
                  color: isDarkMode ? 'rgba(255,255,255,0.6)' : 'rgba(0,0,0,0.5)',
                  mr: 1,
                }}
              />
              <Typography
                variant="body2"
                sx={{
                  color: isDarkMode ? 'rgba(255,255,255,0.8)' : 'rgba(0,0,0,0.7)',
                }}
              >
                Est. time remaining: {formatTime(progressData.estimatedTimeRemaining)}
              </Typography>
            </Box>
            {lastUpdated && (
              <Typography
                variant="caption"
                sx={{
                  color: isDarkMode ? 'rgba(255,255,255,0.5)' : 'rgba(0,0,0,0.4)',
                }}
              >
                Updated: {lastUpdated.toLocaleTimeString()}
              </Typography>
            )}
          </Box>
        </>
      )}

      {/* Status Message */}
      {progressData?.message && (
        <Box sx={{ mt: 2 }}>
          <Typography
            variant="body2"
            sx={{
              color: isDarkMode ? 'rgba(255,255,255,0.8)' : 'rgba(0,0,0,0.7)',
              fontStyle: 'italic',
              textAlign: 'center',
            }}
          >
            {progressData.message}
          </Typography>
        </Box>
      )}
    </Paper>
  );
};

export default AIProgressTracker;
