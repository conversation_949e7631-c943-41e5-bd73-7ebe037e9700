'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Typography,
} from '@mui/material';
import { getAxiosInstance } from 'config/axios';
import { API_ENDPOINTS } from 'config/api';
import SellerTable from './SellerTable';
import { debounce } from 'lodash';
import { useCallback } from 'react';

const SellerIdTab = ({ loading, setLoading, setNotification, isDarkMode }) => {
  const [countryCode, setCountryCode] = useState('');
  const [sellers, setSellers] = useState([]);
  const [sellerId, setSellerId] = useState('');
  const [searchSellerName, setSearchSellerName] = useState('');
  const [searchSellerId, setSearchSellerId] = useState('');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  });

  const getSellers = async (params = {}) => {
    setLoading(true);
    try {
      const mergedParams = {
        ...params,
        page: pagination.page,
        limit: pagination.limit,
      };
      const response = await getAxiosInstance().get(API_ENDPOINTS.LEX_SELLER, {
        params: mergedParams,
      });
      setSellers(response?.data?.sellers || []);
      setPagination(
        response?.data?.pagination || {
          page: 1,
          limit: 20,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false,
        },
      );
      setNotification({
        open: true,
        message: 'Sellers retrieved successfully',
        severity: 'success',
      });
    } catch (err) {
      setNotification({
        open: true,
        message: err.message || 'Failed to fetch sellers',
        severity: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const debouncedSearchByName = useCallback(
    debounce((name, getSellers) => {
      getSellers({ seller_name: name });
    }, 400),
    [],
  );
  
  const debouncedSearchBySellerId = useCallback(
    debounce((id, getSellers) => {
      getSellers({ seller_id: id });
    }, 400),
    [],
  );

  const handleAddSeller = async () => {
    if (!sellerId) {
      setNotification({
        open: true,
        message: 'Please enter a seller ID',
        severity: 'error',
      });

      return;
    }

    try {
      const response = await getAxiosInstance({
        cookiesKey: 'jeff-authorization',
      }).post(API_ENDPOINTS.LEX_SELLER, {
        seller_id: sellerId,
        ...(countryCode ? { country_code: countryCode } : {}),
      });

      console.log('response', response.data);

      await getSellers();

      setSellerId('');
      setCountryCode('');
      setNotification({
        open: true,
        message: 'Seller added successfully',
        severity: 'success',
      });
    } catch (err) {
      setNotification({
        open: true,
        message: err.message || 'Failed to add seller',
        severity: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (event, newPage) => {
    setPagination((prev) => ({
      ...prev,
      page: newPage + 1, // MUI uses 0-based index
    }));
  };
  const handleRowsPerPageChange = (event) => {
    setPagination((prev) => ({
      ...prev,
      page: 1,
      limit: parseInt(event.target.value, 10),
    }));
  };

  useEffect(() => {
    getSellers();
    // eslint-disable-next-line
  }, [pagination.page, pagination.limit]);

  return (
    <Box sx={{ mb: 3 }}>
      <Card
        sx={{
          mb: 3,
          backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        }}
      >
        <CardContent>
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              color: isDarkMode ? '#fff' : 'inherit',
              mb: 3,
            }}
          >
            Create Seller
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
            <TextField
              label="Add a new seller by ID"
              variant="outlined"
              value={sellerId}
              onChange={(e) => setSellerId(e.target.value)}
              size="small"
              sx={{
                flex: 1,
                '& .MuiOutlinedInput-root': {
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  '& fieldset': {
                    borderColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.23)'
                      : 'rgba(0, 0, 0, 0.23)',
                  },
                  '&:hover fieldset': {
                    borderColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.4)'
                      : 'rgba(0, 0, 0, 0.4)',
                  },
                },
                '& .MuiInputLabel-root': {
                  color: isDarkMode
                    ? 'rgba(255, 255, 255, 0.7)'
                    : 'rgba(0, 0, 0, 0.6)',
                },
              }}
            />
            <FormControl
              size="small"
              sx={{
                flex: 1,
                '& .MuiOutlinedInput-root': {
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  '& fieldset': {
                    borderColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.23)'
                      : 'rgba(0, 0, 0, 0.23)',
                  },
                  '&:hover fieldset': {
                    borderColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.4)'
                      : 'rgba(0, 0, 0, 0.4)',
                  },
                },
                '& .MuiInputLabel-root': {
                  color: isDarkMode
                    ? 'rgba(255, 255, 255, 0.7)'
                    : 'rgba(0, 0, 0, 0.6)',
                },
              }}
            >
              <InputLabel>Country Code</InputLabel>
              <Select
                value={countryCode}
                onChange={(e) => setCountryCode(e.target.value)}
                label="Country Code"
              >
                <MenuItem value="US">US</MenuItem>
                <MenuItem value="UK">UK</MenuItem>
                <MenuItem value="CA">CA</MenuItem>
              </Select>
            </FormControl>
          </Box>

          <Box
            sx={{ display: 'flex', gap: 2, mb: 3, justifyContent: 'flex-end' }}
          >
            <Button
              variant="contained"
              onClick={handleAddSeller}
              disabled={loading}
              sx={{
                backgroundColor: isDarkMode ? '#3182CE' : '#2f6ad9',
                '&:hover': {
                  backgroundColor: isDarkMode ? '#4299E1' : '#3b7be8',
                },
                minWidth: '120px',
              }}
            >
              {loading ? (
                <CircularProgress size={24} color="inherit" />
              ) : (
                'Create'
              )}
            </Button>
          </Box>
        </CardContent>
      </Card>
      <Card
        sx={{
          mb: 3,
          backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        }}
      >
        <CardContent>
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              color: isDarkMode ? '#fff' : 'inherit',
              mb: 3,
            }}
          >
            Filters
          </Typography>
          <Box sx={{ mb: 2 }}>
            <TextField
              fullWidth
              label="Search an existing seller by name"
              variant="outlined"
              size="small"
              value={searchSellerName}
              onChange={(e) => {
                const value = e.target.value;
                setSearchSellerName(value);
                debouncedSearchByName(value, getSellers);
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  '& fieldset': {
                    borderColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.23)'
                      : 'rgba(0, 0, 0, 0.23)',
                  },
                  '&:hover fieldset': {
                    borderColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.4)'
                      : 'rgba(0, 0, 0, 0.4)',
                  },
                },
                '& .MuiInputLabel-root': {
                  color: isDarkMode
                    ? 'rgba(255, 255, 255, 0.7)'
                    : 'rgba(0, 0, 0, 0.6)',
                },
              }}
            />
          </Box>
          <Box sx={{ mb: 2 }}>
            <TextField
              fullWidth
              label="Search an existing seller by seller id"
              variant="outlined"
              size="small"
              value={searchSellerId}
              onChange={(e) => {
                const value = e.target.value;
                setSearchSellerId(value);
                debouncedSearchBySellerId(value, getSellers);
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  '& fieldset': {
                    borderColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.23)'
                      : 'rgba(0, 0, 0, 0.23)',
                  },
                  '&:hover fieldset': {
                    borderColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.4)'
                      : 'rgba(0, 0, 0, 0.4)',
                  },
                },
                '& .MuiInputLabel-root': {
                  color: isDarkMode
                    ? 'rgba(255, 255, 255, 0.7)'
                    : 'rgba(0, 0, 0, 0.6)',
                },
              }}
            />
          </Box>
        </CardContent>
      </Card>
      <SellerTable
        sellers={sellers}
        isDarkMode={isDarkMode}
        pagination={pagination}
        onPageChange={handlePageChange}
        onRowsPerPageChange={handleRowsPerPageChange}
      />
    </Box>
  );
};

export default SellerIdTab;
