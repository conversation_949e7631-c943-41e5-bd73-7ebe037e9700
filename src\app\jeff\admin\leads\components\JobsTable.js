'use client';

import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  CircularProgress,
  LinearProgress,
  Checkbox,
  Typography,
  Box,
  Tooltip,
  Divider,
  Link,
  TablePagination,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import RefreshIcon from '@mui/icons-material/Refresh';
import InfoIcon from '@mui/icons-material/Info';
import { useAdminTheme } from 'views/Jeff/AdminThemeContext';
import DownloadButton from './DownloadButton';
import ExportLinksButton from './ExportLinksButton';

const JobsTable = ({
  jobs,
  selectedJobs,
  actionLoading,
  onJobSelection,
  onSelectAllJobs,
  onExportJob,
  onDeleteJob,
  onCheckStatus,
  onExportInputData,
  onExportLinks,
  isDarkMode,
  pagination,
  onPageChange,
  onRowsPerPageChange,
}) => {
  const { mode: adminThemeMode } = useAdminTheme() || { mode: 'light' };
  isDarkMode = isDarkMode || adminThemeMode === 'dark';

  // Helper function to format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };


  // Get styling for status
  const getStatusInfo = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return {
          text: 'Completed',
          color: isDarkMode ? '#68D391' : '#48BB78',
          bgColor: isDarkMode
            ? 'rgba(104, 211, 145, 0.15)'
            : 'rgba(72, 187, 120, 0.1)',
        };
      case 'pending':
        return {
          text: 'In Progress',
          color: isDarkMode ? '#F6E05E' : '#ECC94B',
          bgColor: isDarkMode
            ? 'rgba(246, 224, 94, 0.15)'
            : 'rgba(236, 201, 75, 0.1)',
        };
      case 'failed':
        return {
          text: 'Failed',
          color: isDarkMode ? '#FC8181' : '#F56565',
          bgColor: isDarkMode
            ? 'rgba(252, 129, 129, 0.15)'
            : 'rgba(245, 101, 101, 0.1)',
        };
      default:
        return {
          text: status || 'Unknown',
          color: isDarkMode ? '#A0AEC0' : '#718096',
          bgColor: isDarkMode
            ? 'rgba(160, 174, 192, 0.15)'
            : 'rgba(113, 128, 150, 0.1)',
        };
    }
  };

  // Helper function to format job statistics
  const formatJobStats = (job) => {
    const stats = job.resultJson?.stats || {};
    const validatedData = job.resultJson?.validatedData || {};

    return {
      totalUrls: stats.totalUrls || 0,
      totalLeads: stats.totalLeads || job.totalLeads || 0,
      validatedUrls: stats.validatedUrls || 0,
      confidenceFilteredUrls: stats.confidenceFilteredUrls || 0,
      validatedPercentage: validatedData.percentage || 0,
      completionPercentage: job.completionPercentage || 0,
    };
  };

  return (
    <>
      <TableContainer
        component={Paper}
        sx={{
          backgroundColor: isDarkMode ? '#1A202C' : '#FFFFFF',
          boxShadow: isDarkMode
            ? '0 4px 6px rgba(0, 0, 0, 0.25)'
            : '0 1px 3px rgba(0, 0, 0, 0.1)',
        }}
      >
        <Table sx={{ minWidth: 650 }} aria-label="lead jobs table">
          <TableHead>
            <TableRow
              sx={{ backgroundColor: isDarkMode ? '#2D3748' : '#F7FAFC' }}
            >
              <TableCell padding="checkbox">
                <Checkbox
                  indeterminate={
                    selectedJobs.length > 0 && selectedJobs.length < jobs.length
                  }
                  checked={
                    jobs.length > 0 && selectedJobs.length === jobs.length
                  }
                  onChange={onSelectAllJobs}
                  sx={{
                    color: isDarkMode
                      ? 'rgba(255, 255, 255, 0.5)'
                      : 'rgba(0, 0, 0, 0.4)',
                    '&.Mui-checked': {
                      color: isDarkMode ? '#90CAF9' : '#1976d2',
                    },
                  }}
                />
              </TableCell>
              <TableCell
                sx={{
                  fontWeight: 600,
                  color: isDarkMode ? '#E2E8F0' : '#4A5568',
                }}
              >
                ID
              </TableCell>
              <TableCell
                sx={{
                  fontWeight: 600,
                  color: isDarkMode ? '#E2E8F0' : '#4A5568',
                }}
              >
                CSV / Job Name
              </TableCell>
              <TableCell
                sx={{
                  fontWeight: 600,
                  color: isDarkMode ? '#E2E8F0' : '#4A5568',
                }}
              >
                Mode
              </TableCell>
              <TableCell
                sx={{
                  fontWeight: 600,
                  color: isDarkMode ? '#E2E8F0' : '#4A5568',
                }}
              >
                AI Model
              </TableCell>
              <TableCell
                sx={{
                  fontWeight: 600,
                  color: isDarkMode ? '#E2E8F0' : '#4A5568',
                }}
              >
                Cost
              </TableCell>
              <TableCell
                sx={{
                  fontWeight: 600,
                  color: isDarkMode ? '#E2E8F0' : '#4A5568',
                }}
              >
                Status
              </TableCell>
              <TableCell
                sx={{
                  fontWeight: 600,
                  color: isDarkMode ? '#E2E8F0' : '#4A5568',
                }}
              >
                Lead Count
              </TableCell>
              <TableCell
                sx={{
                  fontWeight: 600,
                  color: isDarkMode ? '#E2E8F0' : '#4A5568',
                }}
              >
                Progress
              </TableCell>
              <TableCell
                sx={{
                  fontWeight: 600,
                  color: isDarkMode ? '#E2E8F0' : '#4A5568',
                }}
              >
                Created At
              </TableCell>
              <TableCell
                sx={{
                  fontWeight: 600,
                  color: isDarkMode ? '#E2E8F0' : '#4A5568',
                }}
              >
                Actions
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {jobs.map((job) => {
              const statusInfo = getStatusInfo(job.status);
              const jobName = job.inputCsvName || job.name;
              const isActionDisabled =
                actionLoading === job.id ||
                actionLoading === `status-${job.id}`;
              const stats = formatJobStats(job);

              return (
                <TableRow
                  key={job.id}
                  hover
                  sx={{
                    '&:hover': {
                      backgroundColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.05)'
                        : 'rgba(0, 0, 0, 0.02)',
                    },
                    backgroundColor: selectedJobs.includes(job.id)
                      ? isDarkMode
                        ? 'rgba(44, 82, 130, 0.2)'
                        : 'rgba(49, 130, 206, 0.1)'
                      : 'transparent',
                  }}
                >
                  <TableCell padding="checkbox">
                    <Checkbox
                      checked={selectedJobs.includes(job.id)}
                      onChange={() => onJobSelection(job.id)}
                      sx={{
                        color: isDarkMode
                          ? 'rgba(255, 255, 255, 0.5)'
                          : 'rgba(0, 0, 0, 0.4)',
                        '&.Mui-checked': {
                          color: isDarkMode ? '#90CAF9' : '#1976d2',
                        },
                      }}
                    />
                  </TableCell>
                  <TableCell sx={{ color: isDarkMode ? '#E2E8F0' : '#4A5568' }}>
                    {job.id}
                  </TableCell>
                  <TableCell sx={{ color: isDarkMode ? '#E2E8F0' : '#4A5568' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Typography>{jobName}</Typography>
                      <Box sx={{ ml: 1 }}>
                        <DownloadButton
                          jobId={job.id}
                          type="input"
                          isDarkMode={isDarkMode}
                          onError={(error) =>
                            console.error('Download error:', error)
                          }
                          fallbackHandler={onExportInputData}
                          tooltipTitle="Export Input Data"
                        />
                      </Box>
                    </Box>
                  </TableCell>

                  {/* Mode Column */}
                  <TableCell sx={{ color: isDarkMode ? '#E2E8F0' : '#4A5568' }}>
                    {job.mode ? (
                      <Chip
                        label={job.mode === 'serp' ? 'SERP' : job.mode === 'ai' ? 'AI' : 'Input'}
                        size="small"
                        sx={{
                          backgroundColor: job.mode === 'serp' ? '#1976d2' :
                                         job.mode === 'ai' ? '#2e7d32' : '#7b1fa2',
                          color: 'white',
                          fontWeight: 500,
                          fontSize: '0.7rem',
                        }}
                      />
                    ) : (
                      <Chip
                        label="SERP"
                        size="small"
                        sx={{
                          backgroundColor: '#1976d2',
                          color: 'white',
                          fontWeight: 500,
                          fontSize: '0.7rem',
                        }}
                      />
                    )}
                  </TableCell>

                  {/* AI Model Column */}
                  <TableCell sx={{ color: isDarkMode ? '#E2E8F0' : '#4A5568' }}>
                    {job.mode === 'ai' && job.aiModel ? (
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {job.aiModel === 'openai' ? 'GPT-4' :
                         job.aiModel === 'claude' ? 'Claude' :
                         job.aiModel === 'gemini' ? 'Gemini' : job.aiModel}
                      </Typography>
                    ) : (
                      <Typography variant="body2" sx={{ color: 'rgba(128,128,128,0.6)', fontStyle: 'italic' }}>
                        N/A
                      </Typography>
                    )}
                  </TableCell>

                  {/* Cost Column */}
                  <TableCell sx={{ color: isDarkMode ? '#E2E8F0' : '#4A5568' }}>
                    {job.mode === 'ai' && job.costAccumulated !== undefined ? (
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        ${job.costAccumulated.toFixed(4)}
                      </Typography>
                    ) : (
                      <Typography variant="body2" sx={{ color: 'rgba(128,128,128,0.6)', fontStyle: 'italic' }}>
                        N/A
                      </Typography>
                    )}
                  </TableCell>

                  <TableCell>
                    <Chip
                      label={statusInfo.text}
                      size="small"
                      sx={{
                        backgroundColor: statusInfo.bgColor,
                        color: statusInfo.color,
                        fontWeight: 500,
                        fontSize: '0.8rem',
                      }}
                    />
                  </TableCell>
                  <TableCell sx={{ color: isDarkMode ? '#E2E8F0' : '#4A5568' }}>
                    <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                      <Typography
                        variant="body2"
                        color={isDarkMode ? '#e2e8f0' : '#333333'}
                        fontSize="0.75rem"
                      >
                        {job.statusMap?.completed || 0} / {stats.totalLeads}{' '}
                        leads
                      </Typography>
                      {job.statusMap &&
                        Object.entries(job.statusMap)
                          .filter(([key]) => key !== 'completed')
                          .map(([key, value]) => (
                            <Typography
                              key={key}
                              variant="body2"
                              color={
                                isDarkMode
                                  ? 'rgba(255, 255, 255, 0.6)'
                                  : 'rgba(0, 0, 0, 0.6)'
                              }
                              fontSize="0.75rem"
                              sx={{ mt: 0.5 }}
                            >
                              {key}: {value || 0}
                            </Typography>
                          ))}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        width: '100%',
                      }}
                    >
                      <Box sx={{ width: '100%', mr: 1 }}>
                        <LinearProgress
                          variant="determinate"
                          value={stats.completionPercentage}
                          sx={{
                            height: 8,
                            borderRadius: 4,
                            backgroundColor: isDarkMode
                              ? 'rgba(255, 255, 255, 0.1)'
                              : 'rgba(0, 0, 0, 0.06)',
                            '& .MuiLinearProgress-bar': {
                              borderRadius: 4,
                              backgroundColor: statusInfo.color,
                            },
                          }}
                        />
                      </Box>
                      <Box sx={{ minWidth: 35 }}>
                        <Typography
                          variant="body2"
                          sx={{ color: isDarkMode ? '#E2E8F0' : '#4A5568' }}
                        >
                          {stats.completionPercentage}%
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell sx={{ color: isDarkMode ? '#E2E8F0' : '#4A5568' }}>
                    {formatDate(job.createdAt)}
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex' }}>
                      <DownloadButton
                        jobId={job.id}
                        type="results"
                        isDarkMode={isDarkMode}
                        onError={(error) =>
                          console.error('Download error:', error)
                        }
                        fallbackHandler={onExportJob}
                        tooltipTitle="Export Results"
                      />

                      <ExportLinksButton
                        jobId={job.id}
                        onExportLinks={onExportLinks}
                        isLoading={actionLoading === `links-${job.id}`}
                        isDarkMode={isDarkMode}
                        existingData={job.resultJson}
                      />

                      <Tooltip title="Refresh Status">
                        <IconButton
                          onClick={() => onCheckStatus(job.id)}
                          disabled={isActionDisabled}
                          size="small"
                          sx={{
                            color: isDarkMode ? '#A0AEC0' : '#718096',
                            '&:hover': {
                              backgroundColor: isDarkMode
                                ? 'rgba(160, 174, 192, 0.1)'
                                : 'rgba(113, 128, 150, 0.1)',
                            },
                          }}
                        >
                          {actionLoading === `status-${job.id}` ? (
                            <CircularProgress size={20} color="inherit" />
                          ) : (
                            <RefreshIcon fontSize="small" />
                          )}
                        </IconButton>
                      </Tooltip>

                      {(job.status?.toLowerCase() === 'completed' || job.resultJson?.googleSheet?.url) && (
                        <Tooltip
                          title={
                            <Box sx={{ p: 1 }}>
                              <Typography variant="subtitle2" fontWeight={600}>
                                Job Statistics
                              </Typography>
                              <Typography variant="body2">
                                Total URLs: {stats.totalUrls}
                              </Typography>
                              <Typography variant="body2">
                                Total Leads: {stats.totalLeads}
                              </Typography>
                              <Typography variant="body2">
                                Validated URLs: {stats.validatedUrls}
                              </Typography>
                              <Typography variant="body2">
                                Confidence Filtered URLs:{' '}
                                {stats.confidenceFilteredUrls}
                              </Typography>
                              {/* <Typography variant="body2">
                                Validated Data:{' '}
                                {job.resultJson?.validatedData?.count || 0} (
                                {stats.validatedPercentage}%)
                              </Typography> */}
                              <Box sx={{ mt: 1 }}>
                                {job.resultJson?.googleSheet?.url ? (
                                  <>
                                    <Divider sx={{ my: 1 }} />
                                    <Typography
                                      variant="subtitle2"
                                      fontWeight={600}
                                    >
                                      Google Sheets
                                    </Typography>
                                    <Typography
                                      variant="body2"
                                      sx={{ wordBreak: 'break-all' }}
                                    >
                                      <Link
                                        href={job.resultJson.googleSheet.url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        sx={{
                                          color: isDarkMode
                                            ? '#63B3ED'
                                            : '#3182CE',
                                        }}
                                      >
                                        View Spreadsheet
                                      </Link>
                                    </Typography>
                                    {job.resultJson.googleSheet.sheets &&
                                      job.resultJson.googleSheet.sheets.length >
                                        0 && (
                                      <Box sx={{ mt: 1 }}>
                                        {job.resultJson.googleSheet.sheets.map(
                                          (sheet, index) => (
                                            <Typography
                                              key={index}
                                              variant="body2"
                                            >
                                              • {sheet.name}: {sheet.rows}{' '}
                                              rows
                                            </Typography>
                                          ),
                                        )}
                                      </Box>
                                    )}
                                  </>
                                ) : null}
                              </Box>
                            </Box>
                          }
                          arrow
                        >
                          <IconButton
                            size="small"
                            sx={{
                              color: isDarkMode ? '#63B3ED' : '#3182CE',
                              '&:hover': {
                                backgroundColor: isDarkMode
                                  ? 'rgba(99, 179, 237, 0.1)'
                                  : 'rgba(49, 130, 206, 0.1)',
                              },
                            }}
                          >
                            <InfoIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}

                      <Tooltip title="Delete">
                        <IconButton
                          onClick={() => onDeleteJob(job.id)}
                          disabled={isActionDisabled}
                          size="small"
                          sx={{
                            color: isDarkMode ? '#FC8181' : '#E53E3E',
                            '&:hover': {
                              backgroundColor: isDarkMode
                                ? 'rgba(252, 129, 129, 0.1)'
                                : 'rgba(229, 62, 62, 0.1)',
                            },
                          }}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              );
            })}

            {jobs.length === 0 && (
              <TableRow>
                <TableCell
                  colSpan={8}
                  align="center"
                  sx={{ py: 5, color: isDarkMode ? '#A0AEC0' : '#718096' }}
                >
                  <Typography variant="body1">No lead jobs found</Typography>
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    Create a new lead job to get started
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {pagination && (
        <TablePagination
          component="div"
          count={pagination.total}
          page={pagination.page - 1}
          onPageChange={(event, newPage) => onPageChange(newPage + 1)}
          rowsPerPage={pagination.limit}
          onRowsPerPageChange={(event) =>
            onRowsPerPageChange(parseInt(event.target.value, 10))
          }
          rowsPerPageOptions={[5, 10, 25, 50]}
          sx={{
            color: isDarkMode ? '#E2E8F0' : '#4A5568',
            '.MuiTablePagination-selectIcon': {
              color: isDarkMode ? '#E2E8F0' : '#4A5568',
            },
            '.MuiTablePagination-select': {
              color: isDarkMode ? '#E2E8F0' : '#4A5568',
            },
          }}
        />
      )}
    </>
  );
};

export default JobsTable;
