import { useQuery } from '@tanstack/react-query';
import { API_ENDPOINTS, getBaseURL } from '../../config/api';
import { getBrandbuddyAxiosInstance } from '../../config/axios';
// import { cookies } from 'next/headers';
import Cookies from 'js-cookie';

const isServer = typeof window === 'undefined';

const getJobs = async () => {
  const baseUrl = getBaseURL();
  let authorization;
  if (isServer) {
    const { cookies } = await import('next/headers');
    const cookieStore = cookies();
    authorization = cookieStore.get('authorization').value;
  } else {
    authorization = Cookies.get('authorization');
  }
  const headers = new Headers();
  headers.append('authorization', authorization);

  const url = baseUrl + API_ENDPOINTS.JOBS;

  const res = await fetch(url, {
    method: 'GET',
    headers,
  });
  return await res.json();
};

const getABOptions = async () => {
  let authorization;
  if (isServer) {
    const { cookies } = await import('next/headers');
    const cookieStore = cookies();
    authorization = cookieStore.get('authorization').value;
  } else {
    authorization = Cookies.get('authorization');
  }

  const response = await getBrandbuddyAxiosInstance().get(API_ENDPOINTS.AB_OPTIONS, {
    headers: {
      Authorization: authorization,
    },
  });

  return response.data?.options;
};

const useABOptions = () => {
  return useQuery({
    queryKey: ['abOptions'],
    queryFn: getABOptions,
  });
};

export { getJobs, getABOptions, useABOptions };
