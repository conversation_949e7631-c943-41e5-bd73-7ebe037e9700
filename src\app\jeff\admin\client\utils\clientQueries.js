import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { API_ENDPOINTS, getJeffBaseURL } from 'config/api';
import getAxiosInstance from 'config/axios';
import { fetchSignup } from 'views/Jeff/JeffSignUp';
import Cookies from 'js-cookie';

const isServer = typeof window === 'undefined';
const cookiesKey = 'jeff-authorization';
const baseUrl = getJeffBaseURL();

// Query keys
export const clientKeys = {
  all: ['clients'],
  list: () => [...clientKeys.all, 'list'],
  detail: (id) => [...clientKeys.all, 'detail', id],
};

// API functions
const fetchClients = async () => {
  let authorization;
  if (isServer) {
    const { cookies } = await import('next/headers');
    const cookieStore = cookies();
    authorization = cookieStore.get(cookiesKey)?.value;
  } else {
    authorization = Cookies.get(cookiesKey);
  }

  // Return early if no token is found
  if (!authorization) {
    throw new Error('Failed to load clients: No authorization token found');
  }

  try {
    const axiosInstance = getAxiosInstance({
      baseUrl: baseUrl,
      cookiesKey: cookiesKey,
    });
    const response = await axiosInstance.get(API_ENDPOINTS.JEFF_CLIENTS);
    return response.data;
  } catch (error) {
    console.error('Failed to load clients:', error);
    throw new Error(
      'Failed to load clients: ' +
        (error.response?.data?.error || error.message),
    );
  }
};

const createClient = async (clientData) => {
  try {
    // Make the signup API call and get the response
    const response = await fetchSignup(clientData, baseUrl);
    console.log('REPOSNE OF SIGNUP', response, response.data);
    // If we have a response with a success message, return it
    if (response) {
      return response;
    }

    // If we get here, something unexpected happened
    throw new Error('Failed to create client: Unexpected response');
  } catch (error) {
    console.error('Error creating client:', error);
    throw new Error(
      error.response?.data?.error || error.message || 'Failed to create client',
    );
  }
};

const getClientById = async (clientId) => {
  let authorization;
  if (isServer) {
    const { cookies } = await import('next/headers');
    const cookieStore = cookies();
    authorization = cookieStore.get(cookiesKey)?.value;
    if (!authorization) {
      throw new Error('No authorization token found');
    }
  } else {
    authorization = Cookies.get(cookiesKey);
    if (!authorization) {
      throw new Error('No authorization token found');
    }
  }

  try {
    const axiosInstance = getAxiosInstance({
      baseUrl: baseUrl,
      cookiesKey: cookiesKey,
    });
    const response = await axiosInstance.get(
      `${API_ENDPOINTS.JEFF_USER}/${clientId}`,
    );

    // Ensure all necessary fields exist
    const userData = response.data;

    // Add default values for any missing fields
    return {
      ...userData,
      logo: userData.logo || '',
      mbLogo: userData.mbLogo || '',
      tableLogo: userData.tableLogo || '',
      bgColor: userData.bgColor || '',
      signature: userData.signature || '',
      ctaLink: userData.ctaLink || '',
      website: userData.website || '',
      offerLine: userData.offerLine || '',
      ctaButtonText: userData.ctaButtonText || '',
      googleSheetURL: userData.googleSheetURL || '',
      caseStudiesText: userData.caseStudiesText || 'Read full case study here',
      caseStudies: userData.caseStudies || {},
      testimonials: userData.testimonials || [],
      singleEntryOpt: userData.singleEntryOpt || false,
    };
  } catch (error) {
    console.error('Error fetching client data:', error);
    throw error;
  }
};

const updateClient = async ({ clientId, clientData }) => {
  try {
    const axiosInstance = getAxiosInstance({
      baseUrl: baseUrl,
      cookiesKey: cookiesKey,
    });
    const response = await axiosInstance.put(
      `${API_ENDPOINTS.JEFF_USER}/${clientId}`,
      clientData,
    );
    return response.data;
  } catch (error) {
    console.error('Error updating client:', error);
    throw error;
  }
};

// React Query hooks
export const useClients = () => {
  return useQuery({
    queryKey: ['jeff-clients'],
    queryFn: fetchClients,
    retry: 1,
    onError: (error) => {
      console.error('Client fetch error:', error);
    },
  });
};

export const useClient = (clientId) => {
  return useQuery({
    queryKey: ['jeff-client', parseInt(clientId)],
    queryFn: () => getClientById(clientId),
    enabled: !!clientId,
  });
};

export const useCreateClient = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createClient,
    onSuccess: () => {
      // Invalidate and refetch clients list
      queryClient.invalidateQueries({ queryKey: ['jeff-clients'] });
    },
  });
};

export const useUpdateClient = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateClient,
    onSuccess: (data, variables) => {
      // Invalidate and refetch both the client list and the specific client
      queryClient.invalidateQueries({ queryKey: ['jeff-clients'] });
      queryClient.invalidateQueries({
        queryKey: ['jeff-client', parseInt(variables.clientId)],
      });
    },
  });
};
