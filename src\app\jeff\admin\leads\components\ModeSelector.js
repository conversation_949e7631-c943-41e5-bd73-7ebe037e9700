'use client';

import React from 'react';
import {
  Box,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
  Chip,
  Paper,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import InputIcon from '@mui/icons-material/Input';
import { useAdminTheme } from 'views/Jeff/AdminThemeContext';

const ModeSelector = ({ mode, onModeChange, disabled = false }) => {
  const { mode: adminThemeMode } = useAdminTheme() || { mode: 'light' };
  const isDarkMode = adminThemeMode === 'dark';

  const modes = [
    {
      value: 'serp',
      label: 'SERP Scraper',
      icon: <SearchIcon />,
      description: 'Traditional search engine result scraping',
      color: '#1976d2', // Blue
      available: true,
    },
    {
      value: 'ai',
      label: 'AI Website Finder',
      icon: <SmartToyIcon />,
      description: 'AI-powered website discovery and analysis',
      color: '#2e7d32', // Green
      available: true,
    },
    {
      value: 'input',
      label: 'Input Domain',
      icon: <InputIcon />,
      description: 'Direct domain input processing',
      color: '#7b1fa2', // Purple
      available: true,
    },
  ];

  const handleModeChange = (event, newMode) => {
    if (newMode !== null && !disabled) {
      onModeChange(newMode);
    }
  };

  return (
    <Paper
      sx={{
        p: 3,
        mb: 3,
        backgroundColor: isDarkMode ? '#2D3748' : '#FFFFFF',
        boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
        borderRadius: '8px',
      }}
    >
      <Typography
        variant="h6"
        sx={{
          mb: 2,
          color: isDarkMode ? '#E2E8F0' : '#4A5568',
          fontWeight: 600,
        }}
      >
        Lead Generation Mode
      </Typography>
      
      <ToggleButtonGroup
        value={mode}
        exclusive
        onChange={handleModeChange}
        aria-label="lead generation mode"
        sx={{
          display: 'flex',
          flexWrap: 'wrap',
          gap: 1,
          '& .MuiToggleButton-root': {
            flex: '1 1 300px',
            minHeight: '80px',
            border: `2px solid ${isDarkMode ? 'rgba(255,255,255,0.12)' : 'rgba(0,0,0,0.12)'}`,
            borderRadius: '8px',
            textTransform: 'none',
            color: isDarkMode ? '#E2E8F0' : '#4A5568',
            backgroundColor: isDarkMode ? '#1A202C' : '#F7FAFC',
            '&:hover': {
              backgroundColor: isDarkMode ? '#2D3748' : '#EDF2F7',
            },
            '&.Mui-selected': {
              backgroundColor: isDarkMode ? 'rgba(144, 202, 249, 0.16)' : 'rgba(25, 118, 210, 0.08)',
              borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
              color: isDarkMode ? '#90CAF9' : '#1976d2',
              '&:hover': {
                backgroundColor: isDarkMode ? 'rgba(144, 202, 249, 0.24)' : 'rgba(25, 118, 210, 0.12)',
              },
            },
            '&.Mui-disabled': {
              opacity: 0.5,
              cursor: 'not-allowed',
            },
          },
        }}
      >
        {modes.map((modeOption) => (
          <ToggleButton
            key={modeOption.value}
            value={modeOption.value}
            disabled={disabled || !modeOption.available}
            sx={{
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              p: 2,
              '&.Mui-selected': {
                borderColor: modeOption.color,
                color: modeOption.color,
                backgroundColor: isDarkMode 
                  ? `${modeOption.color}20` 
                  : `${modeOption.color}10`,
              },
            }}
          >
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                mb: 1,
                color: mode === modeOption.value ? modeOption.color : 'inherit',
              }}
            >
              {React.cloneElement(modeOption.icon, { sx: { mr: 1, fontSize: 24 } })}
              <Typography variant="subtitle1" fontWeight={600}>
                {modeOption.label}
              </Typography>
              {modeOption.value === 'ai' && (
                <Chip
                  label="NEW"
                  size="small"
                  sx={{
                    ml: 1,
                    height: 20,
                    fontSize: '0.7rem',
                    backgroundColor: '#4caf50',
                    color: 'white',
                  }}
                />
              )}
            </Box>
            <Typography
              variant="caption"
              sx={{
                textAlign: 'center',
                color: isDarkMode ? 'rgba(255,255,255,0.7)' : 'rgba(0,0,0,0.6)',
                lineHeight: 1.2,
              }}
            >
              {modeOption.description}
            </Typography>
          </ToggleButton>
        ))}
      </ToggleButtonGroup>
      
      {mode && (
        <Box sx={{ mt: 2 }}>
          <Typography
            variant="body2"
            sx={{
              color: isDarkMode ? 'rgba(255,255,255,0.8)' : 'rgba(0,0,0,0.7)',
              fontStyle: 'italic',
            }}
          >
            Selected: {modes.find(m => m.value === mode)?.label}
          </Typography>
        </Box>
      )}
    </Paper>
  );
};

export default ModeSelector;
