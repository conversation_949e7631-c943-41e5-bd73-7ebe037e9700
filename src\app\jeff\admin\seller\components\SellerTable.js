'use client';

import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Box,
  TablePagination,
} from '@mui/material';
import { useRouter } from 'next/navigation';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';

const SellerTable = ({
  sellers,
  isDarkMode,
  pagination,
  onPageChange,
  onRowsPerPageChange,
}) => {
  const router = useRouter();
  const [sortConfig, setSortConfig] = useState({
    key: 'createdAt',
    direction: 'desc',
  });

  const handleSort = (key) => {
    setSortConfig((currentSort) => ({
      key,
      direction:
        currentSort.key === key && currentSort.direction === 'asc'
          ? 'desc'
          : 'asc',
    }));
  };

  const getSortedData = (data) => {
    if (!sortConfig.key) return data;

    return [...data].sort((a, b) => {
      let aValue = a[sortConfig.key];
      let bValue = b[sortConfig.key];

      // Handle date sorting
      if (sortConfig.key === 'createdAt') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      // Handle numeric sorting
      if (sortConfig.key === 'totalAsins') {
        aValue = Number(aValue) || 0;
        bValue = Number(bValue) || 0;
      }

      // Handle string sorting
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  };

  const SortableTableCell = ({ label, sortKey, width }) => (
    <TableCell
      onClick={() => handleSort(sortKey)}
      sx={{
        backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
        color: isDarkMode ? '#e2e8f0' : '#4a5568',
        fontWeight: 600,
        borderBottom: `1px solid ${
          isDarkMode ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)'
        }`,
        cursor: 'pointer',
        width: width,
        '&:hover': {
          backgroundColor: isDarkMode ? '#4a5568' : '#edf2f7',
        },
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
        {label}
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            ml: 0.5,
            height: '20px',
          }}
        >
          <ArrowUpwardIcon
            sx={{
              fontSize: '0.875rem',
              color:
                sortConfig.key === sortKey && sortConfig.direction === 'asc'
                  ? isDarkMode
                    ? '#90CAF9'
                    : '#1976d2'
                  : isDarkMode
                    ? 'rgba(255, 255, 255, 0.3)'
                    : 'rgba(0, 0, 0, 0.3)',
              height: '10px',
              lineHeight: '10px',
            }}
          />
          <ArrowDownwardIcon
            sx={{
              fontSize: '0.875rem',
              color:
                sortConfig.key === sortKey && sortConfig.direction === 'desc'
                  ? isDarkMode
                    ? '#90CAF9'
                    : '#1976d2'
                  : isDarkMode
                    ? 'rgba(255, 255, 255, 0.3)'
                    : 'rgba(0, 0, 0, 0.3)',
              height: '10px',
              lineHeight: '10px',
            }}
          />
        </Box>
      </Box>
    </TableCell>
  );

  const handleRowClick = (sellerId) => {
    if (sellerId) {
      router.push(`/jeff/admin/seller?tabID=1&sellerId=${sellerId}`);
    }
  };

  const sortedSellers = getSortedData(sellers || []);

  return (
    <TableContainer
      component={Paper}
      sx={{
        backgroundColor: isDarkMode ? '#1a202c' : '#fff',
        boxShadow: isDarkMode
          ? '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)'
          : '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        borderRadius: '8px',
        overflow: 'hidden',
      }}
    >
      <Table>
        <TableHead>
          <TableRow>
            <SortableTableCell
              label="Seller ID"
              sortKey="sellerId"
              width="180px"
            />
            <SortableTableCell label="Name" sortKey="name" />
            <SortableTableCell
              label="Total ASIN"
              sortKey="totalAsins"
              width="120px"
            />
            <TableCell width="150px">Status</TableCell>
            <SortableTableCell
              label="Country Code"
              sortKey="countryCode"
              width="120px"
            />
            <SortableTableCell
              label="Created At"
              sortKey="createdAt"
              width="180px"
            />
          </TableRow>
        </TableHead>
        <TableBody>
          {sortedSellers && sortedSellers.length > 0 ? (
            sortedSellers.map((seller) => (
              <TableRow
                key={seller.id}
                hover
                sx={{
                  cursor: 'pointer',
                  '&:nth-of-type(odd)': {
                    backgroundColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.05)'
                      : '#f7fafc',
                  },
                  '&:hover': {
                    backgroundColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.08)'
                      : '#edf2f7',
                  },
                }}
                onClick={() => handleRowClick(seller.sellerId)}
              >
                <TableCell
                  sx={{
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    borderBottom: `1px solid ${
                      isDarkMode
                        ? 'rgba(255, 255, 255, 0.12)'
                        : 'rgba(0, 0, 0, 0.12)'
                    }`,
                  }}
                >
                  {seller.sellerId}
                </TableCell>
                <TableCell
                  sx={{
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    borderBottom: `1px solid ${
                      isDarkMode
                        ? 'rgba(255, 255, 255, 0.12)'
                        : 'rgba(0, 0, 0, 0.12)'
                    }`,
                  }}
                >
                  {seller.name || '-'}
                </TableCell>

                <TableCell
                  sx={{
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    borderBottom: `1px solid ${
                      isDarkMode
                        ? 'rgba(255, 255, 255, 0.12)'
                        : 'rgba(0, 0, 0, 0.12)'
                    }`,
                  }}
                >
                  {seller.totalAsins}
                </TableCell>

                <TableCell
                  sx={{
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    borderBottom: `1px solid ${
                      isDarkMode
                        ? 'rgba(255, 255, 255, 0.12)'
                        : 'rgba(0, 0, 0, 0.12)'
                    }`,
                  }}
                >
                  {seller.status ? (
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 0.5,
                      }}
                    >
                      <span
                        style={{
                          color:
                            seller.status === 'active' ? '#38a169' : '#e53e3e',
                        }}
                      >
                        {seller.status}
                      </span>
                    </Box>
                  ) : (
                    '-'
                  )}
                </TableCell>
                <TableCell
                  sx={{
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    borderBottom: `1px solid ${
                      isDarkMode
                        ? 'rgba(255, 255, 255, 0.12)'
                        : 'rgba(0, 0, 0, 0.12)'
                    }`,
                  }}
                >
                  {seller.countryCode}
                </TableCell>
                <TableCell
                  sx={{
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    borderBottom: `1px solid ${
                      isDarkMode
                        ? 'rgba(255, 255, 255, 0.12)'
                        : 'rgba(0, 0, 0, 0.12)'
                    }`,
                  }}
                >
                  {new Date(seller.createdAt).toLocaleDateString()}
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell
                colSpan={5}
                align="center"
                sx={{
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  borderBottom: `1px solid ${
                    isDarkMode
                      ? 'rgba(255, 255, 255, 0.12)'
                      : 'rgba(0, 0, 0, 0.12)'
                  }`,
                }}
              >
                No sellers found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      {pagination && (
        <TablePagination
          component="div"
          count={pagination.total}
          page={pagination.page - 1}
          onPageChange={onPageChange}
          rowsPerPage={pagination.limit}
          onRowsPerPageChange={onRowsPerPageChange}
          rowsPerPageOptions={[10, 20, 50, 100]}
          labelDisplayedRows={({ from, to, count }) =>
            `${from}-${to} of ${count} (${Math.ceil(
              count / pagination.limit,
            )} pages)`
          }
          sx={{
            color: isDarkMode ? '#fff' : 'inherit',
            '.MuiTablePagination-select': {
              color: isDarkMode ? '#fff' : 'inherit',
            },
            '.MuiTablePagination-selectIcon': {
              color: isDarkMode ? '#fff' : 'inherit',
            },
          }}
        />
      )}
    </TableContainer>
  );
};

export default SellerTable;
