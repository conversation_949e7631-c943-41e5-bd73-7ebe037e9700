'use client';

import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Box,
  TextField,
  Button,
  Typography,
  CircularProgress,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControlLabel,
  Checkbox,
  Chip,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  LinearProgress,
  Collapse,
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import UploadIcon from '@mui/icons-material/Upload';
import DownloadIcon from '@mui/icons-material/Download';
import RefreshIcon from '@mui/icons-material/Refresh';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import { getAxiosInstance } from 'config/axios';
import { API_ENDPOINTS } from 'config/api';

const LexPromptsTab = ({ setLoading, setNotification, isDarkMode }) => {
  const queryClient = useQueryClient();

  // Separate form data for client and non-client prompts
  const [promptFormData, setPromptFormData] = useState({
    name: '',
    prompt1: '',
    prompt2: '',
    prompt3: '',
    isClient: true, // Default to true for new prompts
  });

  const [editDialog, setEditDialog] = useState({
    open: false,
    prompt: null,
  });
  const [deleteDialog, setDeleteDialog] = useState({
    open: false,
    promptId: null,
  });
  const [selectedModel, setSelectedModel] = useState('azure-gpt4o');
  const [csvFile, setCsvFile] = useState(null);
  const [uploadDialog, setUploadDialog] = useState(false);
  const [promptFormCollapsed, setPromptFormCollapsed] = useState(true);
  const [promptPromptsCollapsed, setPromptPromptsCollapsed] = useState(true);
  // eslint-disable-next-line no-unused-vars
  const [internalPromptsCollapsed, setInternalPromptsCollapsed] =
    useState(true);
  const [jobsCollapsed, setJobsCollapsed] = useState(true); // default expanded

  // 1. Add state for selectedPromptId in the CSV upload dialog
  const [selectedPromptId, setSelectedPromptId] = useState('');

  // React Query for fetching prompts
  const { data: prompts = [], isLoading: promptsLoading } = useQuery({
    queryKey: ['lex-prompts-management'],
    queryFn: async () => {
      const response = await getAxiosInstance({
        cookiesKey: 'jeff-authorization',
      }).get(`${API_ENDPOINTS.LEX_PROMPTS}?active_only=false&limit=50`);

      let promptsArr = [];
      if (Array.isArray(response.data)) {
        promptsArr = response.data;
      } else if (Array.isArray(response.data?.data)) {
        promptsArr = response.data.data;
      } else if (Array.isArray(response.data?.results)) {
        promptsArr = response.data.results;
      } else {
        promptsArr = [];
      }
      return promptsArr;
    },
    staleTime: 60000, // 1 minute
    onError: (e) => {
      console.log(
        'errorrrrrrrrrr-------------------------------------->>>>>>>>>',
        e,
      );
      setNotification({
        open: true,
        message: 'Failed to fetch prompts',
        severity: 'error',
      });
    },
  });

  // Query for available models
  const { data: availableModels = [] } = useQuery({
    queryKey: ['lex-models'],
    queryFn: async () => {
      const response = await getAxiosInstance({
        cookiesKey: 'jeff-authorization',
      }).get(`${API_ENDPOINTS.LEX_PROMPTS}/models`);

      // Handle the API response structure
      if (response.data?.success && Array.isArray(response.data.data)) {
        return response.data.data;
      }
      return response.data || [];
    },
    staleTime: 300000, // 5 minutes
  });

  // Query for bulk jobs
  const {
    data: bulkJobs = [],
    isLoading: jobsLoading,
    refetch: refetchJobs,
  } = useQuery({
    queryKey: ['lex-bulk-jobs'],
    queryFn: async () => {
      const response = await getAxiosInstance({
        cookiesKey: 'jeff-authorization',
      }).get(`${API_ENDPOINTS.LEX_PROMPTS}/bulk-jobs?limit=50`);

      // Handle the API response structure
      if (response.data?.success && Array.isArray(response.data.data)) {
        return response.data.data;
      }
      return response.data || [];
    },
    staleTime: 10000, // 10 seconds
    refetchInterval: 5000, // Auto-refresh every 5 seconds
  });

  // Mutation for creating prompts
  const createPromptMutation = useMutation({
    mutationFn: async (payload) => {
      const response = await getAxiosInstance({
        cookiesKey: 'jeff-authorization',
      }).post(API_ENDPOINTS.LEX_PROMPTS, payload);
      return response.data;
    },
    onSuccess: () => {
      setNotification({
        open: true,
        message: 'Prompt chain added successfully',
        severity: 'success',
      });
      setPromptFormData({
        name: '',
        prompt1: '',
        prompt2: '',
        prompt3: '',
        isClient: true, // Reset to default
      });
      queryClient.invalidateQueries({ queryKey: ['lex-prompts-management'] });
    },
    onError: () => {
      setNotification({
        open: true,
        message: 'Failed to add prompt chain',
        severity: 'error',
      });
    },
  });

  // Mutation for updating prompts
  const updatePromptMutation = useMutation({
    mutationFn: async ({ id, payload }) => {
      const response = await getAxiosInstance({
        cookiesKey: 'jeff-authorization',
      }).put(API_ENDPOINTS.LEX_PROMPTS_ID(id), payload);
      return response.data;
    },
    onSuccess: () => {
      setNotification({
        open: true,
        message: 'Prompt updated successfully',
        severity: 'success',
      });
      setEditDialog({ open: false, prompt: null });
      queryClient.invalidateQueries({ queryKey: ['lex-prompts-management'] });
    },
    onError: () => {
      setNotification({
        open: true,
        message: 'Failed to update prompt',
        severity: 'error',
      });
    },
  });

  // Mutation for deleting prompts
  const deletePromptMutation = useMutation({
    mutationFn: async (promptId) => {
      const response = await getAxiosInstance({
        cookiesKey: 'jeff-authorization',
      }).delete(API_ENDPOINTS.LEX_PROMPTS_ID(promptId));
      return response.data;
    },
    onSuccess: () => {
      setNotification({
        open: true,
        message: 'Prompt deleted successfully',
        severity: 'success',
      });
      setDeleteDialog({ open: false, promptId: null });
      queryClient.invalidateQueries({ queryKey: ['lex-prompts-management'] });
    },
    onError: () => {
      setNotification({
        open: true,
        message: 'Failed to delete prompt',
        severity: 'error',
      });
    },
  });

  // Mutation for CSV upload
  const uploadCsvMutation = useMutation({
    mutationFn: async ({ file, model, promptId }) => {
      const formData = new FormData();
      formData.append('csvFile', file);
      formData.append('model', model);
      formData.append('promptId', promptId); // Append promptId

      const response = await getAxiosInstance({
        cookiesKey: 'jeff-authorization',
      }).post(`${API_ENDPOINTS.LEX_PROMPTS}/upload-csv`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    },
    onSuccess: () => {
      setNotification({
        open: true,
        message: 'CSV uploaded successfully. Processing started.',
        severity: 'success',
      });
      setUploadDialog(false);
      setCsvFile(null);
      setSelectedPromptId(''); // Clear selected prompt after upload
      refetchJobs();
    },
    onError: () => {
      setNotification({
        open: true,
        message: 'Failed to upload CSV file',
        severity: 'error',
      });
    },
  });

  // Update loading state for parent component
  const isLoading =
    promptsLoading ||
    createPromptMutation.isPending ||
    updatePromptMutation.isPending ||
    deletePromptMutation.isPending ||
    jobsLoading ||
    uploadCsvMutation.isPending;

  useEffect(() => {
    setLoading(isLoading);
  }, [isLoading, setLoading]);

  // Auto-populate forms when prompts data is loaded
  useEffect(() => {
    if (prompts.length > 0) {
      const clientPrompt = prompts.find((p) => p.isClient === true);
      const nonClientPrompt = prompts.find((p) => p.isClient === false);

      if (clientPrompt) {
        setPromptFormData({
          name: clientPrompt.name || '',
          prompt1: clientPrompt.prompt1 || '',
          prompt2: clientPrompt.prompt2 || '',
          prompt3: clientPrompt.prompt3 || '',
          isClient: clientPrompt.isClient || true, // Ensure isClient is set
        });
      }

      if (nonClientPrompt) {
        setPromptFormData({
          name: nonClientPrompt.name || '',
          prompt1: nonClientPrompt.prompt1 || '',
          prompt2: nonClientPrompt.prompt2 || '',
          prompt3: nonClientPrompt.prompt3 || '',
          isClient: nonClientPrompt.isClient || false, // Ensure isClient is set
        });
      }
    }
  }, [prompts]);

  // Input change handlers for both forms
  const handlePromptInputChange = (field) => (event) => {
    setPromptFormData({
      ...promptFormData,
      [field]: event.target.value,
    });
  };

  // Submit handlers for both forms
  const handlePromptSubmit = async (e) => {
    e.preventDefault();

    if (!promptFormData.name.trim()) {
      setNotification({
        open: true,
        message: 'Please enter a prompt chain name',
        severity: 'warning',
      });
      return;
    }

    if (
      !promptFormData.prompt1.trim() ||
      !promptFormData.prompt2.trim() ||
      !promptFormData.prompt3.trim()
    ) {
      setNotification({
        open: true,
        message: 'All 3 prompts are required for a complete chain',
        severity: 'warning',
      });
      return;
    }

    const payload = {
      name: promptFormData.name,
      description: promptFormData.description || '',
      prompt1: promptFormData.prompt1,
      prompt2: promptFormData.prompt2,
      prompt3: promptFormData.prompt3,
      isActive: true,
      isPrimary: false,
      isClient: promptFormData.isClient, // Use isClient boolean
    };

    createPromptMutation.mutate(payload);
  };

  const handleEdit = (prompt) => {
    setEditDialog({
      open: true,
      prompt: { ...prompt },
    });
  };

  const handleEditSubmit = async () => {
    if (
      !editDialog.prompt?.prompt1?.trim() ||
      !editDialog.prompt?.prompt2?.trim() ||
      !editDialog.prompt?.prompt3?.trim()
    ) {
      setNotification({
        open: true,
        message: 'All 3 prompts are required for a complete chain',
        severity: 'warning',
      });
      return;
    }

    const payload = {
      name: editDialog.prompt.name || 'Prompt Chain',
      description: editDialog.prompt.description || '',
      prompt1: editDialog.prompt.prompt1,
      prompt2: editDialog.prompt.prompt2,
      prompt3: editDialog.prompt.prompt3,
      isActive:
        editDialog.prompt.isActive !== undefined
          ? editDialog.prompt.isActive
          : true,
      isPrimary:
        editDialog.prompt.isPrimary !== undefined
          ? editDialog.prompt.isPrimary
          : false,
      isClient: editDialog.prompt.isClient || false, // Use isClient boolean
    };

    updatePromptMutation.mutate({
      id: editDialog.prompt.id,
      payload,
    });
  };

  const handleDelete = (promptId) => {
    setDeleteDialog({
      open: true,
      promptId,
    });
  };

  const handleDeleteConfirm = async () => {
    deletePromptMutation.mutate(deleteDialog.promptId);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Separate prompts by type
  const clientPrompts = prompts.filter((p) => p.isClient);
  // eslint-disable-next-line no-unused-vars
  const internalPrompts = prompts.filter((p) => !p.isClient);

  // Handle CSV file selection
  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file && file.type === 'text/csv') {
      setCsvFile(file);
    } else {
      setNotification({
        open: true,
        message: 'Please select a valid CSV file',
        severity: 'warning',
      });
    }
  };

  // Handle CSV upload
  const handleCsvUpload = () => {
    if (!csvFile) {
      setNotification({
        open: true,
        message: 'Please select a CSV file first',
        severity: 'warning',
      });
      return;
    }
    if (!selectedPromptId) {
      setNotification({
        open: true,
        message: 'Please select a prompt chain',
        severity: 'warning',
      });
      return;
    }
    uploadCsvMutation.mutate({
      file: csvFile,
      model: selectedModel,
      promptId: selectedPromptId,
    });
  };

  // Handle job download
  const handleJobDownload = async (jobId, jobName) => {
    try {
      const response = await getAxiosInstance({
        cookiesKey: 'jeff-authorization',
      }).get(`${API_ENDPOINTS.LEX_PROMPTS}/bulk-jobs/${jobId}/download`, {
        responseType: 'blob',
      });

      const blob = new Blob([response.data], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${jobName || `job_${jobId}`}_results.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      setNotification({
        open: true,
        message: 'CSV file downloaded successfully',
        severity: 'success',
      });
    } catch (error) {
      setNotification({
        open: true,
        message: 'Failed to download CSV file',
        severity: 'error',
      });
    }
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'COMPLETED':
        return 'success';
      case 'IN_PROGRESS':
        return 'primary';
      case 'FAILED':
        return 'error';
      case 'PENDING':
        return 'warning';
      default:
        return 'default';
    }
  };

  // Format progress percentage
  const formatProgress = (processed, total) => {
    if (!total || total === 0) return 0;
    return Math.round((processed / total) * 100);
  };

  // Render form component
  const renderPromptForm = (
    formData,
    handleInputChange,
    handleSubmit,
    existingPrompt,
  ) => {
    const isCollapsed = promptFormCollapsed;
    const setIsCollapsed = setPromptFormCollapsed;

    return (
      <Card
        sx={{
          mb: 3,
          backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
          border: `2px solid ${isDarkMode ? '#3182CE' : '#1976d2'}`,
        }}
      >
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 600,
                color: isDarkMode ? '#90CAF9' : '#1976d2',
                flex: 1,
              }}
            >
              Prompt Chain
            </Typography>
            <Chip
              label={formData.isClient ? 'Client' : 'Internal'}
              color={formData.isClient ? 'primary' : 'default'}
              size="small"
            />
            {existingPrompt && (
              <Chip
                label="Auto-populated"
                color="success"
                size="small"
                sx={{ fontSize: '0.7rem' }}
              />
            )}
            <IconButton
              onClick={() => setIsCollapsed(!isCollapsed)}
              sx={{
                color: isDarkMode ? '#90CAF9' : '#1976d2',
                '&:hover': {
                  backgroundColor: isDarkMode
                    ? 'rgba(144, 202, 249, 0.1)'
                    : 'rgba(25, 118, 210, 0.1)',
                },
              }}
            >
              {isCollapsed ? <ExpandMoreIcon /> : <ExpandLessIcon />}
            </IconButton>
          </Box>

          <Collapse in={!isCollapsed}>
            <Box component="form" onSubmit={handleSubmit}>
              <Box
                sx={{ display: 'flex', flexDirection: 'column', gap: 2, mb: 3 }}
              >
                <Box
                  sx={{
                    mb: 1,
                    p: 1.5,
                    borderRadius: 1,
                    backgroundColor: isDarkMode
                      ? 'rgba(144,202,249,0.08)'
                      : 'rgba(25,118,210,0.06)',
                    border: `1px solid ${
                      isDarkMode ? 'rgba(144,202,249,0.2)' : 'rgba(25,118,210,0.15)'
                    }`,
                  }}
                >
                  <Typography
                    variant="body2"
                    sx={{
                      color: isDarkMode ? '#90CAF9' : '#1976d2',
                      fontWeight: 500,
                    }}
                  >
                    <strong>Required:</strong> All 3 prompts must be filled for
                    a complete chain. Use{' '}
                    <b>{'{{}}'}</b> for dynamic variables.
                  </Typography>
                </Box>

                <TextField
                  label="Prompt Chain Name *"
                  variant="outlined"
                  value={formData.name}
                  onChange={handleInputChange('name')}
                  multiline
                  rows={4}
                  required
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      backgroundColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.05)'
                        : 'inherit',
                      color: isDarkMode ? '#e2e8f0' : '#4a5568',
                      '& fieldset': {
                        borderColor: isDarkMode
                          ? 'rgba(255, 255, 255, 0.23)'
                          : 'rgba(0, 0, 0, 0.23)',
                      },
                      '&:hover fieldset': {
                        borderColor: isDarkMode
                          ? 'rgba(255, 255, 255, 0.4)'
                          : 'rgba(0, 0, 0, 0.4)',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                      },
                      '& textarea': {
                        resize: 'vertical',
                      },
                    },
                    '& .MuiInputLabel-root': {
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.7)'
                        : 'rgba(0, 0, 0, 0.6)',
                      '&.Mui-focused': {
                        color: isDarkMode ? '#90CAF9' : '#1976d2',
                      },
                    },
                  }}
                />

                <TextField
                  label="Prompt 1 (Analysis) *"
                  variant="outlined"
                  value={formData.prompt1}
                  onChange={handleInputChange('prompt1')}
                  multiline
                  rows={4}
                  required
                  placeholder="Enter the first prompt for initial analysis..."
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      backgroundColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.05)'
                        : 'inherit',
                      color: isDarkMode ? '#e2e8f0' : '#4a5568',
                      '& fieldset': {
                        borderColor: isDarkMode
                          ? 'rgba(255, 255, 255, 0.23)'
                          : 'rgba(0, 0, 0, 0.23)',
                      },
                      '&:hover fieldset': {
                        borderColor: isDarkMode
                          ? 'rgba(255, 255, 255, 0.4)'
                          : 'rgba(0, 0, 0, 0.4)',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                      },
                      '& textarea': {
                        resize: 'vertical',
                      },
                    },
                    '& .MuiInputLabel-root': {
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.7)'
                        : 'rgba(0, 0, 0, 0.6)',
                      '&.Mui-focused': {
                        color: isDarkMode ? '#90CAF9' : '#1976d2',
                      },
                    },
                  }}
                />

                <TextField
                  label="Prompt 2 (Processing) *"
                  variant="outlined"
                  value={formData.prompt2}
                  onChange={handleInputChange('prompt2')}
                  multiline
                  rows={4}
                  required
                  placeholder="Enter the second prompt for processing..."
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      backgroundColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.05)'
                        : 'inherit',
                      color: isDarkMode ? '#e2e8f0' : '#4a5568',
                      '& fieldset': {
                        borderColor: isDarkMode
                          ? 'rgba(255, 255, 255, 0.23)'
                          : 'rgba(0, 0, 0, 0.23)',
                      },
                      '&:hover fieldset': {
                        borderColor: isDarkMode
                          ? 'rgba(255, 255, 255, 0.4)'
                          : 'rgba(0, 0, 0, 0.4)',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                      },
                      '& textarea': {
                        resize: 'vertical',
                      },
                    },
                    '& .MuiInputLabel-root': {
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.7)'
                        : 'rgba(0, 0, 0, 0.6)',
                      '&.Mui-focused': {
                        color: isDarkMode ? '#90CAF9' : '#1976d2',
                      },
                    },
                  }}
                />

                <TextField
                  label="Prompt 3 (Violation Detection) *"
                  variant="outlined"
                  value={formData.prompt3}
                  onChange={handleInputChange('prompt3')}
                  multiline
                  rows={4}
                  required
                  placeholder="Enter the third prompt for violation detection..."
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      backgroundColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.05)'
                        : 'inherit',
                      color: isDarkMode ? '#e2e8f0' : '#4a5568',
                      '& fieldset': {
                        borderColor: isDarkMode
                          ? 'rgba(255, 255, 255, 0.23)'
                          : 'rgba(0, 0, 0, 0.23)',
                      },
                      '&:hover fieldset': {
                        borderColor: isDarkMode
                          ? 'rgba(255, 255, 255, 0.4)'
                          : 'rgba(0, 0, 0, 0.4)',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                      },
                      '& textarea': {
                        resize: 'vertical',
                      },
                    },
                    '& .MuiInputLabel-root': {
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.7)'
                        : 'rgba(0, 0, 0, 0.6)',
                      '&.Mui-focused': {
                        color: isDarkMode ? '#90CAF9' : '#1976d2',
                      },
                    },
                  }}
                />

                <FormControlLabel
                  control={
                    <Checkbox
                      checked={formData.isClient || false}
                      onChange={e => setPromptFormData({ ...formData, isClient: e.target.checked })}
                      sx={{
                        color: isDarkMode ? '#e2e8f0' : '#4a5568',
                        '&.Mui-checked': {
                          color: isDarkMode ? '#90CAF9' : '#1976d2',
                        },
                      }}
                    />
                  }
                  label="Client Prompt Chain"
                  sx={{ color: isDarkMode ? '#e2e8f0' : '#4a5568' }}
                />
              </Box>

              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                <Button
                  type="submit"
                  variant="contained"
                  disabled={createPromptMutation.isPending}
                  startIcon={<AddIcon />}
                  sx={{
                    backgroundColor: isDarkMode ? '#3182CE' : '#2f6ad9',
                    '&:hover': {
                      backgroundColor: isDarkMode ? '#4299E1' : '#3b7be8',
                    },
                    minWidth: '180px',
                  }}
                >
                  {createPromptMutation.isPending ? (
                    <CircularProgress size={20} />
                  ) : (
                    'Save Prompt Chain'
                  )}
                </Button>
              </Box>
            </Box>
          </Collapse>
        </CardContent>
      </Card>
    );
  };

  return (
    <Box sx={{ mb: 3 }}>
      {/* Job Management Section */}
      <Card
        sx={{
          mb: 3,
          backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        }}
      >
        <CardContent>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mb: 3,
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  color: isDarkMode ? '#90CAF9' : '#1976d2',
                }}
              >
                LEX Processing Jobs ({bulkJobs.length})
              </Typography>
              <IconButton
                onClick={() => setJobsCollapsed((prev) => !prev)}
                sx={{
                  color: isDarkMode ? '#90CAF9' : '#1976d2',
                  ml: 1,
                  '&:hover': {
                    backgroundColor: isDarkMode
                      ? 'rgba(144, 202, 249, 0.1)'
                      : 'rgba(25, 118, 210, 0.1)',
                  },
                }}
                size="small"
              >
                {jobsCollapsed ? <ExpandMoreIcon /> : <ExpandLessIcon />}
              </IconButton>
            </Box>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Box sx={{ minWidth: '200px' }}>
                <FormControl size="small">
                  <InputLabel
                    sx={{ color: isDarkMode ? '#e2e8f0' : '#4a5568' }}
                  >
                    Model
                  </InputLabel>
                  <Select
                    value={selectedModel}
                    onChange={(e) => setSelectedModel(e.target.value)}
                    sx={{
                      color: isDarkMode ? '#e2e8f0' : '#4a5568',
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: isDarkMode
                          ? 'rgba(255, 255, 255, 0.23)'
                          : 'rgba(0, 0, 0, 0.23)',
                      },
                    }}
                  >
                    {availableModels.map((model) => (
                      <MenuItem
                        key={model.id || model}
                        value={model.id || model}
                      >
                        {model.name || model}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>

              <Button
                variant="outlined"
                startIcon={<UploadIcon />}
                onClick={() => setUploadDialog(true)}
                sx={{
                  color: isDarkMode ? '#90CAF9' : '#1976d2',
                  borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                }}
              >
                Upload CSV
              </Button>
              <IconButton
                onClick={async () => {
                  await refetchJobs();
                }}
                sx={{
                  color: isDarkMode ? '#90CAF9' : '#1976d2',
                }}
              >
                <RefreshIcon />
              </IconButton>
            </Box>
          </Box>

          <Collapse in={!jobsCollapsed}>
            {jobsLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : bulkJobs.length > 0 ? (
              <TableContainer
                component={Paper}
                sx={{ backgroundColor: 'transparent' }}
              >
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell
                        sx={{
                          backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          fontWeight: 600,
                        }}
                      >
                        Job ID
                      </TableCell>
                      <TableCell
                        sx={{
                          backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          fontWeight: 600,
                        }}
                      >
                        Status
                      </TableCell>
                      <TableCell
                        sx={{
                          backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          fontWeight: 600,
                        }}
                      >
                        Progress
                      </TableCell>
                      <TableCell
                        sx={{
                          backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          fontWeight: 600,
                        }}
                      >
                        Model
                      </TableCell>
                      <TableCell
                        sx={{
                          backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          fontWeight: 600,
                        }}
                      >
                        Created
                      </TableCell>
                      <TableCell
                        sx={{
                          backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          fontWeight: 600,
                        }}
                      >
                        Actions
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {bulkJobs.map((job) => (
                      <TableRow key={job.id}>
                        <TableCell
                          sx={{ color: isDarkMode ? '#fff' : 'inherit' }}
                        >
                          <Typography
                            variant="subtitle2"
                            sx={{ fontWeight: 600 }}
                          >
                            #{job.id}
                          </Typography>
                          {job.fileName && (
                            <Typography
                              variant="caption"
                              sx={{ color: isDarkMode ? '#9CA3AF' : '#6B7280' }}
                            >
                              {job.fileName}
                            </Typography>
                          )}
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={job.status}
                            color={getStatusColor(job.status)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell
                          sx={{ color: isDarkMode ? '#fff' : 'inherit' }}
                        >
                          <Box sx={{ width: '100%' }}>
                            <Typography variant="body2" sx={{ mb: 1 }}>
                              {job.summary?.successfulReviews || 0} /{' '}
                              {job.summary?.totalRows || 0}
                            </Typography>
                            <LinearProgress
                              variant="determinate"
                              value={formatProgress(
                                job.summary?.successfulReviews,
                                job.summary?.totalRows,
                              )}
                              sx={{
                                height: 6,
                                borderRadius: 3,
                                backgroundColor: isDarkMode
                                  ? '#4a5568'
                                  : '#e2e8f0',
                                '& .MuiLinearProgress-bar': {
                                  backgroundColor:
                                    job.status === 'COMPLETED'
                                      ? '#48bb78'
                                      : '#3182ce',
                                },
                              }}
                            />
                          </Box>
                        </TableCell>
                        <TableCell
                          sx={{ color: isDarkMode ? '#fff' : 'inherit' }}
                        >
                          <Typography variant="body2">
                            {job.model || 'azure-gpt4o'}
                          </Typography>
                        </TableCell>
                        <TableCell
                          sx={{ color: isDarkMode ? '#fff' : 'inherit' }}
                        >
                          {formatDate(job.createdAt)}
                        </TableCell>
                        <TableCell>
                          {job.status === 'COMPLETED' && job.canDownload && (
                            <IconButton
                              onClick={() =>
                                handleJobDownload(
                                  job.id,
                                  job.summary?.fileName || `job_${job.id}`,
                                )
                              }
                              size="small"
                              sx={{
                                color: isDarkMode ? '#48bb78' : '#38a169',
                                '&:hover': {
                                  backgroundColor: isDarkMode
                                    ? 'rgba(72, 187, 120, 0.1)'
                                    : 'rgba(56, 161, 105, 0.1)',
                                },
                              }}
                            >
                              <DownloadIcon />
                            </IconButton>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Typography
                variant="body2"
                sx={{
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  textAlign: 'center',
                  p: 3,
                }}
              >
                No processing jobs found. Upload a CSV to start processing
                reviews.
              </Typography>
            )}
          </Collapse>
        </CardContent>
      </Card>

      {/* CSV Upload Dialog */}
      <Dialog
        open={uploadDialog}
        onClose={() => setUploadDialog(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          },
        }}
      >
        <DialogTitle sx={{ color: isDarkMode ? '#fff' : 'inherit' }}>
          Upload CSV for Bulk Processing
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, mt: 2 }}>
            <Alert severity="info" sx={{ mb: 2 }}>
              CSV should contain columns: reviewID, reviewContent, isClient. The
              system will automatically process reviews with the appropriate
              prompt chains.
            </Alert>

            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel sx={{ color: isDarkMode ? '#e2e8f0' : '#4a5568' }}>
                AI Model
              </InputLabel>
              <Select
                value={selectedModel}
                onChange={e => setSelectedModel(e.target.value)}
                sx={{
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.23)' : 'rgba(0, 0, 0, 0.23)',
                  },
                }}
                label="AI Model"
              >
                {availableModels.map((model) => (
                  <MenuItem key={model.id || model} value={model.id || model}>
                    {model.name || model}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth sx={{ mt: 2 }}>
              <InputLabel sx={{ color: isDarkMode ? '#e2e8f0' : '#4a5568' }}>
                Select Prompt Chain
              </InputLabel>
              <Select
                value={selectedPromptId}
                onChange={e => setSelectedPromptId(e.target.value)}
                sx={{
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.23)' : 'rgba(0, 0, 0, 0.23)',
                  },
                }}
                label="Select Prompt Chain"
              >
                {prompts.map((prompt) => (
                  <MenuItem key={prompt.id} value={prompt.id}>
                    {prompt.name} ({prompt.isClient ? 'Client' : 'Internal'})
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {selectedPromptId && (() => {
              const selectedPrompt = prompts.find(p => p.id === selectedPromptId);
              if (!selectedPrompt) return null;
              return (
                <Box sx={{ mt: 2, p: 2, border: '1px solid', borderColor: isDarkMode ? '#2d3748' : '#e2e8f0', borderRadius: 2, background: isDarkMode ? '#23293a' : '#f9fafb' }}>
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>Prompt Chain Preview</Typography>
                  <Typography variant="body2" sx={{ mb: 1 }}><b>Prompt 1:</b> {selectedPrompt.prompt1}</Typography>
                  <Typography variant="body2" sx={{ mb: 1 }}><b>Prompt 2:</b> {selectedPrompt.prompt2}</Typography>
                  <Typography variant="body2"><b>Prompt 3:</b> {selectedPrompt.prompt3}</Typography>
                </Box>
              );
            })()}

            <Box>
              <input
                type="file"
                accept=".csv"
                onChange={handleFileSelect}
                style={{ display: 'none' }}
                id="csv-file-input"
              />
              <label htmlFor="csv-file-input">
                <Button
                  variant="outlined"
                  component="span"
                  startIcon={<UploadIcon />}
                  fullWidth
                  sx={{
                    color: isDarkMode ? '#90CAF9' : '#1976d2',
                    borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                    py: 2,
                  }}
                >
                  {csvFile ? csvFile.name : 'Select CSV File'}
                </Button>
              </label>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setUploadDialog(false)}
            sx={{ color: isDarkMode ? '#e2e8f0' : '#4a5568' }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleCsvUpload}
            variant="contained"
            disabled={!csvFile || uploadCsvMutation.isPending}
            sx={{
              backgroundColor: isDarkMode ? '#3182CE' : '#2f6ad9',
              '&:hover': {
                backgroundColor: isDarkMode ? '#4299E1' : '#3b7be8',
              },
            }}
          >
            {uploadCsvMutation.isPending ? (
              <CircularProgress size={20} />
            ) : (
              'Upload & Process'
            )}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Prompt Chain Form */}
      {renderPromptForm(
        promptFormData,
        handlePromptInputChange,
        handlePromptSubmit,
        clientPrompts.length > 0 ? clientPrompts[0] : null,
      )}

      {/* Prompt Chains Section */}
      <Card
        sx={{
          mb: 3,
          backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        }}
      >
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 600,
                color: isDarkMode ? '#90CAF9' : '#1976d2',
                flex: 1,
              }}
            >
              Prompt Chains ({prompts.length})
            </Typography>
            <Chip label="All" color="primary" size="small" />
            <IconButton
              onClick={() => setPromptPromptsCollapsed(!promptPromptsCollapsed)}
              sx={{
                color: isDarkMode ? '#90CAF9' : '#1976d2',
                '&:hover': {
                  backgroundColor: isDarkMode
                    ? 'rgba(144, 202, 249, 0.1)'
                    : 'rgba(25, 118, 210, 0.1)',
                },
              }}
            >
              {promptPromptsCollapsed ? <ExpandMoreIcon /> : <ExpandLessIcon />}
            </IconButton>
          </Box>

          <Collapse in={!promptPromptsCollapsed}>
            {prompts.length > 0 ? (
              <TableContainer
                component={Paper}
                sx={{ backgroundColor: 'transparent' }}
              >
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell
                        sx={{
                          backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          fontWeight: 600,
                        }}
                      >
                        Name
                      </TableCell>
                      <TableCell
                        sx={{
                          backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          fontWeight: 600,
                        }}
                      >
                        Prompt 1
                      </TableCell>
                      <TableCell
                        sx={{
                          backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          fontWeight: 600,
                        }}
                      >
                        Prompt 2
                      </TableCell>
                      <TableCell
                        sx={{
                          backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          fontWeight: 600,
                        }}
                      >
                        Prompt 3
                      </TableCell>
                      <TableCell
                        sx={{
                          backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          fontWeight: 600,
                        }}
                      >
                        Type
                      </TableCell>
                      <TableCell
                        sx={{
                          backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          fontWeight: 600,
                        }}
                      >
                        Created
                      </TableCell>
                      <TableCell
                        sx={{
                          backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          fontWeight: 600,
                        }}
                      >
                        Actions
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {prompts.map((prompt) => (
                      <TableRow key={prompt.id}>
                        <TableCell
                          sx={{ color: isDarkMode ? '#fff' : 'inherit' }}
                        >
                          <Typography
                            variant="subtitle2"
                            sx={{ fontWeight: 600 }}
                          >
                            {prompt.name}
                          </Typography>
                        </TableCell>
                        <TableCell
                          sx={{ color: isDarkMode ? '#fff' : 'inherit' }}
                        >
                          <Typography
                            variant="body2"
                            sx={{ maxWidth: 200, wordBreak: 'break-word' }}
                          >
                            {prompt.prompt1?.substring(0, 50)}...
                          </Typography>
                        </TableCell>
                        <TableCell
                          sx={{ color: isDarkMode ? '#fff' : 'inherit' }}
                        >
                          <Typography
                            variant="body2"
                            sx={{ maxWidth: 200, wordBreak: 'break-word' }}
                          >
                            {prompt.prompt2?.substring(0, 50)}...
                          </Typography>
                        </TableCell>
                        <TableCell
                          sx={{ color: isDarkMode ? '#fff' : 'inherit' }}
                        >
                          <Typography
                            variant="body2"
                            sx={{ maxWidth: 200, wordBreak: 'break-word' }}
                          >
                            {prompt.prompt3?.substring(0, 50)}...
                          </Typography>
                        </TableCell>
                        <TableCell
                          sx={{ color: isDarkMode ? '#fff' : 'inherit' }}
                        >
                          <Typography variant="body2">
                            {prompt.isClient ? 'Client' : 'Internal'}
                          </Typography>
                        </TableCell>
                        <TableCell
                          sx={{ color: isDarkMode ? '#fff' : 'inherit' }}
                        >
                          {formatDate(prompt.createdAt)}
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <IconButton
                              onClick={() => handleEdit(prompt)}
                              size="small"
                              sx={{
                                color: isDarkMode ? '#90CAF9' : '#1976d2',
                                '&:hover': {
                                  backgroundColor: isDarkMode
                                    ? 'rgba(144, 202, 249, 0.1)'
                                    : 'rgba(25, 118, 210, 0.1)',
                                },
                              }}
                            >
                              <EditIcon />
                            </IconButton>
                            <IconButton
                              onClick={() => handleDelete(prompt.id)}
                              size="small"
                              sx={{
                                color: isDarkMode ? '#f56565' : '#e53e3e',
                                '&:hover': {
                                  backgroundColor: isDarkMode
                                    ? 'rgba(245, 101, 101, 0.1)'
                                    : 'rgba(229, 62, 62, 0.1)',
                                },
                              }}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Typography
                variant="body2"
                sx={{
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  textAlign: 'center',
                  p: 3,
                }}
              >
                No prompt chains created yet.
              </Typography>
            )}
          </Collapse>
        </CardContent>
      </Card>

      {/* Edit Dialog - ensure all 3 prompts are required */}
      <Dialog
        open={editDialog.open}
        onClose={() => setEditDialog({ open: false, prompt: null })}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          },
        }}
      >
        <DialogTitle sx={{ color: isDarkMode ? '#fff' : 'inherit' }}>
          Edit LEX Prompt Chain
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
            <Box
              sx={{
                mb: 1,
                p: 1.5,
                borderRadius: 1,
                backgroundColor: isDarkMode
                  ? 'rgba(144,202,249,0.08)'
                  : 'rgba(25,118,210,0.06)',
                border: `1px solid ${
                  isDarkMode ? 'rgba(144,202,249,0.2)' : 'rgba(25,118,210,0.15)'
                }`,
              }}
            >
              <Typography
                variant="body2"
                sx={{
                  color: isDarkMode ? '#90CAF9' : '#1976d2',
                  fontWeight: 500,
                }}
              >
                <strong>Required:</strong> All 3 prompts must be filled for a
                complete chain. Use <b>{'{{}}'}</b> for dynamic variables.
              </Typography>
            </Box>
            <TextField
              label="Prompt Chain Name *"
              variant="outlined"
              value={editDialog.prompt?.name || ''}
              onChange={(e) =>
                setEditDialog({
                  ...editDialog,
                  prompt: { ...editDialog.prompt, name: e.target.value },
                })
              }
              required
              sx={{
                '& .MuiOutlinedInput-root': {
                  backgroundColor: isDarkMode
                    ? 'rgba(255, 255, 255, 0.05)'
                    : 'inherit',
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  '& fieldset': {
                    borderColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.23)'
                      : 'rgba(0, 0, 0, 0.23)',
                  },
                  '&:hover fieldset': {
                    borderColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.4)'
                      : 'rgba(0, 0, 0, 0.4)',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                },
                '& .MuiInputLabel-root': {
                  color: isDarkMode
                    ? 'rgba(255, 255, 255, 0.7)'
                    : 'rgba(0, 0, 0, 0.6)',
                  '&.Mui-focused': {
                    color: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                },
              }}
            />
            <TextField
              label="Prompt 1 (Analysis) *"
              variant="outlined"
              value={editDialog.prompt?.prompt1 || ''}
              onChange={(e) =>
                setEditDialog({
                  ...editDialog,
                  prompt: { ...editDialog.prompt, prompt1: e.target.value },
                })
              }
              multiline
              rows={3}
              required
              sx={{
                '& .MuiOutlinedInput-root': {
                  backgroundColor: isDarkMode
                    ? 'rgba(255, 255, 255, 0.05)'
                    : 'inherit',
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  '& fieldset': {
                    borderColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.23)'
                      : 'rgba(0, 0, 0, 0.23)',
                  },
                  '&:hover fieldset': {
                    borderColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.4)'
                      : 'rgba(0, 0, 0, 0.4)',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                },
                '& .MuiInputLabel-root': {
                  color: isDarkMode
                    ? 'rgba(255, 255, 255, 0.7)'
                    : 'rgba(0, 0, 0, 0.6)',
                  '&.Mui-focused': {
                    color: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                },
                '& textarea': {
                  resize: 'vertical',
                },
              }}
            />

            <TextField
              label="Prompt 2 (Processing) *"
              variant="outlined"
              value={editDialog.prompt?.prompt2 || ''}
              onChange={(e) =>
                setEditDialog({
                  ...editDialog,
                  prompt: { ...editDialog.prompt, prompt2: e.target.value },
                })
              }
              multiline
              rows={3}
              required
              sx={{
                '& .MuiOutlinedInput-root': {
                  backgroundColor: isDarkMode
                    ? 'rgba(255, 255, 255, 0.05)'
                    : 'inherit',
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  '& fieldset': {
                    borderColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.23)'
                      : 'rgba(0, 0, 0, 0.23)',
                  },
                  '&:hover fieldset': {
                    borderColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.4)'
                      : 'rgba(0, 0, 0, 0.4)',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                },
                '& .MuiInputLabel-root': {
                  color: isDarkMode
                    ? 'rgba(255, 255, 255, 0.7)'
                    : 'rgba(0, 0, 0, 0.6)',
                  '&.Mui-focused': {
                    color: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                },
                '& textarea': {
                  resize: 'vertical',
                },
              }}
            />

            <TextField
              label="Prompt 3 (Violation Detection) *"
              variant="outlined"
              value={editDialog.prompt?.prompt3 || ''}
              onChange={(e) =>
                setEditDialog({
                  ...editDialog,
                  prompt: { ...editDialog.prompt, prompt3: e.target.value },
                })
              }
              multiline
              rows={3}
              required
              sx={{
                '& .MuiOutlinedInput-root': {
                  backgroundColor: isDarkMode
                    ? 'rgba(255, 255, 255, 0.05)'
                    : 'inherit',
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  '& fieldset': {
                    borderColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.23)'
                      : 'rgba(0, 0, 0, 0.23)',
                  },
                  '&:hover fieldset': {
                    borderColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.4)'
                      : 'rgba(0, 0, 0, 0.4)',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                },
                '& .MuiInputLabel-root': {
                  color: isDarkMode
                    ? 'rgba(255, 255, 255, 0.7)'
                    : 'rgba(0, 0, 0, 0.6)',
                  '&.Mui-focused': {
                    color: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                },
                '& textarea': {
                  resize: 'vertical',
                },
              }}
            />

            <FormControlLabel
              control={
                <Checkbox
                  checked={editDialog.prompt?.isClient || false}
                  onChange={(e) =>
                    setEditDialog({
                      ...editDialog,
                      prompt: {
                        ...editDialog.prompt,
                        isClient: e.target.checked,
                      },
                    })
                  }
                  sx={{
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    '&.Mui-checked': {
                      color: isDarkMode ? '#90CAF9' : '#1976d2',
                    },
                  }}
                />
              }
              label="Client Prompt Chain"
              sx={{
                color: isDarkMode ? '#e2e8f0' : '#4a5568',
              }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setEditDialog({ open: false, prompt: null })}
            sx={{ color: isDarkMode ? '#e2e8f0' : '#4a5568' }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleEditSubmit}
            variant="contained"
            disabled={updatePromptMutation.isPending}
            sx={{
              backgroundColor: isDarkMode ? '#3182CE' : '#2f6ad9',
              '&:hover': {
                backgroundColor: isDarkMode ? '#4299E1' : '#3b7be8',
              },
            }}
          >
            {updatePromptMutation.isPending ? (
              <CircularProgress size={20} />
            ) : (
              'Update Chain'
            )}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialog.open}
        onClose={() => setDeleteDialog({ open: false, promptId: null })}
        PaperProps={{
          sx: {
            backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          },
        }}
      >
        <DialogTitle sx={{ color: isDarkMode ? '#fff' : 'inherit' }}>
          Confirm Delete
        </DialogTitle>
        <DialogContent>
          <Typography sx={{ color: isDarkMode ? '#e2e8f0' : '#4a5568' }}>
            Are you sure you want to delete this prompt chain? This action
            cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setDeleteDialog({ open: false, promptId: null })}
            sx={{ color: isDarkMode ? '#e2e8f0' : '#4a5568' }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleDeleteConfirm}
            variant="contained"
            disabled={deletePromptMutation.isPending}
            sx={{
              backgroundColor: isDarkMode ? '#f56565' : '#e53e3e',
              '&:hover': {
                backgroundColor: isDarkMode ? '#fc8181' : '#f56565',
              },
            }}
          >
            {deletePromptMutation.isPending ? (
              <CircularProgress size={20} />
            ) : (
              'Delete Chain'
            )}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default LexPromptsTab;
