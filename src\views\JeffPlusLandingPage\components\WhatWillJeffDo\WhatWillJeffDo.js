import React from 'react';
import { alpha } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Typography from '@mui/material/Typography';
import Spacer from '../../../../components/Equal/Spacer';
import { colors } from '@mui/material';

import SearchIcon from '@mui/icons-material/Search';
import FilterAltIcon from '@mui/icons-material/FilterAlt';
import EmojiPeopleIcon from '@mui/icons-material/EmojiPeople';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import SendIcon from '@mui/icons-material/Send';

const JEFF_FEATURES = [
  {
    icon: SearchIcon,
    title: 'Finding prospects',
    description: 'Finds a list of 5000 Amazon sellers, D2C brands and their valid email addresses.'
  },
  {
    icon: FilterAltIcon,
    title: 'Qualification & Segmentation',
    description: 'Qualifies and segments them based on 20+ signals. Powered by more than 200k+ sellers.'
  },
  {
    icon: EmojiPeopleIcon,
    title: 'Personalised Outreach',
    description: 'Write ultra-personalized emails sequences based on 20+ signals, in your brand voice.'
  },
  {
    icon: AutoAwesomeIcon,
    title: 'Automated Reachout',
    description: 'Reaches out to prospects and follows up consistently to gauge their attention.'
  },
  {
    icon: CalendarMonthIcon,
    title: 'Closing & Scheduling',
    description: 'Replies instantly, books meetings, adds them to your calendar, boosting conversions.'
  },
  {
    icon: SendIcon,
    title: 'Deliverability Management',
    description: 'Manages your email infrastructure so that you never land up in spam.'
  }
];

const QualificationSignals = () => {
  return (
    <Box>
      <Typography variant={'h4'} sx={{ fontWeight: 700, textAlign: 'center' }} gutterBottom>
        { 'What ' }
        <Typography
          color={'primary'}
          component={'span'}
          variant={'inherit'}
          sx={{
            fontWeight: 700,
            display: 'inline',
            background: `linear-gradient(180deg, transparent 82%, ${alpha(
              colors.purple[200],
              0.3,
            )} 0%)`,
          }}
        >
          { ' will Jeff do?' }
        </Typography>
      </Typography>
      <Spacer y={2} />
      <Grid container spacing={2}>
        {JEFF_FEATURES.map((feature, key) => {
          return (
            <Grid key={key} item md={4}>
              <Box component={Card} variant='outlined'>
                <CardContent>
                  <feature.icon sx={{ width: 40, height: 40 }} />
                  <Typography variant="h5" sx={{ fontWeight: 700 }}>{feature.title}</Typography>
                  <Spacer y={1} />
                  <Typography>{feature.description}</Typography>
                </CardContent>
              </Box>
            </Grid>
          );
        })}
      </Grid>
    </Box>
  );
};

export default QualificationSignals;
