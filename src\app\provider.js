'use client';

import React, { useState } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { AdminThemeProvider } from '../views/Jeff/AdminThemeContext';
import { CSPostHogProvider } from './PostHog';

const Providers = ({ children }) => {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            retry: false,
          },
        },
      }),
  );

  //useEffect(() => {
  //  let title = '';
  //  if (window.location.pathname === '/') {
  //    title = 'Agents for Small & Medium Businesses | Equal Collective';
  //  }

  //  //else if (window.location.pathname.includes('/ecom-sellers')) {
  //  //  title = 'Custom AI Solutions for eCommerce Businesses | Equal Collective';
  //  //}

  //  if (title) document.title = title;
  //}, []);

  return (
    <QueryClientProvider client={queryClient}>
      <AdminThemeProvider>
        <CSPostHogProvider>
          {children}
          <ReactQueryDevtools initialIsOpen={false} />
        </CSPostHogProvider>
      </AdminThemeProvider>
    </QueryClientProvider>
  );
};

export default Providers;
