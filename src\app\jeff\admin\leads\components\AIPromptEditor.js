'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  TextField,
  Button,
  Chip,
  Grid,
  Paper,
  Divider,
  IconButton,
  Tooltip,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import RestoreIcon from '@mui/icons-material/Restore';
import InfoIcon from '@mui/icons-material/Info';
import { useAdminTheme } from 'views/Jeff/AdminThemeContext';

const AIPromptEditor = ({
  prompt,
  onPromptChange,
  selectedModel,
  onLoadDefaultPrompt,
  disabled = false,
  csvRowCount = 0,
}) => {
  const { mode: adminThemeMode } = useAdminTheme() || { mode: 'light' };
  const isDarkMode = adminThemeMode === 'dark';
  const [expanded, setExpanded] = useState(false);

  // Available variables that can be used in prompts
  const availableVariables = [
    { name: '{{sellerName}}', description: 'The seller/company name from CSV' },
    { name: '{{businessName}}', description: 'Business name if different from seller name' },
    { name: '{{industry}}', description: 'Industry category' },
    { name: '{{country}}', description: 'Country code (e.g., US, UK)' },
    { name: '{{address}}', description: 'Business address' },
    { name: '{{email}}', description: 'Contact email if available' },
  ];

  // Calculate character count and estimated tokens
  const characterCount = prompt ? prompt.length : 0;
  const estimatedTokens = Math.ceil(characterCount / 4); // Rough estimation: 4 chars per token

  // Calculate cost estimation
  const getCostEstimation = () => {
    if (!selectedModel || !csvRowCount) return null;
    
    const modelCosts = {
      openai: 0.03,
      claude: 0.025,
      gemini: 0.02,
    };
    
    const costPer1k = modelCosts[selectedModel] || 0.03;
    const tokensPerRow = estimatedTokens;
    const totalTokens = tokensPerRow * csvRowCount;
    const totalCost = (totalTokens / 1000) * costPer1k;
    
    return {
      tokensPerRow,
      totalTokens,
      totalCost,
    };
  };

  const costEstimation = getCostEstimation();

  const handleVariableClick = (variable) => {
    if (disabled) return;
    
    const textarea = document.getElementById('prompt-textarea');
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const newPrompt = prompt.substring(0, start) + variable + prompt.substring(end);
      onPromptChange(newPrompt);
      
      // Set cursor position after the inserted variable
      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(start + variable.length, start + variable.length);
      }, 0);
    }
  };

  return (
    <Box sx={{ mb: 2 }}>
      <Accordion 
        expanded={expanded} 
        onChange={(e, isExpanded) => setExpanded(isExpanded)}
        sx={{
          backgroundColor: isDarkMode ? '#2D3748' : '#FFFFFF',
          '&:before': {
            display: 'none',
          },
          boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
          borderRadius: '8px !important',
        }}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon sx={{ color: isDarkMode ? '#E2E8F0' : '#4A5568' }} />}
          sx={{
            backgroundColor: isDarkMode ? '#1A202C' : '#F7FAFC',
            borderRadius: expanded ? '8px 8px 0 0' : '8px',
            '& .MuiAccordionSummary-content': {
              alignItems: 'center',
            },
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: 600,
                color: isDarkMode ? '#E2E8F0' : '#4A5568',
                mr: 2,
              }}
            >
              AI Prompt Configuration
            </Typography>
            {!expanded && prompt && (
              <Chip
                label={`${characterCount} chars`}
                size="small"
                sx={{
                  backgroundColor: isDarkMode ? '#4A5568' : '#E2E8F0',
                  color: isDarkMode ? '#E2E8F0' : '#4A5568',
                  fontSize: '0.7rem',
                }}
              />
            )}
          </Box>
        </AccordionSummary>
        
        <AccordionDetails sx={{ p: 3 }}>
          <Grid container spacing={3}>
            {/* Prompt Editor */}
            <Grid item xs={12} md={8}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Typography
                  variant="subtitle2"
                  sx={{
                    fontWeight: 600,
                    color: isDarkMode ? '#E2E8F0' : '#4A5568',
                    mr: 2,
                  }}
                >
                  Custom Prompt
                </Typography>
                <Tooltip title="Load default prompt for selected model">
                  <IconButton
                    size="small"
                    onClick={onLoadDefaultPrompt}
                    disabled={disabled || !selectedModel}
                    sx={{
                      color: isDarkMode ? '#90CAF9' : '#1976d2',
                    }}
                  >
                    <RestoreIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
              
              <TextField
                id="prompt-textarea"
                fullWidth
                multiline
                rows={8}
                value={prompt || ''}
                onChange={(e) => onPromptChange(e.target.value)}
                placeholder="Enter your custom prompt here. Use variables like {{sellerName}} to personalize for each lead..."
                disabled={disabled}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    fontFamily: 'monospace',
                    fontSize: '0.9rem',
                    '& fieldset': {
                      borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.23)' : 'rgba(0, 0, 0, 0.23)',
                    },
                    '&:hover fieldset': {
                      borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.4)' : 'rgba(0, 0, 0, 0.4)',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                    },
                  },
                }}
              />
              
              {/* Character count and stats */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                <Typography
                  variant="caption"
                  sx={{ color: isDarkMode ? 'rgba(255,255,255,0.6)' : 'rgba(0,0,0,0.5)' }}
                >
                  {characterCount} characters • ~{estimatedTokens} tokens
                </Typography>
                {costEstimation && (
                  <Typography
                    variant="caption"
                    sx={{ color: isDarkMode ? '#90CAF9' : '#1976d2' }}
                  >
                    Est. cost: ${costEstimation.totalCost.toFixed(4)}
                  </Typography>
                )}
              </Box>
            </Grid>
            
            {/* Variable Helper */}
            <Grid item xs={12} md={4}>
              <Typography
                variant="subtitle2"
                sx={{
                  fontWeight: 600,
                  color: isDarkMode ? '#E2E8F0' : '#4A5568',
                  mb: 2,
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                <InfoIcon sx={{ fontSize: 16, mr: 1 }} />
                Available Variables
              </Typography>
              
              <Paper
                sx={{
                  p: 2,
                  backgroundColor: isDarkMode ? '#1A202C' : '#F7FAFC',
                  border: `1px solid ${isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'}`,
                }}
              >
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                  {availableVariables.map((variable) => (
                    <Chip
                      key={variable.name}
                      label={variable.name}
                      size="small"
                      clickable={!disabled}
                      onClick={() => handleVariableClick(variable.name)}
                      sx={{
                        backgroundColor: isDarkMode ? '#4A5568' : '#E2E8F0',
                        color: isDarkMode ? '#E2E8F0' : '#4A5568',
                        fontSize: '0.7rem',
                        fontFamily: 'monospace',
                        '&:hover': {
                          backgroundColor: isDarkMode ? '#2D3748' : '#CBD5E0',
                        },
                      }}
                    />
                  ))}
                </Box>
                
                <Divider sx={{ my: 2 }} />
                
                <Typography
                  variant="caption"
                  sx={{
                    color: isDarkMode ? 'rgba(255,255,255,0.7)' : 'rgba(0,0,0,0.6)',
                    display: 'block',
                    lineHeight: 1.4,
                  }}
                >
                  Click on variables to insert them into your prompt. These will be replaced with actual values from your CSV data.
                </Typography>
              </Paper>
              
              {/* Cost Estimation Panel */}
              {costEstimation && (
                <Paper
                  sx={{
                    p: 2,
                    mt: 2,
                    backgroundColor: isDarkMode ? '#1A202C' : '#F0F9FF',
                    border: `1px solid ${isDarkMode ? 'rgba(144, 202, 249, 0.3)' : 'rgba(59, 130, 246, 0.3)'}`,
                  }}
                >
                  <Typography
                    variant="subtitle2"
                    sx={{
                      fontWeight: 600,
                      color: isDarkMode ? '#90CAF9' : '#1976d2',
                      mb: 1,
                    }}
                  >
                    Cost Estimation
                  </Typography>
                  <Typography
                    variant="caption"
                    sx={{
                      color: isDarkMode ? 'rgba(255,255,255,0.8)' : 'rgba(0,0,0,0.7)',
                      display: 'block',
                      mb: 0.5,
                    }}
                  >
                    Tokens per row: ~{costEstimation.tokensPerRow}
                  </Typography>
                  <Typography
                    variant="caption"
                    sx={{
                      color: isDarkMode ? 'rgba(255,255,255,0.8)' : 'rgba(0,0,0,0.7)',
                      display: 'block',
                      mb: 0.5,
                    }}
                  >
                    Total tokens: ~{costEstimation.totalTokens.toLocaleString()}
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      fontWeight: 600,
                      color: isDarkMode ? '#90CAF9' : '#1976d2',
                    }}
                  >
                    Total cost: ${costEstimation.totalCost.toFixed(4)}
                  </Typography>
                </Paper>
              )}
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>
    </Box>
  );
};

export default AIPromptEditor;
