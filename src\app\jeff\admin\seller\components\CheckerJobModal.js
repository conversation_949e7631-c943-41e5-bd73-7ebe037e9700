'use client';

import React from 'react';
import { Typography, Box, Button } from '@mui/material';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';

const CheckerJobModal = (props) => {
  const {
    addJobModalOpen,
    setAddJobModalOpen,
    isDarkMode,
    useFilters,
    setUseFilters,
    selectedAsins,
    handleAddCheckerJob,
    addCheckerJobMutation,
  } = props;

  return (
    <Dialog
      open={addJobModalOpen}
      onClose={() => {
        setAddJobModalOpen(false);
        setUseFilters(false);
      }}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
        },
      }}
    >
      <DialogTitle sx={{ color: isDarkMode ? '#fff' : 'inherit' }}>
        Add Checker Job
      </DialogTitle>
      <DialogContent>
        <Box sx={{ mb: 2 }}>
          <Typography
            variant="body2"
            sx={{ color: isDarkMode ? '#e2e8f0' : '#4a5568', mb: 2 }}
          >
            {useFilters
              ? 'Create a checker job for all filtered data.'
              : `Create a checker job for ${selectedAsins.length} selected ASIN(s).`}
          </Typography>
        </Box>

        <Box sx={{ mb: 2 }}>
          <Typography
            variant="subtitle2"
            sx={{ color: isDarkMode ? '#fff' : 'inherit', mb: 1 }}
          >
            Selected ASINs:
          </Typography>
          <Box
            sx={{
              maxHeight: '200px',
              overflow: 'auto',
              p: 2,
              backgroundColor: isDarkMode
                ? 'rgba(255, 255, 255, 0.05)'
                : '#f5f5f5',
              borderRadius: '4px',
              border: `1px solid ${
                isDarkMode ? 'rgba(255, 255, 255, 0.12)' : '#e0e0e0'
              }`,
            }}
          >
            {useFilters ? (
              <Typography
                variant="body2"
                sx={{
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  fontStyle: 'italic',
                }}
              >
                Selected all filtered data
              </Typography>
            ) : (
              selectedAsins.map((item, index) => (
                <Typography
                  key={index}
                  variant="body2"
                  sx={{
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    mb: 0.5,
                    fontFamily: 'monospace',
                  }}
                >
                  {item.asin} ({item.countryCode})
                </Typography>
              ))
            )}
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button
          onClick={() => {
            setAddJobModalOpen(false);
            setUseFilters(false);
          }}
          sx={{ color: isDarkMode ? '#e2e8f0' : '#4a5568' }}
        >
          Cancel
        </Button>
        <Button
          variant="contained"
          disabled={addCheckerJobMutation.isPending}
          onClick={async () => {
            await handleAddCheckerJob(useFilters);
            setAddJobModalOpen(false);
            setUseFilters(false);
          }}
          sx={{
            backgroundColor: isDarkMode ? '#7C3AED' : '#6D28D9',
            '&:hover': {
              backgroundColor: isDarkMode ? '#A78BFA' : '#8B5CF6',
            },
          }}
        >
          {addCheckerJobMutation.isPending ? 'Creating...' : 'Create Job'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CheckerJobModal;
