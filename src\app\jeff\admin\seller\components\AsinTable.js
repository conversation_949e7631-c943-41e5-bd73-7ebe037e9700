'use client';

import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Box,
  Chip,
  Link,
  Checkbox,
  Button,
  CircularProgress,
  TextField,
  InputAdornment,
  Tooltip,
  Card,
  CardContent,
  TablePagination,
  IconButton,
} from '@mui/material';
import { getAxiosInstance } from 'config/axios';
import { API_ENDPOINTS } from 'config/api';
import DownloadIcon from '@mui/icons-material/Download';
import RateReviewIcon from '@mui/icons-material/RateReview';
import SearchIcon from '@mui/icons-material/Search';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import { useRouter } from 'next/navigation';
import { useMutation } from '@tanstack/react-query';
import AddIcon from '@mui/icons-material/Add';
import SettingsIcon from '@mui/icons-material/Settings';
import CheckerJobModal from './CheckerJobModal';
import FrequencyModal from './FrequencyModal';
import ConfirmationDialog from './ConfirmationDialog';

const getStatusColor = (status, isDarkMode) => {
  switch (status) {
    case 'PENDING':
      return {
        bg: isDarkMode
          ? 'rgba(144, 202, 249, 0.16)'
          : 'rgba(25, 118, 210, 0.08)',
        text: isDarkMode ? '#90CAF9' : '#1976d2',
      };
    case 'SCRAPED':
      return {
        bg: isDarkMode
          ? 'rgba(129, 199, 132, 0.16)'
          : 'rgba(46, 125, 50, 0.08)',
        text: isDarkMode ? '#81C784' : '#2E7D32',
      };
    case 'FAILED':
      return {
        bg: isDarkMode ? 'rgba(239, 83, 80, 0.16)' : 'rgba(211, 47, 47, 0.08)',
        text: isDarkMode ? '#EF5350' : '#D32F2F',
      };
    case 'REVIEW_PENDING':
      return {
        bg: isDarkMode ? 'rgba(255, 193, 7, 0.16)' : 'rgba(237, 108, 2, 0.08)',
        text: isDarkMode ? '#FFC107' : '#ED6C02',
      };
    case 'REVIEW_SCRAPED':
      return {
        bg: isDarkMode ? 'rgba(76, 175, 80, 0.16)' : 'rgba(46, 125, 50, 0.08)',
        text: isDarkMode ? '#4CAF50' : '#2E7D32',
      };
    default:
      return {
        bg: isDarkMode ? 'rgba(255, 255, 255, 0.16)' : 'rgba(0, 0, 0, 0.08)',
        text: isDarkMode ? '#FFFFFF' : '#000000',
      };
  }
};

const AsinTable = ({
  asinsList,
  isDarkMode,
  setNotification,
  pagination,
  onPageChange,
  onRowsPerPageChange,
  filters,
  getASINWithURLParams,
}) => {
  const router = useRouter();
  const [selectedAsins, setSelectedAsins] = useState([]);
  const [isDownloadLoading, setIsDownloadLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState({
    key: 'createdAt',
    direction: 'desc',
  });
  const [addJobModalOpen, setAddJobModalOpen] = useState(false);
  const [frequencyModalOpen, setFrequencyModalOpen] = useState(false);
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [selectedFrequency, setSelectedFrequency] = useState(1);
  const [useFilters, setUseFilters] = useState(false);

  console.log('selectedAsins', selectedAsins);
  const handleRowClick = (asinString, countryCode) => {
    const existingIndex = selectedAsins.findIndex(
      (item) =>
        item.asin === asinString &&
        item.countryCode === countryCode.toUpperCase(),
    );
    const asinObject = filteredAndSortedAsins.find(
      (item) =>
        item.asin === asinString &&
        item.countryCode.toUpperCase() === countryCode.toUpperCase(),
    );

    const newSelectedAsins =
      existingIndex >= 0
        ? selectedAsins.filter(
          (item) =>
            !(
              item.asin === asinString &&
                item.countryCode === countryCode.toUpperCase()
            ),
        )
        : [
          ...selectedAsins,
          {
            asin: asinString,
            countryCode: asinObject?.countryCode
              ? asinObject.countryCode.toUpperCase()
              : '',
          },
        ];

    setSelectedAsins(newSelectedAsins);
  };

  const handleSelectAll = () => {
    // If some items are selected (indeterminate state), deselect all
    if (
      selectedAsins.length > 0 &&
      selectedAsins.length < filteredAndSortedAsins.length
    ) {
      setSelectedAsins([]);
      return;
    }

    // If all items are selected, deselect all
    if (
      selectedAsins.length === filteredAndSortedAsins.length &&
      filteredAndSortedAsins.every((asin) =>
        selectedAsins.some(
          (item) =>
            item.asin === asin.asin &&
            item.countryCode === asin.countryCode.toUpperCase(),
        ),
      )
    ) {
      setSelectedAsins([]);
      return;
    }

    // If no items are selected, select all
    setSelectedAsins(
      filteredAndSortedAsins.map((asin) => ({
        asin: asin.asin,
        countryCode: asin.countryCode.toUpperCase(),
      })),
    );
  };

  const handleGetReviews = async (isFilter) => {
    console.log('isfilter', isFilter);
    if (!isFilter && selectedAsins.length === 0) {
      setNotification({
        open: true,
        message: 'Please select at least one ASIN to get reviews',
        severity: 'warning',
      });
      return;
    }

    try {
      let payload = {
        sortByMostRecent: true,
      };

      if (isFilter) {
        // Use filter values when isFilter is true
        if (filters.sellerId) {
          payload.seller_id = filters.sellerId;
        }
        if (filters.sellerName) {
          payload.seller_name = filters.sellerName;
        }
        if (filters.asin) {
          payload.asin = filters.asin;
        }
        if (filters.status) {
          payload.status = filters.status;
        }
        if (filters.type) {
          payload.type = filters.type;
        }
        if (filters.reviewsCount && filters.reviewOperator) {
          payload.reviews = `${filters.reviewOperator}${filters.reviewsCount}`;
        }
        // Add useFilters flag as true
        payload.useFilters = true;
      } else {
        // Use selected ASINs when useFilters is false
        payload.asins = selectedAsins;
      }

      const response = await getAxiosInstance({
        cookiesKey: 'jeff-authorization',
      }).post(API_ENDPOINTS.LEX_REVIEWS_BULK, payload);

      console.log('Response from bulk review API:', response);

      setNotification({
        open: true,
        message: isFilter
          ? 'Successfully initiated review scraping for all filtered data'
          : 'Successfully initiated review scraping for selected ASINs',
        severity: 'success',
      });

      console.log('payload handleGetReviews', payload);

      // Clear selection after successful API call only if not using filters
      if (!isFilter) {
        setSelectedAsins([]);
      }
    } catch (error) {
      console.error('Error initiating review scraping:', error);
      setNotification({
        open: true,
        message: 'Error initiating review scraping. Please try again.',
        severity: 'error',
      });
    }
  };

  const handleDownloadAllFilteredData = async () => {
    try {
      setIsDownloadLoading(true);

      // Prepare the payload using the filters from props
      const payload = {};

      if (filters.sellerId) {
        payload.seller_id = filters.sellerId;
      }

      if (filters.sellerName) {
        payload.seller_name = filters.sellerName;
      }

      if (filters.asin) {
        payload.asin = filters.asin;
      }

      if (filters.status) {
        payload.status = filters.status;
      }

      if (filters.reviewsCount && filters.reviewOperator) {
        payload.reviews = `${filters.reviewOperator}${filters.reviewsCount}`;
      }

      const response = await getAxiosInstance({
        cookiesKey: 'jeff-authorization',
      }).post(API_ENDPOINTS.LEX_ASIN_DOWNLOAD, payload, {
        responseType: 'blob', // Important for handling file download
      });

      // Create a blob from the response data
      const blob = new Blob([response.data], { type: 'text/csv' });

      // Create a download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'asin_data.csv');

      // Append to body, click, and remove
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      setNotification({
        open: true,
        message: 'Successfully downloaded ASIN data',
        severity: 'success',
      });
    } catch (error) {
      console.error('Error downloading ASIN data:', error);
      setNotification({
        open: true,
        message: 'Error downloading ASIN data. Please try again.',
        severity: 'error',
      });
    } finally {
      setIsDownloadLoading(false);
    }
  };

  const handleDownloadAsinData = async () => {
    if (selectedAsins.length === 0) {
      setNotification({
        open: true,
        message: 'Please select at least one ASIN to download',
        severity: 'warning',
      });
      return;
    }

    console.log('Asins selected for download:', selectedAsins);

    try {
      setIsDownloadLoading(true);
      const response = await getAxiosInstance({
        cookiesKey: 'jeff-authorization',
      }).post(API_ENDPOINTS.LEX_ASIN_DOWNLOAD, {
        asin: selectedAsins.map((item) => item.asin),
      });

      console.log('Response from download API:', response);

      // Create a blob from the response data
      const blob = new Blob([response.data], { type: 'text/csv' });

      // Create a download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'asin_data.csv');

      // Append to body, click, and remove
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading ASIN data:', error);
      setNotification({
        open: true,
        message: 'Error downloading ASIN data. Please try again.',
        severity: 'error',
      });
    } finally {
      setIsDownloadLoading(false);
    }
  };

  const handleSort = (key) => {
    setSortConfig((currentSort) => ({
      key,
      direction:
        currentSort.key === key && currentSort.direction === 'asc'
          ? 'desc'
          : 'asc',
    }));
  };

  const getSortedData = (data) => {
    if (!sortConfig.key) return data;

    return [...data].sort((a, b) => {
      let aValue = a[sortConfig.key];
      let bValue = b[sortConfig.key];

      // Handle null/undefined values first
      if (aValue === null || aValue === undefined) aValue = '';
      if (bValue === null || bValue === undefined) bValue = '';

      // Handle nested object properties (e.g., seller.sellerId)
      if (sortConfig.key.includes('.')) {
        const keys = sortConfig.key.split('.');
        aValue = keys.reduce((obj, key) => obj?.[key] ?? '', a);
        bValue = keys.reduce((obj, key) => obj?.[key] ?? '', b);
      }

      // Handle date sorting
      if (sortConfig.key === 'createdAt') {
        aValue = aValue ? new Date(aValue).getTime() : 0;
        bValue = bValue ? new Date(bValue).getTime() : 0;
      }

      // Handle numeric sorting
      if (sortConfig.key === 'avgRating' || sortConfig.key === 'totalReviews') {
        aValue = Number(aValue) || 0;
        bValue = Number(bValue) || 0;
      }

      // Handle string sorting
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  };

  // Filter and sort ASINs
  const filteredAndSortedAsins = getSortedData(
    asinsList.filter((asin) => {
      const searchLower = searchTerm.toLowerCase();
      return (
        asin.asin.toLowerCase().includes(searchLower) ||
        asin.title.toLowerCase().includes(searchLower)
      );
    }),
  );

  const handleFilterReviews = () => {
    if (selectedAsins.length === 0) {
      setNotification({
        open: true,
        message: 'Please select at least one ASIN to filter reviews',
        severity: 'warning',
      });
      return;
    }
    const asinsParam = selectedAsins.map((item) => item.asin).join(',');
    router.push(`/jeff/admin/seller?tabID=2&asin=${asinsParam}`);
  };

  const truncateTitle = (title) => {
    if (title && title.length <= 25) return title;
    return title ? title.substring(0, 25) + '...' : '-';
  };

  const SortableTableCell = ({ label, sortKey, width }) => (
    <TableCell
      onClick={() => handleSort(sortKey)}
      sx={{
        backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
        color: isDarkMode ? '#e2e8f0' : '#4a5568',
        fontWeight: 600,
        borderBottom: `1px solid ${
          isDarkMode ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)'
        }`,
        cursor: 'pointer',
        width: width,
        '&:hover': {
          backgroundColor: isDarkMode ? '#4a5568' : '#edf2f7',
        },
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
        {label}
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            ml: 0.5,
            height: '20px',
          }}
        >
          <ArrowUpwardIcon
            sx={{
              fontSize: '0.875rem',
              color:
                sortConfig.key === sortKey && sortConfig.direction === 'asc'
                  ? isDarkMode
                    ? '#90CAF9'
                    : '#1976d2'
                  : isDarkMode
                    ? 'rgba(255, 255, 255, 0.3)'
                    : 'rgba(0, 0, 0, 0.3)',
              height: '10px',
              lineHeight: '10px',
            }}
          />
          <ArrowDownwardIcon
            sx={{
              fontSize: '0.875rem',
              color:
                sortConfig.key === sortKey && sortConfig.direction === 'desc'
                  ? isDarkMode
                    ? '#90CAF9'
                    : '#1976d2'
                  : isDarkMode
                    ? 'rgba(255, 255, 255, 0.3)'
                    : 'rgba(0, 0, 0, 0.3)',
              height: '10px',
              lineHeight: '10px',
            }}
          />
        </Box>
      </Box>
    </TableCell>
  );

  // Mutation for adding checker jobs
  const addCheckerJobMutation = useMutation({
    mutationFn: async (payload) => {
      const response = await getAxiosInstance({
        cookiesKey: 'jeff-authorization',
      }).post('/api/lex/review/checker/add_job', payload);
      return response.data;
    },
    onSuccess: (data) => {
      setNotification({
        open: true,
        message:
          data.message ||
          `Successfully created checker job for ${
            data.data?.totalReviews || 0
          } reviews`,
        severity: 'success',
      });
      setAddJobModalOpen(false);
    },
    onError: (error) => {
      console.error('Add checker job error:', error);
      setNotification({
        open: true,
        message:
          error.response?.data?.error ||
          error.response?.data?.message ||
          'Failed to create checker job',
        severity: 'error',
      });
    },
  });

  // Mutation for changing run frequency
  const changeFrequencyMutation = useMutation({
    mutationFn: async (payload) => {
      const response = await getAxiosInstance({
        cookiesKey: 'jeff-authorization',
      }).patch('/api/lex/review/change_run_frequency', payload);
      return response.data;
    },
    onSuccess: (data) => {
      setNotification({
        open: true,
        message:
          data.message ||
          `Successfully updated run frequency to ${selectedFrequency} for ${
            data.data?.updatedCount || 0
          } reviews`,
        severity: 'success',
      });
      setFrequencyModalOpen(false);
    },
    onError: (error) => {
      console.error('Frequency change error:', error);
      setNotification({
        open: true,
        message:
          error.response?.data?.error ||
          error.response?.data?.message ||
          'Failed to update run frequency',
        severity: 'error',
      });
    },
  });

  const handleAddCheckerJob = (isFilter) => {
    if (!isFilter && selectedAsins.length === 0) {
      setNotification({
        open: true,
        message: 'Please select at least one ASIN',
        severity: 'warning',
      });
      return;
    }

    let payload = {};

    if (isFilter) {
      // Use filter values when useFilters is true
      if (filters.sellerId) {
        payload.seller_id = filters.sellerId;
      }
      if (filters.sellerName) {
        payload.seller_name = filters.sellerName;
      }
      if (filters.asin) {
        payload.asin = filters.asin;
      }
      if (filters.status) {
        payload.status = filters.status;
      }
      if (filters.type) {
        payload.type = filters.type;
      }
      if (filters.reviewsCount && filters.reviewOperator) {
        payload.reviews = `${filters.reviewOperator}${filters.reviewsCount}`;
      }
      // Add useFilters flag as true
      payload.useFilters = true;
    } else {
      // Use selected ASINs when useFilters is false
      const asinList = selectedAsins.map((item) => item.asin);
      payload.asins = asinList;
    }

    console.log('payload addCheckerJobMutation', payload);
    addCheckerJobMutation.mutate(payload);
  };

  const handleChangeFrequency = (isFilter) => {
    if (!isFilter && selectedAsins.length === 0) {
      setNotification({
        open: true,
        message: 'Please select at least one ASIN',
        severity: 'warning',
      });
      return;
    }

    let payload = {
      run_frequency: selectedFrequency,
    };

    if (isFilter) {
      // Use filter values when useFilters is true
      if (filters.sellerId) {
        payload.seller_id = filters.sellerId;
      }
      if (filters.sellerName) {
        payload.seller_name = filters.sellerName;
      }
      if (filters.asin) {
        payload.asin = filters.asin;
      }
      if (filters.status) {
        payload.status = filters.status;
      }
      if (filters.type) {
        payload.type = filters.type;
      }
      if (filters.reviewsCount && filters.reviewOperator) {
        payload.reviews = `${filters.reviewOperator}${filters.reviewsCount}`;
      }
      // Add useFilters flag as true
      payload.useFilters = true;
    } else {
      // Use selected ASINs when useFilters is false
      const asinList = selectedAsins.map((item) => item.asin);
      payload.asins = asinList;
    }

    console.log('payload changeFrequencyMutation', payload);

    changeFrequencyMutation.mutate(payload);
  };

  const handleMarkClient = async (asinValue, currentType) => {
    const isClient = currentType === 'CLIENT';
    const newType = isClient ? null : 'CLIENT';

    try {
      await getAxiosInstance({ cookiesKey: 'jeff-authorization' }).patch(
        API_ENDPOINTS.LEX_ASIN,
        {
          asin: asinValue,
          type: newType,
        },
      );

      setNotification({
        open: true,
        message: isClient
          ? 'ASIN unmarked as client successfully'
          : 'ASIN marked as client successfully',
        severity: 'success',
      });

      await getASINWithURLParams();
    } catch (error) {
      setNotification({
        open: true,
        message: 'Failed to update ASIN status',
        severity: 'error',
      });
    }
  };

  return (
    <Card
      sx={{
        backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        borderRadius: '12px',
        overflow: 'hidden',
      }}
    >
      <CardContent sx={{ p: 3 }}>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 3,
          }}
        >
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              color: isDarkMode ? '#fff' : 'inherit',
            }}
          >
            ASIN Data
          </Typography>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: 2,
              alignItems: 'flex-start',
            }}
          >
            {/* First row: main action buttons */}
            <Box
              sx={{
                display: 'flex',
                gap: 1,
                alignItems: 'center',
                width: '100%',
              }}
            >
              <Button
                variant="outlined"
                onClick={handleFilterReviews}
                disabled={selectedAsins.length === 0}
                sx={{
                  borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  color: isDarkMode ? '#90CAF9' : '#1976d2',
                  '&:hover': {
                    borderColor: isDarkMode ? '#64B5F6' : '#1565C0',
                    backgroundColor: isDarkMode
                      ? 'rgba(144, 202, 249, 0.08)'
                      : 'rgba(25, 118, 210, 0.08)',
                  },
                  borderRadius: '8px',
                  flex: 1,
                }}
              >
                Filter Reviews
              </Button>
              <Button
                variant="outlined"
                color="primary"
                size="small"
                startIcon={<RateReviewIcon />}
                onClick={() => handleGetReviews(false)}
                disabled={!useFilters && selectedAsins.length === 0}
                sx={{
                  borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  color: isDarkMode ? '#90CAF9' : '#1976d2',
                  '&:hover': {
                    borderColor: isDarkMode ? '#64B5F6' : '#1565C0',
                    backgroundColor: isDarkMode
                      ? 'rgba(144, 202, 249, 0.08)'
                      : 'rgba(25, 118, 210, 0.08)',
                  },
                  borderRadius: '8px',
                  flex: 1,
                }}
              >
                Generate Reviews
              </Button>
              <Button
                variant="outlined"
                color="secondary"
                size="small"
                startIcon={<AddIcon />}
                onClick={() => setAddJobModalOpen(true)}
                disabled={!useFilters && selectedAsins.length === 0}
                sx={{
                  borderColor: isDarkMode ? '#7C3AED' : '#6D28D9',
                  color: isDarkMode ? '#7C3AED' : '#6D28D9',
                  '&:hover': {
                    borderColor: isDarkMode ? '#A78BFA' : '#8B5CF6',
                    backgroundColor: isDarkMode
                      ? 'rgba(124, 58, 237, 0.08)'
                      : 'rgba(109, 40, 217, 0.08)',
                  },
                  borderRadius: '8px',
                  flex: 1,
                }}
              >
                Add Checker Job
              </Button>
              <Button
                variant="outlined"
                color="secondary"
                size="small"
                startIcon={<SettingsIcon />}
                onClick={() => setFrequencyModalOpen(true)}
                disabled={!useFilters && selectedAsins.length === 0}
                sx={{
                  borderColor: isDarkMode ? '#7C3AED' : '#6D28D9',
                  color: isDarkMode ? '#7C3AED' : '#6D28D9',
                  '&:hover': {
                    borderColor: isDarkMode ? '#A78BFA' : '#8B5CF6',
                    backgroundColor: isDarkMode
                      ? 'rgba(124, 58, 237, 0.08)'
                      : 'rgba(109, 40, 217, 0.08)',
                  },
                  borderRadius: '8px',
                  flex: 1,
                }}
              >
                Change Frequency
              </Button>
            </Box>
            {/* Second row: new buttons for all filtered data */}
            <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
              <Button
                variant="outlined"
                color="primary"
                size="small"
                startIcon={<RateReviewIcon />}
                onClick={() => {
                  setSelectedAsins([]);
                  setConfirmationDialogOpen(true);
                }}
                sx={{
                  borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  color: isDarkMode ? '#90CAF9' : '#1976d2',
                  '&:hover': {
                    borderColor: isDarkMode ? '#64B5F6' : '#1565C0',
                    backgroundColor: isDarkMode
                      ? 'rgba(144, 202, 249, 0.08)'
                      : 'rgba(25, 118, 210, 0.08)',
                  },
                  borderRadius: '8px',
                }}
              >
                Generate Reviews for All Filtered Data
              </Button>
              <Button
                variant="outlined"
                color="secondary"
                size="small"
                startIcon={<AddIcon />}
                onClick={() => {
                  setSelectedAsins([]);
                  setAddJobModalOpen(true);
                  setUseFilters(true);
                }}
                sx={{
                  borderColor: isDarkMode ? '#7C3AED' : '#6D28D9',
                  color: isDarkMode ? '#7C3AED' : '#6D28D9',
                  '&:hover': {
                    borderColor: isDarkMode ? '#A78BFA' : '#8B5CF6',
                    backgroundColor: isDarkMode
                      ? 'rgba(124, 58, 237, 0.08)'
                      : 'rgba(109, 40, 217, 0.08)',
                  },
                  borderRadius: '8px',
                }}
              >
                Add Checker Job for All Filtered Data
              </Button>
              <Button
                variant="outlined"
                color="secondary"
                size="small"
                startIcon={<SettingsIcon />}
                onClick={() => {
                  setSelectedAsins([]);
                  setFrequencyModalOpen(true);
                  setUseFilters(true);
                }}
                sx={{
                  borderColor: isDarkMode ? '#7C3AED' : '#6D28D9',
                  color: isDarkMode ? '#7C3AED' : '#6D28D9',
                  '&:hover': {
                    borderColor: isDarkMode ? '#A78BFA' : '#8B5CF6',
                    backgroundColor: isDarkMode
                      ? 'rgba(124, 58, 237, 0.08)'
                      : 'rgba(109, 40, 217, 0.08)',
                  },
                  borderRadius: '8px',
                }}
              >
                Change Frequency for All Filtered Data
              </Button>
            </Box>
            {/* Third row: download buttons and search */}
            <Box
              sx={{
                display: 'flex',
                gap: 1,
                alignItems: 'center',
                width: '100%',
              }}
            >
              <Button
                variant="outlined"
                size="small"
                startIcon={
                  isDownloadLoading ? (
                    <CircularProgress size={20} />
                  ) : (
                    <DownloadIcon />
                  )
                }
                onClick={() => {
                  setSelectedAsins([]);
                  handleDownloadAllFilteredData();
                }}
                disabled={
                  filteredAndSortedAsins.length === 0 || isDownloadLoading
                }
                sx={{
                  borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  color: isDarkMode ? '#90CAF9' : '#1976d2',
                  '&:hover': {
                    borderColor: isDarkMode ? '#64B5F6' : '#1565C0',
                    backgroundColor: isDarkMode
                      ? 'rgba(144, 202, 249, 0.08)'
                      : 'rgba(25, 118, 210, 0.08)',
                  },
                  borderRadius: '8px',
                  flex: 1,
                }}
              >
                Download All Filtered Data
              </Button>
              <Button
                variant="outlined"
                size="small"
                startIcon={
                  isDownloadLoading ? (
                    <CircularProgress size={20} />
                  ) : (
                    <DownloadIcon />
                  )
                }
                onClick={handleDownloadAsinData}
                disabled={selectedAsins.length === 0 || isDownloadLoading}
                sx={{
                  borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  color: isDarkMode ? '#90CAF9' : '#1976d2',
                  '&:hover': {
                    borderColor: isDarkMode ? '#64B5F6' : '#1565C0',
                    backgroundColor: isDarkMode
                      ? 'rgba(144, 202, 249, 0.08)'
                      : 'rgba(25, 118, 210, 0.08)',
                  },
                  borderRadius: '8px',
                  flex: 1,
                }}
              >
                Download Selected ASIN
              </Button>
              <TextField
                size="small"
                placeholder="Search ASINs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon
                        sx={{
                          color: isDarkMode
                            ? 'rgba(255, 255, 255, 0.7)'
                            : 'inherit',
                        }}
                      />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  flex: 1,
                  '& .MuiOutlinedInput-root': {
                    backgroundColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.05)'
                      : 'inherit',
                    color: isDarkMode ? '#fff' : 'inherit',
                    borderRadius: '8px',
                    '& fieldset': {
                      borderColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.23)'
                        : 'inherit',
                    },
                    '&:hover fieldset': {
                      borderColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.4)'
                        : 'inherit',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                    },
                  },
                  '& .MuiInputBase-input::placeholder': {
                    color: isDarkMode ? 'rgba(255, 255, 255, 0.5)' : 'inherit',
                  },
                }}
              />
            </Box>
          </Box>
        </Box>

        <TableContainer sx={{ borderRadius: '8px', overflow: 'auto' }}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell
                  sx={{
                    backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    fontWeight: 600,
                    borderBottom: `1px solid ${
                      isDarkMode
                        ? 'rgba(255, 255, 255, 0.12)'
                        : 'rgba(0, 0, 0, 0.12)'
                    }`,
                    width: '50px',
                  }}
                >
                  <Checkbox
                    checked={
                      selectedAsins.length > 0 &&
                      filteredAndSortedAsins.length > 0 &&
                      filteredAndSortedAsins.every((asin) =>
                        selectedAsins.some(
                          (item) =>
                            item.asin === asin.asin &&
                            item.countryCode === asin.countryCode.toUpperCase(),
                        ),
                      )
                    }
                    indeterminate={
                      selectedAsins.length > 0 &&
                      selectedAsins.length < filteredAndSortedAsins.length
                    }
                    onChange={handleSelectAll}
                    sx={{
                      color: isDarkMode ? '#90CAF9' : '#1976d2',
                      '&.Mui-checked': {
                        color: isDarkMode ? '#90CAF9' : '#1976d2',
                      },
                    }}
                  />
                </TableCell>
                <SortableTableCell label="ASIN" sortKey="asin" width="120px" />
                <TableCell
                  sx={{
                    backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    fontWeight: 600,
                    borderBottom: `1px solid ${
                      isDarkMode
                        ? 'rgba(255, 255, 255, 0.12)'
                        : 'rgba(0, 0, 0, 0.12)'
                    }`,
                  }}
                >
                  Mark Client
                </TableCell>
                <SortableTableCell label="Title" sortKey="title" />
                <TableCell
                  sx={{
                    backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    fontWeight: 600,
                    borderBottom: `1px solid ${
                      isDarkMode
                        ? 'rgba(255, 255, 255, 0.12)'
                        : 'rgba(0, 0, 0, 0.12)'
                    }`,
                  }}
                >
                  Status
                </TableCell>
                <TableCell
                  sx={{
                    backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    fontWeight: 600,
                    borderBottom: `1px solid ${
                      isDarkMode
                        ? 'rgba(255, 255, 255, 0.12)'
                        : 'rgba(0, 0, 0, 0.12)'
                    }`,
                  }}
                >
                  Country Code
                </TableCell>
                <TableCell
                  sx={{
                    backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    fontWeight: 600,
                    borderBottom: `1px solid ${
                      isDarkMode
                        ? 'rgba(255, 255, 255, 0.12)'
                        : 'rgba(0, 0, 0, 0.12)'
                    }`,
                  }}
                >
                  Type
                </TableCell>
                <SortableTableCell
                  label="Avg. Rating"
                  sortKey="avgRating"
                  width="120px"
                />
                <SortableTableCell
                  label="Total Reviews"
                  sortKey="totalReviews"
                  width="120px"
                />
                <TableCell
                  sx={{
                    backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    fontWeight: 600,
                    borderBottom: `1px solid ${
                      isDarkMode
                        ? 'rgba(255, 255, 255, 0.12)'
                        : 'rgba(0, 0, 0, 0.12)'
                    }`,
                    width: '200px',
                    minWidth: '200px',
                  }}
                >
                  Latest Data
                </TableCell>
                <SortableTableCell
                  label="Seller"
                  sortKey="seller.sellerId"
                  width="180px"
                />
                <SortableTableCell
                  label="Created At"
                  sortKey="createdAt"
                  width="180px"
                />
              </TableRow>
            </TableHead>
            <TableBody>
              {!filteredAndSortedAsins ||
              filteredAndSortedAsins.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={11}
                      align="center"
                      sx={{
                        borderBottomColor: isDarkMode
                          ? 'rgba(255, 255, 255, 0.12)'
                          : undefined,
                      }}
                    >
                      <Typography
                        variant="body2"
                        sx={{
                          py: 2,
                          color: isDarkMode
                            ? 'rgba(255, 255, 255, 0.5)'
                            : 'text.secondary',
                        }}
                      >
                        {searchTerm
                          ? 'No matching ASINs found'
                          : 'No ASINs found'}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredAndSortedAsins.map((asin) => {
                    const statusColors = getStatusColor(asin.status, isDarkMode);
                    const isSelected = selectedAsins.some(
                      (item) =>
                        item.asin === asin.asin &&
                      item.countryCode === asin.countryCode.toUpperCase(),
                    );

                    return (
                      <TableRow
                        key={`${asin.asin}-${asin.countryCode}`}
                        onClick={() =>
                          handleRowClick(asin.asin, asin.countryCode)
                        }
                        sx={{
                          '&:nth-of-type(odd)': {
                            backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                          },
                          '&:hover': {
                            backgroundColor: isDarkMode ? '#4a5568' : '#edf2f7',
                            cursor: 'pointer',
                          },
                          backgroundColor: isSelected
                            ? isDarkMode
                              ? 'rgba(144, 202, 249, 0.16)'
                              : 'rgba(25, 118, 210, 0.08)'
                            : 'inherit',
                        }}
                      >
                        <TableCell
                          onClick={(e) => e.stopPropagation()}
                          sx={{
                            borderBottom: `1px solid ${
                              isDarkMode
                                ? 'rgba(255, 255, 255, 0.12)'
                                : 'rgba(0, 0, 0, 0.12)'
                            }`,
                          }}
                        >
                          <Checkbox
                            checked={isSelected}
                            onChange={() =>
                              handleRowClick(asin.asin, asin.countryCode)
                            }
                            sx={{
                              color: isDarkMode ? '#90CAF9' : '#1976d2',
                              '&.Mui-checked': {
                                color: isDarkMode ? '#90CAF9' : '#1976d2',
                              },
                            }}
                          />
                        </TableCell>
                        <TableCell
                          sx={{
                            color: isDarkMode ? '#e2e8f0' : '#4a5568',
                            borderBottom: `1px solid ${
                              isDarkMode
                                ? 'rgba(255, 255, 255, 0.12)'
                                : 'rgba(0, 0, 0, 0.12)'
                            }`,
                          }}
                        >
                          <Link
                            href={asin.productLink}
                            target="_blank"
                            rel="noopener noreferrer"
                            onClick={(e) => e.stopPropagation()}
                            sx={{
                              color: isDarkMode ? '#90CAF9' : '#1976d2',
                              textDecoration: 'none',
                              '&:hover': {
                                textDecoration: 'underline',
                              },
                            }}
                          >
                            {asin.asin}
                          </Link>
                        </TableCell>
                        <TableCell
                          sx={{
                            color: isDarkMode ? '#e2e8f0' : '#4a5568',
                            borderBottom: `1px solid ${
                              isDarkMode
                                ? 'rgba(255, 255, 255, 0.12)'
                                : 'rgba(0, 0, 0, 0.12)'
                            }`,
                          }}
                        >
                          <Box display="flex" justifyContent="center">
                            <Tooltip
                              title={
                                asin.type === 'CLIENT'
                                  ? 'Unmark as client'
                                  : 'Mark as client'
                              }
                            >
                              <span>
                                <IconButton
                                  size="small"
                                  color={
                                    asin.type === 'CLIENT' ? 'success' : 'primary'
                                  }
                                  onClick={async (e) => {
                                    e.stopPropagation();
                                    await handleMarkClient(asin.asin, asin.type);
                                  }}
                                >
                                  {asin.type === 'CLIENT' ? (
                                    <CheckCircleIcon />
                                  ) : (
                                    <AddCircleOutlineIcon />
                                  )}
                                </IconButton>
                              </span>
                            </Tooltip>
                          </Box>
                        </TableCell>

                        <TableCell
                          sx={{
                            color: isDarkMode ? '#e2e8f0' : '#4a5568',
                            borderBottom: `1px solid ${
                              isDarkMode
                                ? 'rgba(255, 255, 255, 0.12)'
                                : 'rgba(0, 0, 0, 0.12)'
                            }`,
                          }}
                        >
                          <Tooltip
                            title={asin.title}
                            placement="top"
                            arrow
                            componentsProps={{
                              tooltip: {
                                sx: {
                                  bgcolor: isDarkMode ? '#2d3748' : '#fff',
                                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                                  '& .MuiTooltip-arrow': {
                                    color: isDarkMode ? '#2d3748' : '#fff',
                                  },
                                },
                              },
                            }}
                          >
                            <Typography
                              variant="body2"
                              sx={{
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                              }}
                            >
                              {truncateTitle(asin.title)}
                            </Typography>
                          </Tooltip>
                        </TableCell>
                        <TableCell
                          sx={{
                            borderBottom: `1px solid ${
                              isDarkMode
                                ? 'rgba(255, 255, 255, 0.12)'
                                : 'rgba(0, 0, 0, 0.12)'
                            }`,
                          }}
                        >
                          <Chip
                            label={
                              asin.status === 'SCRAPED'
                                ? 'ASIN SCRAPED'
                                : asin.status
                            }
                            sx={{
                              backgroundColor: statusColors.bg,
                              color: statusColors.text,
                              fontWeight: 500,
                              borderRadius: '4px',
                            }}
                          />
                        </TableCell>
                        <TableCell
                          sx={{
                            borderBottom: `1px solid ${
                              isDarkMode
                                ? 'rgba(255, 255, 255, 0.12)'
                                : 'rgba(0, 0, 0, 0.12)'
                            }`,
                            color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          }}
                        >
                          {asin.countryCode
                            ? asin.countryCode.toUpperCase()
                            : '-'}
                        </TableCell>
                        <TableCell
                          sx={{
                            color: isDarkMode ? '#e2e8f0' : '#4a5568',
                            borderBottom: `1px solid ${
                              isDarkMode
                                ? 'rgba(255, 255, 255, 0.12)'
                                : 'rgba(0, 0, 0, 0.12)'
                            }`,
                          }}
                        >
                          {asin.type ? asin.type : '-'}
                        </TableCell>
                        <TableCell
                          sx={{
                            color: isDarkMode ? '#e2e8f0' : '#4a5568',
                            borderBottom: `1px solid ${
                              isDarkMode
                                ? 'rgba(255, 255, 255, 0.12)'
                                : 'rgba(0, 0, 0, 0.12)'
                            }`,
                          }}
                        >
                          {asin.avgRating}
                        </TableCell>
                        <TableCell
                          sx={{
                            color: isDarkMode ? '#e2e8f0' : '#4a5568',
                            borderBottom: `1px solid ${
                              isDarkMode
                                ? 'rgba(255, 255, 255, 0.12)'
                                : 'rgba(0, 0, 0, 0.12)'
                            }`,
                          }}
                        >
                          {asin.totalReviews}
                        </TableCell>
                        <TableCell
                          sx={{
                            color: isDarkMode ? '#e2e8f0' : '#4a5568',
                            borderBottom: `1px solid ${
                              isDarkMode
                                ? 'rgba(255, 255, 255, 0.12)'
                                : 'rgba(0, 0, 0, 0.12)'
                            }`,
                            width: '200px',
                            minWidth: '200px',
                          }}
                        >
                          <Box
                            sx={{
                              display: 'flex',
                              flexDirection: 'column',
                              gap: 1,
                              padding: '2px',
                            }}
                          >
                            <Box
                              sx={{
                                display: 'flex',
                                gap: 4,
                                alignItems: 'center',
                              }}
                            >
                              <Typography
                                variant="caption"
                                sx={{
                                  color: isDarkMode
                                    ? 'rgba(255, 255, 255, 0.7)'
                                    : 'text.secondary',
                                  fontWeight: 500,
                                }}
                              >
                              Avg Rating:
                              </Typography>

                              <Typography
                                variant="body2"
                                sx={{ fontWeight: 600 }}
                              >
                                {asin.latestData?.avgRating ?? '-'}
                              </Typography>
                            </Box>

                            <Box
                              sx={{
                                display: 'flex',
                                gap: 3,
                                alignItems: 'center',
                              }}
                            >
                              <Typography
                                variant="caption"
                                sx={{
                                  color: isDarkMode
                                    ? 'rgba(255, 255, 255, 0.7)'
                                    : 'text.secondary',
                                  fontWeight: 500,
                                }}
                              >
                              Total Reviews:
                              </Typography>
                              <Typography
                                variant="body2"
                                sx={{ fontWeight: 600 }}
                              >
                                {asin.latestData?.totalReviews ?? '-'}
                              </Typography>
                            </Box>
                            <Box
                              sx={{
                                display: 'flex',
                                gap: 2,
                                alignItems: 'center',
                              }}
                            >
                              <Typography
                                variant="caption"
                                sx={{
                                  color: isDarkMode
                                    ? 'rgba(255, 255, 255, 0.7)'
                                    : 'text.secondary',
                                  fontWeight: 500,
                                }}
                              >
                              Review Counts:
                              </Typography>
                              <Typography
                                variant="body2"
                                sx={{ fontWeight: 600 }}
                              >
                                {asin.latestData?.reviewCounts ?? '-'}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell
                          sx={{
                            color: isDarkMode ? '#e2e8f0' : '#4a5568',
                            borderBottom: `1px solid ${
                              isDarkMode
                                ? 'rgba(255, 255, 255, 0.12)'
                                : 'rgba(0, 0, 0, 0.12)'
                            }`,
                          }}
                        >
                          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {asin.seller?.sellerId || '-'}
                            </Typography>
                            <Typography
                              variant="caption"
                              sx={{
                                color: isDarkMode
                                  ? 'rgba(255, 255, 255, 0.7)'
                                  : 'text.secondary',
                              }}
                            >
                              {asin.seller?.name || '-'}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell
                          sx={{
                            color: isDarkMode ? '#e2e8f0' : '#4a5568',
                            borderBottom: `1px solid ${
                              isDarkMode
                                ? 'rgba(255, 255, 255, 0.12)'
                                : 'rgba(0, 0, 0, 0.12)'
                            }`,
                          }}
                        >
                          {new Date(asin.createdAt).toLocaleString()}
                        </TableCell>
                      </TableRow>
                    );
                  })
                )}
            </TableBody>
          </Table>
        </TableContainer>

        <TablePagination
          component="div"
          count={pagination.total}
          page={pagination.page - 1} // Convert to 0-based index for MUI
          onPageChange={onPageChange}
          rowsPerPage={pagination.limit}
          onRowsPerPageChange={onRowsPerPageChange}
          rowsPerPageOptions={[10, 20, 50, 100]}
          labelDisplayedRows={({ from, to, count }) =>
            `${from}-${to} of ${count} (${Math.ceil(
              count / pagination.limit,
            )} pages)`
          }
          sx={{
            color: isDarkMode ? '#fff' : 'inherit',
            '.MuiTablePagination-select': {
              color: isDarkMode ? '#fff' : 'inherit',
            },
            '.MuiTablePagination-selectIcon': {
              color: isDarkMode ? '#fff' : 'inherit',
            },
          }}
        />

        {/* Add Checker Job Modal */}
        <CheckerJobModal
          addJobModalOpen={addJobModalOpen}
          setAddJobModalOpen={setAddJobModalOpen}
          isDarkMode={isDarkMode}
          useFilters={useFilters}
          setUseFilters={setUseFilters}
          selectedAsins={selectedAsins}
          handleAddCheckerJob={handleAddCheckerJob}
          addCheckerJobMutation={addCheckerJobMutation}
          setSelectedAsins={setSelectedAsins}
        />

        {/* Change Frequency Modal */}
        <FrequencyModal
          frequencyModalOpen={frequencyModalOpen}
          setFrequencyModalOpen={setFrequencyModalOpen}
          isDarkMode={isDarkMode}
          useFilters={useFilters}
          setUseFilters={setUseFilters}
          selectedAsins={selectedAsins}
          selectedFrequency={selectedFrequency}
          setSelectedFrequency={setSelectedFrequency}
          changeFrequencyMutation={changeFrequencyMutation}
          handleChangeFrequency={handleChangeFrequency}
          setSelectedAsins={setSelectedAsins}
        />

        {/* Confirmation Dialog */}
        <ConfirmationDialog
          open={confirmationDialogOpen}
          onClose={() => setConfirmationDialogOpen(false)}
          onConfirm={() => handleGetReviews(true)}
          title="Are you sure?"
        />
      </CardContent>
    </Card>
  );
};

export default AsinTable;
