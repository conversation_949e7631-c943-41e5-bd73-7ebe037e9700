import { useState, useCallback, useMemo } from 'react';
import { useClients, useCreateClient } from './clientQueries';

/**
 * Hook for managing clients
 * @param {Object} user - The currently logged in user
 * @returns {Object} Client management state and functions
 */
export const useClientManagement = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [notification, setNotification] = useState({
    open: false,
    message: '',
    severity: 'info'
  });

  const { 
    data: clients = [], 
    isLoading, 
    refetch: loadClients 
  } = useClients();

  const createClientMutation = useCreateClient();

  const handleCreateClient = useCallback(async (clientData) => {
    try {
      await createClientMutation.mutateAsync(clientData);
      
      // Show success notification
      setNotification({
        open: true,
        message: 'Client created successfully!',
        severity: 'success'
      });
      
      // Refresh the clients list
      await loadClients();
      
      return true;
    } catch (error) {
      console.error('Error creating client:', error);
      setNotification({
        open: true,
        message: 'Failed to create client: ' + (error.message || 'Unknown error'),
        severity: 'error'
      });
      throw error;
    }
  }, [createClientMutation, loadClients]);

  const handleSearch = useCallback((term) => {
    setSearchTerm(term);
  }, []);

  const handleCloseNotification = useCallback(() => {
    setNotification(prev => ({ ...prev, open: false }));
  }, []);

  // Memoize filtered clients to prevent unnecessary recalculations
  const filteredClients = useMemo(() => {
    if (!searchTerm.trim()) {
      return clients;
    }
    
    return clients.filter(client => 
      client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (client.slug && client.slug.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  }, [clients, searchTerm]);

  return {
    clients: filteredClients,
    isLoading,
    isCreating: createClientMutation.isPending,
    searchTerm,
    notification,
    loadClients,
    handleCreateClient,
    handleSearch,
    handleCloseNotification
  };
}; 