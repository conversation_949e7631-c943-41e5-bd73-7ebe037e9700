'use client';

import React from 'react';
import {
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Paper,
  Typography,
  Grid,
} from '@mui/material';
import FilterListIcon from '@mui/icons-material/FilterList';
import { useAdminTheme } from 'views/Jeff/AdminThemeContext';

const JobFilters = ({
  modeFilter,
  onModeFilterChange,
  aiModelFilter,
  onAIModelFilterChange,
  statusFilter,
  onStatusFilterChange,
  dateFilter,
  onDateFilterChange,
}) => {
  const { mode: adminThemeMode } = useAdminTheme() || { mode: 'light' };
  const isDarkMode = adminThemeMode === 'dark';

  const modeOptions = [
    { value: 'all', label: 'All Modes' },
    { value: 'serp', label: 'SERP Scraper' },
    { value: 'ai', label: 'AI Website Finder' },
    { value: 'input', label: 'Input Domain' },
  ];

  const aiModelOptions = [
    { value: 'all', label: 'All Models' },
    { value: 'openai', label: 'OpenAI GPT-4' },
    { value: 'claude', label: 'Claude 3.5 Sonnet' },
    { value: 'gemini', label: 'Gemini Pro' },
  ];

  const statusOptions = [
    { value: 'all', label: 'All Statuses' },
    { value: 'pending', label: 'Pending' },
    { value: 'processing', label: 'Processing' },
    { value: 'completed', label: 'Completed' },
    { value: 'failed', label: 'Failed' },
  ];

  const dateOptions = [
    { value: 'all', label: 'All Time' },
    { value: 'today', label: 'Today' },
    { value: 'week', label: 'This Week' },
    { value: 'month', label: 'This Month' },
  ];

  const getActiveFiltersCount = () => {
    let count = 0;
    if (modeFilter && modeFilter !== 'all') count++;
    if (aiModelFilter && aiModelFilter !== 'all') count++;
    if (statusFilter && statusFilter !== 'all') count++;
    if (dateFilter && dateFilter !== 'all') count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  return (
    <Paper
      sx={{
        p: 2,
        mb: 2,
        backgroundColor: isDarkMode ? '#2D3748' : '#FFFFFF',
        border: `1px solid ${isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'}`,
        borderRadius: '8px',
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <FilterListIcon
          sx={{
            color: isDarkMode ? '#E2E8F0' : '#4A5568',
            mr: 1,
          }}
        />
        <Typography
          variant="subtitle1"
          sx={{
            fontWeight: 600,
            color: isDarkMode ? '#E2E8F0' : '#4A5568',
          }}
        >
          Filters
        </Typography>
        {activeFiltersCount > 0 && (
          <Chip
            label={`${activeFiltersCount} active`}
            size="small"
            sx={{
              ml: 2,
              backgroundColor: isDarkMode ? '#4A5568' : '#E2E8F0',
              color: isDarkMode ? '#E2E8F0' : '#4A5568',
              fontSize: '0.7rem',
            }}
          />
        )}
      </Box>

      <Grid container spacing={2}>
        {/* Mode Filter */}
        <Grid item xs={12} sm={6} md={3}>
          <FormControl 
            fullWidth 
            size="small"
            sx={{
              '& .MuiOutlinedInput-root': {
                color: isDarkMode ? '#e2e8f0' : '#4a5568',
                '& fieldset': {
                  borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.23)' : 'rgba(0, 0, 0, 0.23)',
                },
                '&:hover fieldset': {
                  borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.4)' : 'rgba(0, 0, 0, 0.4)',
                },
                '&.Mui-focused fieldset': {
                  borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                },
              },
              '& .MuiInputLabel-root': {
                color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)',
              },
            }}
          >
            <InputLabel>Mode</InputLabel>
            <Select
              value={modeFilter || 'all'}
              label="Mode"
              onChange={(e) => onModeFilterChange(e.target.value)}
            >
              {modeOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* AI Model Filter - only show when AI mode is selected or all modes */}
        <Grid item xs={12} sm={6} md={3}>
          <FormControl 
            fullWidth 
            size="small"
            disabled={modeFilter === 'serp' || modeFilter === 'input'}
            sx={{
              '& .MuiOutlinedInput-root': {
                color: isDarkMode ? '#e2e8f0' : '#4a5568',
                '& fieldset': {
                  borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.23)' : 'rgba(0, 0, 0, 0.23)',
                },
                '&:hover fieldset': {
                  borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.4)' : 'rgba(0, 0, 0, 0.4)',
                },
                '&.Mui-focused fieldset': {
                  borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                },
              },
              '& .MuiInputLabel-root': {
                color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)',
              },
            }}
          >
            <InputLabel>AI Model</InputLabel>
            <Select
              value={aiModelFilter || 'all'}
              label="AI Model"
              onChange={(e) => onAIModelFilterChange(e.target.value)}
            >
              {aiModelOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Status Filter */}
        <Grid item xs={12} sm={6} md={3}>
          <FormControl 
            fullWidth 
            size="small"
            sx={{
              '& .MuiOutlinedInput-root': {
                color: isDarkMode ? '#e2e8f0' : '#4a5568',
                '& fieldset': {
                  borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.23)' : 'rgba(0, 0, 0, 0.23)',
                },
                '&:hover fieldset': {
                  borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.4)' : 'rgba(0, 0, 0, 0.4)',
                },
                '&.Mui-focused fieldset': {
                  borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                },
              },
              '& .MuiInputLabel-root': {
                color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)',
              },
            }}
          >
            <InputLabel>Status</InputLabel>
            <Select
              value={statusFilter || 'all'}
              label="Status"
              onChange={(e) => onStatusFilterChange(e.target.value)}
            >
              {statusOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Date Filter */}
        <Grid item xs={12} sm={6} md={3}>
          <FormControl 
            fullWidth 
            size="small"
            sx={{
              '& .MuiOutlinedInput-root': {
                color: isDarkMode ? '#e2e8f0' : '#4a5568',
                '& fieldset': {
                  borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.23)' : 'rgba(0, 0, 0, 0.23)',
                },
                '&:hover fieldset': {
                  borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.4)' : 'rgba(0, 0, 0, 0.4)',
                },
                '&.Mui-focused fieldset': {
                  borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                },
              },
              '& .MuiInputLabel-root': {
                color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)',
              },
            }}
          >
            <InputLabel>Date</InputLabel>
            <Select
              value={dateFilter || 'all'}
              label="Date"
              onChange={(e) => onDateFilterChange(e.target.value)}
            >
              {dateOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default JobFilters;
