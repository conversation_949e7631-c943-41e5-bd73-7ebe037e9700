import React from 'react';
import {
  Box,
  Card,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress
} from '@mui/material';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import StopIcon from '@mui/icons-material/Stop';
import RestartAltIcon from '@mui/icons-material/RestartAlt';
import WarningIcon from '@mui/icons-material/Warning';

const InstancesTable = ({
  instances,
  isLoading,
  actionLoading,
  handleInstanceAction,
  isDarkMode,
  stats
}) => {
  const getStateColor = (state) => {
    switch (state) {
      case 'running':
        return {
          bg: '#48BB78',
          text: '#F0FFF4'
        };
      case 'stopped':
        return {
          bg: '#F56565',
          text: '#FFF5F5'
        };
      case 'pending':
      case 'stopping':
        return {
          bg: '#ECC94B',
          text: '#FFFFF0'
        };
      default:
        return {
          bg: '#A0AEC0',
          text: '#F7FAFC'
        };
    }
  };

  const renderInstanceState = (state) => {
    const colors = getStateColor(state);
    return (
      <Chip
        label={state.charAt(0).toUpperCase() + state.slice(1)}
        sx={{
          backgroundColor: colors.bg,
          color: colors.text,
          fontWeight: 500,
          fontSize: '0.75rem',
          height: '24px'
        }}
      />
    );
  };

  if (isLoading) {
    return (
      <Box 
        display="flex" 
        justifyContent="center" 
        alignItems="center" 
        py={4}
        sx={{
          backgroundColor: isDarkMode ? '#1A202C' : '#F7FAFC',
          borderRadius: '8px',
          border: '1px solid',
          borderColor: isDarkMode ? '#2D3748' : '#E2E8F0',
          minHeight: '300px'
        }}
      >
        <CircularProgress size={40} sx={{ color: isDarkMode ? '#90CAF9' : '#3182CE' }} />
        <Typography ml={2} color={isDarkMode ? '#E2E8F0' : '#2D3748'}>
          Loading instances...
        </Typography>
      </Box>
    );
  }

  if (!instances || instances.length === 0) {
    return (
      <Box 
        p={3} 
        textAlign="center"
        sx={{
          backgroundColor: isDarkMode ? '#1A202C' : '#F7FAFC',
          borderRadius: '8px',
          border: '1px solid',
          borderColor: isDarkMode ? '#2D3748' : '#E2E8F0',
          minHeight: '300px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center'
        }}
      >
        <WarningIcon sx={{ fontSize: 48, color: isDarkMode ? '#CBD5E0' : '#A0AEC0', mb: 2 }} />
        <Typography color={isDarkMode ? '#E2E8F0' : '#2D3748'} variant="h6" gutterBottom>
          No AWS instances found
        </Typography>
        <Typography color={isDarkMode ? '#CBD5E0' : '#718096'} variant="body2">
          Either no instances exist or your search did not match any instances.
        </Typography>
      </Box>
    );
  }

  return (
    <Card 
      sx={{ 
        mt: 3,
        backgroundColor: isDarkMode ? '#1A202C' : '#ffffff',
        borderRadius: '8px',
        boxShadow: isDarkMode ? '0 4px 6px rgba(0, 0, 0, 0.4)' : '0 1px 3px rgba(0, 0, 0, 0.1)',
        overflow: 'hidden'
      }}
    >
      <Box 
        p={2}
        sx={{ 
          backgroundColor: isDarkMode ? '#2D3748' : '#F7FAFC',
          borderBottom: '1px solid',
          borderColor: isDarkMode ? '#4A5568' : '#E2E8F0',
        }}
      >
        <Typography 
          variant="h6" 
          fontWeight="medium"
          color={isDarkMode ? '#E2E8F0' : '#2D3748'}
        >
          Instance Overview
        </Typography>
        <Box 
          display="flex" 
          flexWrap="wrap" 
          gap={2}
          mt={1}
        >
          <Box>
            <Typography variant="body2" color={isDarkMode ? '#A0AEC0' : '#718096'}>
              Total Instances
            </Typography>
            <Typography variant="h6" color={isDarkMode ? '#E2E8F0' : '#2D3748'}>
              {stats?.total || 0}
            </Typography>
          </Box>
          <Box>
            <Typography variant="body2" color={isDarkMode ? '#A0AEC0' : '#718096'}>
              Running
            </Typography>
            <Typography variant="h6" color={isDarkMode ? '#48BB78' : '#38A169'}>
              {stats?.running || 0}
            </Typography>
          </Box>
          <Box>
            <Typography variant="body2" color={isDarkMode ? '#A0AEC0' : '#718096'}>
              Stopped
            </Typography>
            <Typography variant="h6" color={isDarkMode ? '#F56565' : '#E53E3E'}>
              {stats?.stopped || 0}
            </Typography>
          </Box>
          <Box>
            <Typography variant="body2" color={isDarkMode ? '#A0AEC0' : '#718096'}>
              Other States
            </Typography>
            <Typography variant="h6" color={isDarkMode ? '#ECC94B' : '#D69E2E'}>
              {stats?.other || 0}
            </Typography>
          </Box>
        </Box>
      </Box>
      <TableContainer component={Paper} sx={{ backgroundColor: isDarkMode ? '#1A202C' : '#ffffff' }}>
        <Table sx={{ minWidth: 650 }}>
          <TableHead>
            <TableRow sx={{ backgroundColor: isDarkMode ? '#2D3748' : '#F7FAFC' }}>
              <TableCell sx={{ fontWeight: 600, color: isDarkMode ? '#E2E8F0' : '#4A5568' }}>Instance ID</TableCell>
              <TableCell sx={{ fontWeight: 600, color: isDarkMode ? '#E2E8F0' : '#4A5568' }}>Name</TableCell>
              <TableCell sx={{ fontWeight: 600, color: isDarkMode ? '#E2E8F0' : '#4A5568' }}>Type</TableCell>
              <TableCell sx={{ fontWeight: 600, color: isDarkMode ? '#E2E8F0' : '#4A5568' }}>State</TableCell>
              <TableCell sx={{ fontWeight: 600, color: isDarkMode ? '#E2E8F0' : '#4A5568' }}>Private IP</TableCell>
              <TableCell sx={{ fontWeight: 600, color: isDarkMode ? '#E2E8F0' : '#4A5568' }}>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {instances.map((instance) => (
              <TableRow 
                key={instance.InstanceId}
                sx={{ 
                  '&:hover': { 
                    backgroundColor: isDarkMode ? '#2D3748' : '#F7FAFC' 
                  },
                  transition: 'background-color 0.2s'
                }}
              >
                <TableCell 
                  component="th" 
                  scope="row"
                  sx={{ 
                    color: isDarkMode ? '#E2E8F0' : '#2D3748',
                    fontFamily: 'monospace',
                    fontSize: '0.875rem'
                  }}
                >
                  {instance.InstanceId}
                </TableCell>
                <TableCell sx={{ color: isDarkMode ? '#E2E8F0' : '#4A5568' }}>
                  {instance.Name || '-'}
                </TableCell>
                <TableCell sx={{ color: isDarkMode ? '#E2E8F0' : '#4A5568' }}>
                  {instance.InstanceType}
                </TableCell>
                <TableCell>
                  {renderInstanceState(instance.State)}
                </TableCell>
                <TableCell 
                  sx={{ 
                    color: isDarkMode ? '#E2E8F0' : '#4A5568',
                    fontFamily: 'monospace',
                    fontSize: '0.875rem'
                  }}
                >
                  {instance.PrivateIpAddress || '-'}
                </TableCell>
                <TableCell>
                  <Box display="flex" gap={1}>
                    <Tooltip title="Start Instance">
                      <span>
                        <IconButton
                          size="small"
                          disabled={instance.State === 'running' || actionLoading === instance.InstanceId}
                          onClick={() => handleInstanceAction(instance.InstanceId, 'start')}
                          sx={{
                            color: isDarkMode ? '#48BB78' : '#38A169',
                            '&.Mui-disabled': {
                              color: isDarkMode ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.26)'
                            }
                          }}
                        >
                          {actionLoading === instance.InstanceId ? (
                            <CircularProgress size={20} sx={{ color: isDarkMode ? '#90CAF9' : '#3182CE' }} />
                          ) : (
                            <PlayArrowIcon />
                          )}
                        </IconButton>
                      </span>
                    </Tooltip>
                    <Tooltip title="Stop Instance">
                      <span>
                        <IconButton
                          size="small"
                          disabled={instance.State === 'stopped' || actionLoading === instance.InstanceId}
                          onClick={() => handleInstanceAction(instance.InstanceId, 'stop')}
                          sx={{
                            color: isDarkMode ? '#F56565' : '#E53E3E',
                            '&.Mui-disabled': {
                              color: isDarkMode ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.26)'
                            }
                          }}
                        >
                          <StopIcon />
                        </IconButton>
                      </span>
                    </Tooltip>
                    <Tooltip title="Reboot Instance">
                      <span>
                        <IconButton
                          size="small"
                          disabled={instance.State !== 'running' || actionLoading === instance.InstanceId}
                          onClick={() => handleInstanceAction(instance.InstanceId, 'reboot')}
                          sx={{
                            color: isDarkMode ? '#4299E1' : '#3182CE',
                            '&.Mui-disabled': {
                              color: isDarkMode ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.26)'
                            }
                          }}
                        >
                          <RestartAltIcon />
                        </IconButton>
                      </span>
                    </Tooltip>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Card>
  );
};

export default InstancesTable; 