'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

import {
  Box,
  TextField,
  Typography,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Checkbox,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  TablePagination,
  FormControlLabel,
  Link,
  Tooltip,
  Button,
  Card,
  CardContent,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import CloseIcon from '@mui/icons-material/Close';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';

import { getAxiosInstance } from 'config/axios';
import { API_ENDPOINTS } from 'config/api';

const ReviewsTab = ({ loading, setLoading, isDarkMode, setNotification }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  // REMOVE: const queryClient = useQueryClient();

  // Helper function to safely parse JSON or return original value
  const safeJsonParse = (jsonString) => {
    if (!jsonString) return null;

    try {
      return JSON.parse(jsonString);
    } catch (error) {
      // If it's not valid JSON, return the original string
      return jsonString;
    }
  };

  // Initialize filters from URL params
  const getInitialFilters = () => ({
    seller_id: searchParams.get('seller_id') || '',
    asin: searchParams.get('asin') || '',
    violation: searchParams.get('violation') || '',
    rating: searchParams.get('rating') || '',
    sort: searchParams.get('sort') || 'desc',
    status: searchParams.get('status') || '',
    checkerStatus: searchParams.get('checkerStatus') || '', // <-- Added
  });

  // Filter states
  const [filters, setFilters] = useState(getInitialFilters());
  const [page, setPage] = useState(0);
  const [limit, setLimit] = useState(20);
  const [sortConfig, setSortConfig] = useState({
    key: 'reviewDate',
    direction: 'desc',
  });
  const [selectedReviews, setSelectedReviews] = useState([]);
  const [selectedModel, setSelectedModel] = useState('gpt-4.1-mini-jeff');
  const [promptModalOpen, setPromptModalOpen] = useState(false);
  const [selectedPromptId, setSelectedPromptId] = useState(null);
  const [lexCheckerModalOpen, setLexCheckerModalOpen] = useState(false);
  const [frequencyModalOpen, setFrequencyModalOpen] = useState(false);
  const [selectedFrequency, setSelectedFrequency] = useState(1);

  // State for reviews
  const [reviewsData, setReviewsData] = useState({
    reviews: [],
    total: 0,
    totalPages: 0,
  });
  const [reviewsLoading, setReviewsLoading] = useState(false);
  const [reviewsError, setReviewsError] = useState(null);

  // State for models
  const [availableModels, setAvailableModels] = useState([]);
  const [modelsLoading, setModelsLoading] = useState(false);

  // State for prompts
  const [availablePrompts, setAvailablePrompts] = useState([]);
  const [promptsLoading, setPromptsLoading] = useState(false);

  // Watch URL params and trigger API call + update filters state
  useEffect(() => {
    // Read filters from URL
    const urlFilters = {
      seller_id: searchParams.get('seller_id') || '',
      asin: searchParams.get('asin') || '',
      violation: searchParams.get('violation') || '',
      rating: searchParams.get('rating') || '',
      sort: searchParams.get('sort') || 'desc',
      status: searchParams.get('status') || '',
      checkerStatus: searchParams.get('checkerStatus') || '', // <-- Added
    };
    setFilters(urlFilters);
    fetchReviews(urlFilters);
    setPage(0); // Optionally reset page on filter change
  }, [
    searchParams.get('seller_id'),
    searchParams.get('asin'),
    searchParams.get('violation'),
    searchParams.get('rating'),
    searchParams.get('sort'),
    searchParams.get('status'),
    searchParams.get('checkerStatus'), // <-- Added
    limit,
    // page, // page is managed separately
  ]);

  // Fetch reviews with given filters
  const fetchReviews = async (currentFilters = filters) => {
    setReviewsLoading(true);
    setReviewsError(null);
    try {
      const params = {};
      if (currentFilters.seller_id) {
        const sellerIds = currentFilters.seller_id
          .split(',')
          .map((id) => id.trim())
          .filter(Boolean);
        params.seller_id = sellerIds;
      }
      if (currentFilters.asin) {
        const asins = currentFilters.asin
          .split(',')
          .map((a) => a.trim())
          .filter(Boolean);
        params.asin = asins;
      }
      if (currentFilters.rating) {
        const ratings = currentFilters.rating
          .split(',')
          .map((r) => r.trim())
          .filter(Boolean);
        params.rating = ratings;
      }
      if (currentFilters.violation !== '')
        params.violation = currentFilters.violation === 'true';
      if (currentFilters.sort) params.sort = currentFilters.sort;
      if (currentFilters.status) params.status = currentFilters.status;
      if (currentFilters.checkerStatus)
        params.checkerStatus = currentFilters.checkerStatus; // <-- Added
      params.page = page + 1;
      params.limit = limit;
      const response = await getAxiosInstance({
        cookiesKey: 'jeff-authorization',
      }).get(API_ENDPOINTS.LEX_REVIEWS, { params });
      setReviewsData({
        reviews: response.data?.reviews || [],
        total: response.data?.pagination?.total || 0,
        totalPages: response.data?.pagination?.totalPages || 0,
      });
    } catch (error) {
      setReviewsError(error);
      setNotification({
        open: true,
        message: 'Failed to fetch reviews',
        severity: 'error',
      });
    } finally {
      setReviewsLoading(false);
    }
  };

  // On page/limit change, fetch reviews with current filters
  useEffect(() => {
    fetchReviews();
  }, [page, limit]);

  useEffect(() => {
    fetchModels();
  }, []);

  // Update loading state for parent component
  useEffect(() => {
    setLoading(reviewsLoading);
  }, [reviewsLoading, setLoading]);

  // Handle errors
  useEffect(() => {
    if (reviewsError) {
      setNotification({
        open: true,
        message: 'Failed to fetch reviews',
        severity: 'error',
      });
    }
  }, [reviewsError, setNotification]);

  // Open prompt modal and fetch prompts
  const handleOpenPromptModal = () => {
    setPromptModalOpen(true);
    fetchPrompts();
  };

  // Open Lex Checker modal
  const handleOpenLexCheckerModal = () => {
    setLexCheckerModalOpen(true);
  };

  // Open Frequency Change modal
  const handleOpenFrequencyModal = () => {
    setFrequencyModalOpen(true);
  };

  // --- Mutations as direct functions ---
  const handleRunIntegratedPrompts = async () => {
    if (!selectedPromptId || selectedReviews.length === 0) {
      setNotification({
        open: true,
        message: 'Please select reviews and a prompt chain',
        severity: 'warning',
      });
      return;
    }
    if (!selectedModel) {
      setNotification({
        open: true,
        message: 'Please select an AI model',
        severity: 'warning',
      });
      return;
    }
    setLoading(true);
    try {
      const payload = {
        reviewIds: selectedReviews,
        promptChainId: selectedPromptId,
        model: selectedModel,
      };
      await getAxiosInstance({ cookiesKey: 'jeff-authorization' }).post(
        API_ENDPOINTS.LEX_PROMPTS_RUN_INTEGRATED,
        payload,
      );
      setNotification({
        open: true,
        message: `Successfully started integrated prompt execution for ${selectedReviews.length} reviews`,
        severity: 'success',
      });
      setPromptModalOpen(false);
      setSelectedPromptId(null);
      setSelectedReviews([]);
      // Refetch reviews after mutation
      fetchReviews();
    } catch (error) {
      setNotification({
        open: true,
        message: error.response?.data?.message || 'Failed to execute prompts',
        severity: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRunLexChecker = async () => {
    if (selectedReviews.length === 0) {
      setNotification({
        open: true,
        message: 'Please select at least one review',
        severity: 'warning',
      });
      return;
    }
    setLoading(true);
    try {
      const payload = {
        rev_ids: selectedReviews.map((reviewId) => {
          const review = reviewsData.reviews.find(
            (r) => r.reviewID === reviewId,
          );
          return review ? review.id : reviewId;
        }),
      };
      await getAxiosInstance({ cookiesKey: 'jeff-authorization' }).post(
        API_ENDPOINTS.LEX_CHECKER_RUN,
        payload,
      );
      setNotification({
        open: true,
        message: `Successfully started Lex Checker job for ${selectedReviews.length} reviews`,
        severity: 'success',
      });
      setLexCheckerModalOpen(false);
      setSelectedReviews([]);
      // Refetch reviews after mutation
      fetchReviews();
    } catch (error) {
      setNotification({
        open: true,
        message: error.response?.data?.error || 'Failed to run Lex Checker job',
        severity: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleChangeFrequency = async () => {
    if (selectedReviews.length === 0) {
      setNotification({
        open: true,
        message: 'Please select at least one review',
        severity: 'warning',
      });
      return;
    }
    setLoading(true);
    try {
      const payload = {
        rev_ids: selectedReviews.map((reviewId) => {
          const review = reviewsData.reviews.find(
            (r) => r.reviewID === reviewId,
          );
          return review ? review.id : reviewId;
        }),
        run_frequency: selectedFrequency,
      };
      const response = await getAxiosInstance({
        cookiesKey: 'jeff-authorization',
      }).patch('/api/lex/review/change_run_frequency', payload);
      setNotification({
        open: true,
        message:
          response.data.message ||
          `Successfully updated run frequency to ${selectedFrequency} for ${selectedReviews.length} reviews`,
        severity: 'success',
      });
      setFrequencyModalOpen(false);
      setSelectedReviews([]);
      // Refetch reviews after mutation
      fetchReviews();
    } catch (error) {
      setNotification({
        open: true,
        message:
          error.response?.data?.error || 'Failed to update run frequency',
        severity: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadAllFilteredReviews = async () => {
    setLoading(true);
    try {
      const params = {};
      const currentFilters = getInitialFilters();
      if (currentFilters.seller_id) {
        const sellerIds = currentFilters.seller_id
          .split(',')
          .map((id) => id.trim())
          .filter(Boolean);
        params.seller_id = sellerIds;
      }
      if (currentFilters.asin) {
        const asins = currentFilters.asin
          .split(',')
          .map((a) => a.trim())
          .filter(Boolean);
        params.asins = asins;
      }
      if (currentFilters.rating) {
        const ratings = currentFilters.rating
          .split(',')
          .map((r) => r.trim())
          .filter(Boolean);
        params.rating = ratings;
      }
      if (currentFilters.violation !== '') {
        params.violation = currentFilters.violation === 'true';
      }
      if (currentFilters.status) {
        params.status = currentFilters.status;
      }
      const response = await getAxiosInstance({
        cookiesKey: 'jeff-authorization',
      }).post(API_ENDPOINTS.LEX_REVIEW_DOWNLOAD_BY_FILTER, params, {
        responseType: 'blob',
      });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(response.data);
      link.setAttribute('download', 'all_filtered_reviews.csv');
      document.body.appendChild(link);
      link.click();
      link.remove();
      setNotification({
        open: true,
        message: 'Downloading all filtered reviews...',
        severity: 'success',
      });
    } catch (error) {
      setNotification({
        open: true,
        message: 'Failed to download filtered reviews.',
        severity: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadSelectedReviews = async () => {
    if (selectedReviews.length === 0) {
      setNotification({
        open: true,
        message: 'Please select at least one review to download.',
        severity: 'warning',
      });
      return;
    }
    setLoading(true);
    try {
      const payload = { reviewIds: selectedReviews };
      const response = await getAxiosInstance({
        cookiesKey: 'jeff-authorization',
      }).post(API_ENDPOINTS.LEX_REVIEW_DOWNLOAD_BY_REVIEW_ID, payload, {
        responseType: 'blob',
      });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(response.data);
      link.setAttribute('download', 'selected_reviews.csv');
      document.body.appendChild(link);
      link.click();
      link.remove();
      setNotification({
        open: true,
        message: 'Downloading selected reviews...',
        severity: 'success',
      });
    } catch (error) {
      setNotification({
        open: true,
        message: 'Failed to download selected reviews.',
        severity: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  // Get selected model details
  const getSelectedModelDetails = () => {
    return (
      availableModels.find((model) => model.id === selectedModel) || {
        name: selectedModel,
      }
    );
  };

  const handleFilterChange = (field) => (event) => {
    const newFilters = { ...filters, [field]: event.target.value };
    setFilters(newFilters);
  };

  const handleRatingChange = (event) => {
    event.preventDefault();
    event.stopPropagation();
    const newFilters = { ...filters, rating: event.target.value.join(',') };
    setFilters(newFilters);
  };

  const handleCheckboxChange = (event) => {
    const newFilters = {
      ...filters,
      violation: event.target.checked ? 'true' : '',
    };
    setFilters(newFilters);
  };

  // Update handleApplyFilters to set URL only
  const handleApplyFilters = () => {
    updateUrlParams(filters);
    // No need to setPage(0) here, handled in useEffect
  };

  // Update handleResetFilters to reset filters and URL
  const handleResetFilters = () => {
    const defaultFilters = {
      seller_id: '',
      asin: '',
      violation: '',
      rating: '',
      sort: 'desc',
      status: '',
      checkerStatus: '', // <-- Added
    };
    setFilters(defaultFilters);
    updateUrlParams(defaultFilters);
    // No need to setPage(0) here, handled in useEffect
  };

  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const handleRowsPerPageChange = (event) => {
    const newLimit = parseInt(event.target.value, 10);
    setLimit(newLimit);
    setPage(0);
  };

  const handleSort = (key) => {
    const newSortConfig = {
      key,
      direction:
        sortConfig.key === key && sortConfig.direction === 'asc'
          ? 'desc'
          : 'asc',
    };
    setSortConfig(newSortConfig);
  };

  const getSortedData = (data) => {
    if (!sortConfig.key) return data;

    return [...data].sort((a, b) => {
      let aValue = a[sortConfig.key];
      let bValue = b[sortConfig.key];

      // Handle nested object sorting
      if (sortConfig.key === 'asinRef.title') {
        aValue = a.asinRef?.title || '';
        bValue = b.asinRef?.title || '';
      }

      // Handle seller sorting
      if (sortConfig.key === 'asinRef.seller.sellerId') {
        aValue = a.asinRef?.seller?.sellerId || '';
        bValue = b.asinRef?.seller?.sellerId || '';
      }

      // Handle date sorting
      if (sortConfig.key === 'reviewDate') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      // Handle numeric sorting
      if (
        sortConfig.key === 'reviewScore' ||
        sortConfig.key === 'helpfulCounts'
      ) {
        aValue = Number(aValue) || 0;
        bValue = Number(bValue) || 0;
      }

      // Handle string sorting
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  };

  const SortableTableCell = ({ label, sortKey, width }) => (
    <TableCell
      onClick={() => handleSort(sortKey)}
      sx={{
        backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
        color: isDarkMode ? '#e2e8f0' : '#4a5568',
        fontWeight: 600,
        borderBottom: `1px solid ${
          isDarkMode ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)'
        }`,
        cursor: 'pointer',
        width: width,
        '&:hover': {
          backgroundColor: isDarkMode ? '#4a5568' : '#edf2f7',
        },
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
        {label}
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            ml: 0.5,
            height: '20px',
          }}
        >
          <ArrowUpwardIcon
            sx={{
              fontSize: '0.875rem',
              color:
                sortConfig.key === sortKey && sortConfig.direction === 'asc'
                  ? isDarkMode
                    ? '#90CAF9'
                    : '#1976d2'
                  : isDarkMode
                    ? 'rgba(255, 255, 255, 0.3)'
                    : 'rgba(0, 0, 0, 0.3)',
              height: '10px',
              lineHeight: '10px',
            }}
          />
          <ArrowDownwardIcon
            sx={{
              fontSize: '0.875rem',
              color:
                sortConfig.key === sortKey && sortConfig.direction === 'desc'
                  ? isDarkMode
                    ? '#90CAF9'
                    : '#1976d2'
                  : isDarkMode
                    ? 'rgba(255, 255, 255, 0.3)'
                    : 'rgba(0, 0, 0, 0.3)',
              height: '10px',
              lineHeight: '10px',
            }}
          />
        </Box>
      </Box>
    </TableCell>
  );

  const sortedReviews = getSortedData(reviewsData.reviews);

  const handleSelectAllReviews = () => {
    // If some items are selected (indeterminate state), deselect all
    if (
      selectedReviews.length > 0 &&
      selectedReviews.length < sortedReviews.length
    ) {
      setSelectedReviews([]);
      return;
    }

    // If all items are selected, deselect all
    if (selectedReviews.length === sortedReviews.length) {
      setSelectedReviews([]);
      return;
    }

    // If no items are selected, select all
    setSelectedReviews(sortedReviews.map((review) => review.reviewID));
  };

  const handleRowSelectReview = (reviewID) => {
    setSelectedReviews((prevSelected) => {
      if (prevSelected.includes(reviewID)) {
        return prevSelected.filter((id) => id !== reviewID);
      } else {
        return [...prevSelected, reviewID];
      }
    });
  };

  // Status utility functions
  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return '#4caf50';
      case 'processing':
        return '#ff9800';
      case 'pending':
        return '#2196f3';
      case 'failed':
        return '#f44336';
      default:
        return '#9e9e9e';
    }
  };

  const getStatusLabel = (status) => {
    return status?.replace(/_/g, ' ') || 'Unknown';
  };

  const updateUrlParams = (newFilters) => {
    const params = new URLSearchParams();

    // Preserve the current tabID if it exists
    const currentTabID = searchParams.get('tabID');
    if (currentTabID) {
      params.set('tabID', currentTabID);
    }

    // Add filters
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value) params.set(key, value);
    });

    // Update URL without refreshing the page
    router.push(`?${params.toString()}`, { scroll: false });
  };

  // Fetch models
  const fetchModels = async () => {
    setModelsLoading(true);
    try {
      const response = await getAxiosInstance({
        cookiesKey: 'jeff-authorization',
      }).get(API_ENDPOINTS.LEX_PROMPTS_MODELS);
      if (response.data?.success && Array.isArray(response.data?.data)) {
        setAvailableModels(response.data.data);
      } else {
        setAvailableModels(response.data?.models || response.data?.data || []);
      }
    } catch (error) {
      setNotification({
        open: true,
        message: 'Failed to fetch available AI models',
        severity: 'warning',
      });
    } finally {
      setModelsLoading(false);
    }
  };

  // Fetch prompts
  const fetchPrompts = async () => {
    setPromptsLoading(true);
    try {
      const response = await getAxiosInstance({
        cookiesKey: 'jeff-authorization',
      }).get(`${API_ENDPOINTS.LEX_PROMPTS}?active_only=true&limit=50`);
      let promptsArr = [];
      if (Array.isArray(response.data)) {
        promptsArr = response.data;
      } else if (Array.isArray(response.data?.data)) {
        promptsArr = response.data.data;
      } else if (Array.isArray(response.data?.results)) {
        promptsArr = response.data.results;
      }
      setAvailablePrompts(promptsArr);
    } catch (error) {
      setNotification({
        open: true,
        message: 'Failed to fetch prompts',
        severity: 'error',
      });
    } finally {
      setPromptsLoading(false);
    }
  };

  return (
    <Box>
      <Paper
        sx={{ mb: 2, p: 2, backgroundColor: isDarkMode ? '#1a2035' : '#fff' }}
      >
        <Typography
          variant="h6"
          sx={{
            fontWeight: 600,
            color: isDarkMode ? '#fff' : 'inherit',
            mb: 2,
          }}
        >
          Reviews Filters
        </Typography>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: 2,
            width: '100%',
          }}
        >
          {/* First row with filters */}
          <Box
            sx={{
              display: 'flex',
              gap: 2,
              alignItems: 'center',
              width: '100%',
            }}
          >
            <Box
              sx={{ display: 'flex', gap: 2, alignItems: 'center', flex: 1 }}
            >
              <FormControlLabel
                control={
                  <Checkbox
                    checked={filters.violation === 'true'}
                    onChange={handleCheckboxChange}
                    color="primary"
                  />
                }
                label="Violation Only"
                sx={{ flex: 1 }}
              />
              <FormControl size="small" sx={{ flex: 1 }}>
                <InputLabel>Rating</InputLabel>
                <Select
                  multiple
                  value={filters.rating ? filters.rating.split(',') : []}
                  label="Rating"
                  onChange={handleRatingChange}
                  renderValue={(selected) =>
                    selected.map((r) => `${r} Star`).join(', ')
                  }
                  endAdornment={
                    filters.rating && (
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          const newFilters = { ...filters, rating: '' };
                          setFilters(newFilters);
                        }}
                        sx={{
                          position: 'absolute',
                          right: 32,
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          '&:hover': {
                            backgroundColor: isDarkMode
                              ? 'rgba(255, 255, 255, 0.08)'
                              : 'rgba(0, 0, 0, 0.04)',
                          },
                        }}
                      >
                        <CloseIcon fontSize="small" />
                      </IconButton>
                    )
                  }
                  sx={{
                    '& .MuiSelect-select': {
                      whiteSpace: 'normal',
                      wordBreak: 'break-word',
                    },
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    '& .MuiSelect-icon': {
                      color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    },
                  }}
                >
                  {[1, 2, 3, 4, 5].map((r) => (
                    <MenuItem key={r} value={r.toString()}>
                      {r} Star
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <FormControl size="small" sx={{ flex: 1 }}>
                <InputLabel>Sort</InputLabel>
                <Select
                  value={filters.sort}
                  label="Sort"
                  onChange={handleFilterChange('sort')}
                >
                  <MenuItem value="desc">Newest</MenuItem>
                  <MenuItem value="asc">Oldest</MenuItem>
                </Select>
              </FormControl>
              <FormControl size="small" sx={{ flex: 1 }}>
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status}
                  label="Status"
                  onChange={handleFilterChange('status')}
                  sx={{ color: isDarkMode ? '#e2e8f0' : '#4a5568' }}
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="PENDING">PENDING</MenuItem>
                  <MenuItem value="IN_PROGRESS">IN_PROGRESS</MenuItem>
                  <MenuItem value="DATA_SCRAPED">DATA_SCRAPED</MenuItem>
                  <MenuItem value="AI_ANALYSIS_PENDING">
                    AI_ANALYSIS_PENDING
                  </MenuItem>
                  <MenuItem value="VIOLATION_DETECTION_PENDING">
                    VIOLATION_DETECTION_PENDING
                  </MenuItem>
                  <MenuItem value="VIOLATION_DETECTION_IN_PROGRESS">
                    VIOLATION_DETECTION_IN_PROGRESS
                  </MenuItem>
                  <MenuItem value="VIOLATION_DETECTED">
                    VIOLATION_DETECTED
                  </MenuItem>
                  <MenuItem value="NO_VIOLATION_DETECTED">
                    NO_VIOLATION_DETECTED
                  </MenuItem>
                  <MenuItem value="COMPLETED">COMPLETED</MenuItem>
                  <MenuItem value="FAILED">FAILED</MenuItem>
                </Select>
              </FormControl>
              <FormControl size="small" sx={{ flex: 1 }}>
                <InputLabel>Removed</InputLabel>
                <Select
                  value={filters.checkerStatus}
                  label="Removed"
                  onChange={handleFilterChange('checkerStatus')}
                  sx={{ color: isDarkMode ? '#e2e8f0' : '#4a5568' }}
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="PENDING">PENDING</MenuItem>
                  <MenuItem value="PRESENT">PRESENT</MenuItem>
                  <MenuItem value="REMOVED">REMOVED</MenuItem>
                  <MenuItem value="FAILED">FAILED</MenuItem>
                  <MenuItem value="RESURRECTED">RESURRECTED</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </Box>

          {/* Second row with ASIN and Seller ID textareas */}
          <Box sx={{ display: 'flex', gap: 2, width: '100%' }}>
            <TextField
              label="ASIN(s) (comma separated)"
              value={filters.asin}
              onChange={handleFilterChange('asin')}
              size="small"
              multiline
              rows={3}
              placeholder="Enter ASINs separated by commas"
              fullWidth
              sx={{
                '& .MuiOutlinedInput-root': {
                  backgroundColor: isDarkMode
                    ? 'rgba(255, 255, 255, 0.05)'
                    : 'inherit',
                  color: isDarkMode ? '#fff' : 'inherit',
                  '& fieldset': {
                    borderColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.23)'
                      : 'inherit',
                  },
                  '&:hover fieldset': {
                    borderColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.4)'
                      : 'inherit',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                },
                '& .MuiInputLabel-root': {
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'inherit',
                  '&.Mui-focused': {
                    color: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                },
                '& .MuiInputBase-input::placeholder': {
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.5)' : 'inherit',
                },
              }}
            />
            <TextField
              label="Seller ID(s) (comma separated)"
              value={filters.seller_id}
              onChange={handleFilterChange('seller_id')}
              size="small"
              multiline
              rows={3}
              placeholder="Enter Seller IDs separated by commas"
              fullWidth
              sx={{
                '& .MuiOutlinedInput-root': {
                  backgroundColor: isDarkMode
                    ? 'rgba(255, 255, 255, 0.05)'
                    : 'inherit',
                  color: isDarkMode ? '#fff' : 'inherit',
                  '& fieldset': {
                    borderColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.23)'
                      : 'inherit',
                  },
                  '&:hover fieldset': {
                    borderColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.4)'
                      : 'inherit',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                },
                '& .MuiInputLabel-root': {
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'inherit',
                  '&.Mui-focused': {
                    color: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                },
                '& .MuiInputBase-input::placeholder': {
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.5)' : 'inherit',
                },
              }}
            />
          </Box>

          {/* New row for filter buttons */}
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
            <Button
              variant="contained"
              onClick={handleApplyFilters}
              sx={{
                bgcolor: isDarkMode ? '#90CAF9' : '#1976d2',
                color: isDarkMode ? '#000' : '#fff',
                '&:hover': {
                  bgcolor: isDarkMode ? '#64B5F6' : '#1565C0',
                },
              }}
            >
              Apply Filters
            </Button>
            <Button
              variant="outlined"
              onClick={handleResetFilters}
              sx={{
                borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                color: isDarkMode ? '#90CAF9' : '#1976d2',
                '&:hover': {
                  borderColor: isDarkMode ? '#64B5F6' : '#1565C0',
                  bgcolor: isDarkMode
                    ? 'rgba(144, 202, 249, 0.08)'
                    : 'rgba(25, 118, 210, 0.04)',
                },
              }}
            >
              Reset
            </Button>
          </Box>
        </Box>
      </Paper>

      {/* Download Buttons and Table Card */}
      <Card
        sx={{
          backgroundColor: isDarkMode ? '#1a2035' : '#fff',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
          mb: 2,
        }}
      >
        <CardContent>
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              color: isDarkMode ? '#fff' : 'inherit',
              mb: 2,
            }}
          >
            Reviews Data
          </Typography>

          {/* Action Buttons */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'flex-start',
              gap: 2,
              mb: 2,
              alignItems: 'center',
              flexWrap: 'wrap',
            }}
          >
            <Button
              variant="contained"
              startIcon={<FileDownloadIcon />}
              onClick={handleDownloadAllFilteredReviews}
              sx={{
                backgroundColor: isDarkMode ? '#90CAF9' : '#1976d2',
                '&:hover': {
                  backgroundColor: isDarkMode ? '#64B5F6' : '#1565C0',
                },
              }}
            >
              Download All Filtered Reviews
            </Button>
            <Button
              variant="contained"
              startIcon={<FileDownloadIcon />}
              onClick={handleDownloadSelectedReviews}
              disabled={selectedReviews.length === 0}
              sx={{
                backgroundColor: isDarkMode ? '#90CAF9' : '#1976d2',
                '&:hover': {
                  backgroundColor: isDarkMode ? '#64B5F6' : '#1565C0',
                },
              }}
            >
              Download Selected Reviews
            </Button>

            {/* AI Model Selection - Enhanced with API data */}
            <FormControl size="small" sx={{ minWidth: 250 }}>
              <InputLabel>Select AI Model</InputLabel>
              <Select
                value={selectedModel}
                label="Select AI Model"
                onChange={(e) => setSelectedModel(e.target.value)}
                disabled={selectedReviews.length === 0 || modelsLoading}
                startAdornment={
                  modelsLoading && <CircularProgress size={16} sx={{ mr: 1 }} />
                }
              >
                {availableModels.length > 0
                  ? availableModels.map((model) => (
                    <MenuItem key={model.id} value={model.id}>
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'flex-start',
                        }}
                      >
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {model.name}
                        </Typography>
                        {model.description && (
                          <Typography
                            variant="caption"
                            sx={{
                              color: 'text.secondary',
                              fontSize: '0.75rem',
                            }}
                          >
                            {model.description}
                          </Typography>
                        )}
                      </Box>
                    </MenuItem>
                  ))
                  : // Fallback to valid models only
                  [
                    {
                      id: 'gpt-4.1-mini-jeff',
                      name: 'GPT-4.1 Mini (Jeff)',
                      description: 'azure-openai',
                    },
                    {
                      id: 'azure-gpt4o',
                      name: 'Azure GPT-4o',
                      description: 'OpenAI GPT-4o via Azure',
                    },
                    {
                      id: 'gemini-2.5-flash',
                      name: 'Gemini 2.5 Flash',
                      description: 'Google Gemini 2.5 Flash',
                    },
                    {
                      id: 'deepseek',
                      name: 'DeepSeek',
                      description: 'DeepSeek Model',
                    },
                    {
                      id: 'azure-openai',
                      name: 'Azure OpenAI',
                      description: 'Azure OpenAI Service',
                    },
                    {
                      id: 'gemini',
                      name: 'Gemini',
                      description: 'Google Gemini',
                    },
                    {
                      id: 'azure-deepseek',
                      name: 'Azure DeepSeek',
                      description: 'DeepSeek via Azure',
                    },
                  ].map((model) => (
                    <MenuItem key={model.id} value={model.id}>
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'flex-start',
                        }}
                      >
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {model.name}
                        </Typography>
                        <Typography
                          variant="caption"
                          sx={{
                            color: 'text.secondary',
                            fontSize: '0.75rem',
                          }}
                        >
                          {model.description}
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
              </Select>
            </FormControl>

            {/* Run All Prompts Button - Enhanced with model info */}
            <Button
              variant="contained"
              color="secondary"
              disabled={selectedReviews.length === 0 || !selectedModel}
              onClick={handleOpenPromptModal}
              startIcon={<PlayArrowIcon />}
              sx={{
                backgroundColor: isDarkMode ? '#7C3AED' : '#6D28D9',
                '&:hover': {
                  backgroundColor: isDarkMode ? '#A78BFA' : '#8B5CF6',
                },
                minWidth: 200,
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                }}
              >
                <Typography
                  variant="body2"
                  sx={{ fontWeight: 600, lineHeight: 1.2 }}
                >
                  Run All Prompts
                </Typography>
                <Typography
                  variant="caption"
                  sx={{ fontSize: '0.7rem', opacity: 0.9 }}
                >
                  {selectedReviews.length} selected •{' '}
                  {getSelectedModelDetails().name}
                </Typography>
              </Box>
            </Button>
            {/* Run Lex Checker Job */}
            <Button
              variant="contained"
              color="secondary"
              disabled={selectedReviews.length === 0}
              onClick={handleOpenLexCheckerModal}
              startIcon={<PlayArrowIcon />}
              sx={{
                backgroundColor: isDarkMode ? '#7C3AED' : '#6D28D9',
                '&:hover': {
                  backgroundColor: isDarkMode ? '#A78BFA' : '#8B5CF6',
                },
                minWidth: 200,
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                }}
              >
                <Typography
                  variant="body2"
                  sx={{ fontWeight: 600, lineHeight: 1.2 }}
                >
                  Run Lex Checker Job
                </Typography>
                <Typography
                  variant="caption"
                  sx={{ fontSize: '0.7rem', opacity: 0.9 }}
                >
                  {selectedReviews.length} selected
                </Typography>
              </Box>
            </Button>
            {/* Change Run Frequency for Checker */}
            <Button
              variant="contained"
              color="secondary"
              disabled={selectedReviews.length === 0}
              onClick={handleOpenFrequencyModal}
              startIcon={<PlayArrowIcon />}
              sx={{
                backgroundColor: isDarkMode ? '#7C3AED' : '#6D28D9',
                '&:hover': {
                  backgroundColor: isDarkMode ? '#A78BFA' : '#8B5CF6',
                },
                minWidth: 200,
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                }}
              >
                <Typography
                  variant="body2"
                  sx={{ fontWeight: 600, lineHeight: 1.2 }}
                >
                  Change Run Frequency for Checker
                </Typography>
                <Typography
                  variant="caption"
                  sx={{ fontSize: '0.7rem', opacity: 0.9 }}
                >
                  {selectedReviews.length} selected
                </Typography>
              </Box>
            </Button>
          </Box>

          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell
                    padding="checkbox"
                    sx={{
                      backgroundColor: isDarkMode ? '#2d3748' : '#f7fafc',
                      borderBottom: `1px solid ${
                        isDarkMode
                          ? 'rgba(255, 255, 255, 0.12)'
                          : 'rgba(0, 0, 0, 0.12)'
                      }`,
                    }}
                  >
                    <Checkbox
                      indeterminate={
                        selectedReviews.length > 0 &&
                        selectedReviews.length < sortedReviews.length
                      }
                      checked={selectedReviews.length === sortedReviews.length}
                      onChange={handleSelectAllReviews}
                      sx={{
                        color: isDarkMode ? '#e2e8f0' : '#4a5568',
                        '&.Mui-checked': {
                          color: isDarkMode ? '#90CAF9' : '#1976d2',
                        },
                        '&.MuiCheckbox-indeterminate': {
                          color: isDarkMode ? '#90CAF9' : '#1976d2',
                        },
                      }}
                    />
                  </TableCell>
                  <SortableTableCell
                    label="Review ID"
                    sortKey="reviewID"
                    width="120px"
                  />
                  <SortableTableCell
                    label="ASIN"
                    sortKey="asin"
                    width="120px"
                  />
                  <SortableTableCell
                    label="Checker Run Frequency"
                    sortKey="runFrequency"
                    width="200px"
                  />
                  <SortableTableCell
                    label="Seller"
                    sortKey="asinRef.seller.sellerId"
                    width="150px"
                  />
                  <SortableTableCell
                    label="Product Title"
                    sortKey="productTitle"
                    width="200px"
                  />
                  <SortableTableCell
                    label="Rating"
                    sortKey="reviewScore"
                    width="100px"
                  />
                  <SortableTableCell
                    label="Title"
                    sortKey="reviewTitle"
                    width="200px"
                  />
                  <TableCell sx={{ color: isDarkMode ? '#fff' : 'inherit' }}>
                    Content
                  </TableCell>
                  <SortableTableCell
                    label="Violation"
                    sortKey="violation"
                    width="100px"
                  />
                  <SortableTableCell
                    label="Review Date"
                    sortKey="reviewDate"
                    width="150px"
                  />
                  <SortableTableCell
                    label="Status"
                    sortKey="status"
                    width="150px"
                  />
                  <SortableTableCell
                    label="Confidence Score"
                    sortKey="violationCode"
                    width="120px"
                  />
                  <SortableTableCell
                    label="Violation Code"
                    sortKey="violationCode"
                    width="120px"
                  />
                </TableRow>
              </TableHead>
              <TableBody>
                {reviewsLoading ? (
                  <TableRow>
                    <TableCell colSpan={12} align="center">
                      <CircularProgress size={24} />
                    </TableCell>
                  </TableRow>
                ) : sortedReviews.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={12} align="center">
                      <Typography
                        variant="body2"
                        sx={{ color: isDarkMode ? '#fff' : 'inherit' }}
                      >
                        No reviews found.
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  sortedReviews.map((review) => (
                    <TableRow key={review.id}>
                      <TableCell
                        padding="checkbox"
                        sx={{
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                          borderBottom: `1px solid ${
                            isDarkMode
                              ? 'rgba(255, 255, 255, 0.12)'
                              : 'rgba(0, 0, 0, 0.12)'
                          }`,
                        }}
                      >
                        <Checkbox
                          checked={selectedReviews.includes(review.reviewID)}
                          onChange={() =>
                            handleRowSelectReview(review.reviewID)
                          }
                          sx={{
                            color: isDarkMode ? '#e2e8f0' : '#4a5568',
                            '&.Mui-checked': {
                              color: isDarkMode ? '#90CAF9' : '#1976d2',
                            },
                          }}
                        />
                      </TableCell>
                      <TableCell
                        sx={{ color: isDarkMode ? '#fff' : 'inherit' }}
                      >
                        <Link
                          href={review.reviewLink}
                          target="_blank"
                          rel="noopener noreferrer"
                          sx={{
                            color: isDarkMode ? '#90CAF9' : '#1976d2',
                            textDecoration: 'none',
                            '&:hover': {
                              textDecoration: 'underline',
                            },
                          }}
                        >
                          {review.reviewID}
                        </Link>
                      </TableCell>
                      <TableCell
                        sx={{ color: isDarkMode ? '#fff' : 'inherit' }}
                      >
                        <Link
                          href={review.productLink}
                          target="_blank"
                          rel="noopener noreferrer"
                          sx={{
                            color: isDarkMode ? '#90CAF9' : '#1976d2',
                            textDecoration: 'none',
                            '&:hover': {
                              textDecoration: 'underline',
                            },
                          }}
                        >
                          {review.asin}
                        </Link>
                      </TableCell>
                      <TableCell
                        sx={{ color: isDarkMode ? '#fff' : 'inherit' }}
                      >
                        <Chip
                          label={
                            review.run_frequency
                              ? `${review.run_frequency} day${
                                review.run_frequency === 1 ? '' : 's'
                              }`
                              : 'Never'
                          }
                          sx={{
                            backgroundColor: review.run_frequency
                              ? review.run_frequency === 1
                                ? '#4caf50'
                                : review.run_frequency === 3
                                  ? '#2196f3'
                                  : review.run_frequency === 7
                                    ? '#ff9800'
                                    : '#9e9e9e'
                              : '#9e9e9e',
                            color: '#fff',
                            fontWeight: 500,
                            '& .MuiChip-label': {
                              px: 1,
                            },
                          }}
                          size="small"
                        />
                      </TableCell>
                      <TableCell
                        sx={{ color: isDarkMode ? '#fff' : 'inherit' }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            gap: 0.5,
                          }}
                        >
                          <Typography
                            variant="body2"
                            sx={{
                              color: isDarkMode ? '#90CAF9' : '#1976d2',
                              fontWeight: 500,
                            }}
                          >
                            {review.asinRef?.seller?.sellerId || '-'}
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{
                              color: isDarkMode ? '#e2e8f0' : '#4a5568',
                            }}
                          >
                            {review.asinRef?.seller?.name || '-'}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell
                        sx={{ color: isDarkMode ? '#fff' : 'inherit' }}
                      >
                        <Tooltip
                          title={review.productTitle}
                          placement="top"
                          arrow
                          componentsProps={{
                            tooltip: {
                              sx: {
                                bgcolor: isDarkMode ? '#2d3748' : '#fff',
                                color: isDarkMode ? '#fff' : '#000',
                                maxWidth: 400,
                                fontSize: '0.875rem',
                                padding: 2,
                                boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                                '& .MuiTooltip-arrow': {
                                  color: isDarkMode ? '#2d3748' : '#fff',
                                },
                              },
                            },
                          }}
                        >
                          <Box
                            sx={{
                              maxWidth: '200px',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                              cursor: 'pointer',
                            }}
                          >
                            {review.productTitle?.length > 25
                              ? `${review.productTitle.substring(0, 25)}...`
                              : review.productTitle}
                          </Box>
                        </Tooltip>
                      </TableCell>
                      <TableCell
                        sx={{ color: isDarkMode ? '#fff' : 'inherit' }}
                      >
                        {review.reviewScore}
                      </TableCell>
                      <TableCell
                        sx={{ color: isDarkMode ? '#fff' : 'inherit' }}
                      >
                        <Tooltip
                          title={review.reviewTitle}
                          placement="top"
                          arrow
                          componentsProps={{
                            tooltip: {
                              sx: {
                                bgcolor: isDarkMode ? '#2d3748' : '#fff',
                                color: isDarkMode ? '#fff' : '#000',
                                maxWidth: 400,
                                fontSize: '0.875rem',
                                padding: 2,
                                boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                                '& .MuiTooltip-arrow': {
                                  color: isDarkMode ? '#2d3748' : '#fff',
                                },
                              },
                            },
                          }}
                        >
                          <Box
                            sx={{
                              maxWidth: '200px',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                              cursor: 'pointer',
                            }}
                          >
                            {review.reviewTitle?.length > 25
                              ? `${review.reviewTitle.substring(0, 25)}...`
                              : review.reviewTitle}
                          </Box>
                        </Tooltip>
                      </TableCell>
                      <TableCell
                        sx={{ color: isDarkMode ? '#fff' : 'inherit' }}
                      >
                        <Tooltip
                          title={review.reviewContent}
                          placement="top"
                          arrow
                          componentsProps={{
                            tooltip: {
                              sx: {
                                bgcolor: isDarkMode ? '#2d3748' : '#fff',
                                color: isDarkMode ? '#fff' : '#000',
                                maxWidth: 400,
                                fontSize: '0.875rem',
                                padding: 2,
                                boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                                '& .MuiTooltip-arrow': {
                                  color: isDarkMode ? '#2d3748' : '#fff',
                                },
                              },
                            },
                          }}
                        >
                          <Box
                            sx={{
                              maxWidth: '200px',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                              cursor: 'pointer',
                            }}
                          >
                            {review.reviewContent?.length > 25
                              ? `${review.reviewContent.substring(0, 25)}...`
                              : review.reviewContent}
                          </Box>
                        </Tooltip>
                      </TableCell>
                      <TableCell
                        sx={{ color: isDarkMode ? '#fff' : 'inherit' }}
                      >
                        {review.violation ? 'Yes' : 'No'}
                      </TableCell>
                      <TableCell
                        sx={{ color: isDarkMode ? '#fff' : 'inherit' }}
                      >
                        {new Date(review.reviewDate).toLocaleDateString()}
                      </TableCell>
                      <TableCell
                        sx={{ color: isDarkMode ? '#fff' : 'inherit' }}
                      >
                        <Chip
                          label={getStatusLabel(review.status)}
                          sx={{
                            backgroundColor: getStatusColor(review.status),
                            color: '#fff',
                            fontWeight: 500,
                            '& .MuiChip-label': {
                              px: 1,
                            },
                          }}
                          size="small"
                        />
                      </TableCell>
                      <TableCell
                        sx={{ color: isDarkMode ? '#fff' : 'inherit' }}
                      >
                        {(() => {
                          if (!review.prompt2Output) return '-';
                          const parsed = safeJsonParse(review.prompt2Output);
                          if (typeof parsed === 'object' && parsed !== null) {
                            return parsed.ConfidenceScore1 || '-';
                          }
                          return parsed; // Return the original string if it's not JSON
                        })()}
                      </TableCell>
                      <TableCell
                        sx={{ color: isDarkMode ? '#fff' : 'inherit' }}
                      >
                        {(() => {
                          if (!review.prompt2Output) return '-';
                          const parsed = safeJsonParse(review.prompt2Output);
                          if (typeof parsed === 'object' && parsed !== null) {
                            return parsed.GuidelineViolation1 || '-';
                          }
                          return parsed; // Return the original string if it's not JSON
                        })()}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
          <TablePagination
            component="div"
            count={reviewsData.total}
            page={page}
            onPageChange={handlePageChange}
            rowsPerPage={limit}
            onRowsPerPageChange={handleRowsPerPageChange}
            rowsPerPageOptions={[10, 20, 50, 100]}
            labelDisplayedRows={({ from, to, count }) =>
              `${from}-${to} of ${count} (Page ${page + 1} of ${
                reviewsData.totalPages
              })`
            }
            sx={{
              color: isDarkMode ? '#fff' : 'inherit',
              '.MuiTablePagination-select': {
                color: isDarkMode ? '#fff' : 'inherit',
              },
              '.MuiTablePagination-selectIcon': {
                color: isDarkMode ? '#fff' : 'inherit',
              },
            }}
          />
        </CardContent>
      </Card>

      {/* Enhanced Prompt Selection Modal */}
      <Dialog
        open={promptModalOpen}
        onClose={() => setPromptModalOpen(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          },
        }}
      >
        <DialogTitle sx={{ color: isDarkMode ? '#fff' : 'inherit' }}>
          Select Prompt Chain for Integrated Execution
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mb: 2 }}>
            <Typography
              variant="body2"
              sx={{ color: isDarkMode ? '#e2e8f0' : '#4a5568', mb: 2 }}
            >
              This will run all prompts (1, 2, and 3) from the selected chain on{' '}
              {selectedReviews.length} review(s) using {selectedModel}.
            </Typography>
          </Box>

          {promptsLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              {/* Client Prompts Section */}
              <Typography
                variant="h6"
                sx={{ mb: 2, color: isDarkMode ? '#90CAF9' : '#1976d2' }}
              >
                Client Prompts
              </Typography>
              {availablePrompts
                .filter((p) => p.isClient)
                .map((prompt) => (
                  <Accordion
                    key={prompt.id}
                    expanded={selectedPromptId === prompt.id}
                    onChange={() =>
                      setSelectedPromptId(
                        selectedPromptId === prompt.id ? null : prompt.id,
                      )
                    }
                    sx={{
                      mb: 1,
                      backgroundColor: isDarkMode ? '#2d3748' : '#f8f9fa',
                      '&:before': { display: 'none' },
                    }}
                  >
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 2,
                          width: '100%',
                        }}
                      >
                        <Box sx={{ flex: 1 }}>
                          <Typography
                            variant="subtitle1"
                            sx={{
                              fontWeight: 600,
                              color: isDarkMode ? '#fff' : 'inherit',
                            }}
                          >
                            {prompt.name}
                          </Typography>
                        </Box>
                        <Chip label="Client" color="primary" size="small" />
                        <Button
                          variant={
                            selectedPromptId === prompt.id
                              ? 'contained'
                              : 'outlined'
                          }
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedPromptId(prompt.id);
                          }}
                          sx={{ ml: 2 }}
                        >
                          {selectedPromptId === prompt.id
                            ? 'Selected'
                            : 'Select'}
                        </Button>
                      </Box>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Typography
                        variant="body2"
                        sx={{
                          mb: 1,
                          fontWeight: 500,
                          color: isDarkMode ? '#fff' : 'inherit',
                        }}
                      >
                        Prompt 1:
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          mb: 2,
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                        }}
                      >
                        {prompt.prompt1}
                      </Typography>
                      {prompt.prompt2 && (
                        <>
                          <Typography
                            variant="body2"
                            sx={{
                              mb: 1,
                              fontWeight: 500,
                              color: isDarkMode ? '#fff' : 'inherit',
                            }}
                          >
                            Prompt 2:
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{
                              mb: 2,
                              color: isDarkMode ? '#e2e8f0' : '#4a5568',
                            }}
                          >
                            {prompt.prompt2}
                          </Typography>
                        </>
                      )}
                      {prompt.prompt3 && (
                        <>
                          <Typography
                            variant="body2"
                            sx={{
                              mb: 1,
                              fontWeight: 500,
                              color: isDarkMode ? '#fff' : 'inherit',
                            }}
                          >
                            Prompt 3:
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{ color: isDarkMode ? '#e2e8f0' : '#4a5568' }}
                          >
                            {prompt.prompt3}
                          </Typography>
                        </>
                      )}
                    </AccordionDetails>
                  </Accordion>
                ))}

              {/* Internal Prompts Section */}
              <Typography
                variant="h6"
                sx={{ mb: 2, mt: 3, color: isDarkMode ? '#e2e8f0' : '#4a5568' }}
              >
                Internal Prompts
              </Typography>
              {availablePrompts
                .filter((p) => !p.isClient)
                .map((prompt) => (
                  <Accordion
                    key={prompt.id}
                    expanded={selectedPromptId === prompt.id}
                    onChange={() =>
                      setSelectedPromptId(
                        selectedPromptId === prompt.id ? null : prompt.id,
                      )
                    }
                    sx={{
                      mb: 1,
                      backgroundColor: isDarkMode ? '#2d3748' : '#f8f9fa',
                      '&:before': { display: 'none' },
                    }}
                  >
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 2,
                          width: '100%',
                        }}
                      >
                        <Box sx={{ flex: 1 }}>
                          <Typography
                            variant="subtitle1"
                            sx={{
                              fontWeight: 600,
                              color: isDarkMode ? '#fff' : 'inherit',
                            }}
                          >
                            {prompt.name}
                          </Typography>
                        </Box>
                        <Chip label="Internal" color="default" size="small" />
                        <Button
                          variant={
                            selectedPromptId === prompt.id
                              ? 'contained'
                              : 'outlined'
                          }
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedPromptId(prompt.id);
                          }}
                          sx={{ ml: 2 }}
                        >
                          {selectedPromptId === prompt.id
                            ? 'Selected'
                            : 'Select'}
                        </Button>
                      </Box>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Typography
                        variant="body2"
                        sx={{
                          mb: 1,
                          fontWeight: 500,
                          color: isDarkMode ? '#fff' : 'inherit',
                        }}
                      >
                        Prompt 1:
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          mb: 2,
                          color: isDarkMode ? '#e2e8f0' : '#4a5568',
                        }}
                      >
                        {prompt.prompt1}
                      </Typography>
                      {prompt.prompt2 && (
                        <>
                          <Typography
                            variant="body2"
                            sx={{
                              mb: 1,
                              fontWeight: 500,
                              color: isDarkMode ? '#fff' : 'inherit',
                            }}
                          >
                            Prompt 2:
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{
                              mb: 2,
                              color: isDarkMode ? '#e2e8f0' : '#4a5568',
                            }}
                          >
                            {prompt.prompt2}
                          </Typography>
                        </>
                      )}
                      {prompt.prompt3 && (
                        <>
                          <Typography
                            variant="body2"
                            sx={{
                              mb: 1,
                              fontWeight: 500,
                              color: isDarkMode ? '#fff' : 'inherit',
                            }}
                          >
                            Prompt 3:
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{ color: isDarkMode ? '#e2e8f0' : '#4a5568' }}
                          >
                            {prompt.prompt3}
                          </Typography>
                        </>
                      )}
                    </AccordionDetails>
                  </Accordion>
                ))}

              {availablePrompts.length === 0 && (
                <Typography
                  variant="body2"
                  sx={{
                    textAlign: 'center',
                    p: 3,
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  }}
                >
                  No active prompts found. Please create prompts in the LEX
                  Prompts tab first.
                </Typography>
              )}
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setPromptModalOpen(false)}
            sx={{ color: isDarkMode ? '#e2e8f0' : '#4a5568' }}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            disabled={!selectedPromptId || loading}
            onClick={handleRunIntegratedPrompts}
            startIcon={
              loading ? <CircularProgress size={16} /> : <PlayArrowIcon />
            }
            sx={{
              backgroundColor: isDarkMode ? '#7C3AED' : '#6D28D9',
              '&:hover': {
                backgroundColor: isDarkMode ? '#A78BFA' : '#8B5CF6',
              },
            }}
          >
            {loading ? 'Running...' : 'Run All Prompts'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Lex Checker Job Modal */}
      <Dialog
        open={lexCheckerModalOpen}
        onClose={() => setLexCheckerModalOpen(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          },
        }}
      >
        <DialogTitle sx={{ color: isDarkMode ? '#fff' : 'inherit' }}>
          Run Lex Checker Job
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mb: 2 }}>
            <Typography
              variant="body2"
              sx={{ color: isDarkMode ? '#e2e8f0' : '#4a5568', mb: 2 }}
            >
              This will run the Lex Checker job on {selectedReviews.length}{' '}
              selected review(s).
            </Typography>
            <Typography
              variant="body2"
              sx={{ color: isDarkMode ? '#e2e8f0' : '#4a5568', mb: 2 }}
            >
              The Lex Checker will analyze reviews for policy violations and
              compliance issues.
            </Typography>
          </Box>

          <Box sx={{ mb: 2 }}>
            <Typography
              variant="subtitle2"
              sx={{ color: isDarkMode ? '#fff' : 'inherit', mb: 1 }}
            >
              Reviews to Process: {selectedReviews.length}
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setLexCheckerModalOpen(false)}
            sx={{ color: isDarkMode ? '#e2e8f0' : '#4a5568' }}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            disabled={loading}
            onClick={handleRunLexChecker}
            startIcon={
              loading ? <CircularProgress size={16} /> : <PlayArrowIcon />
            }
            sx={{
              backgroundColor: isDarkMode ? '#7C3AED' : '#6D28D9',
              '&:hover': {
                backgroundColor: isDarkMode ? '#A78BFA' : '#8B5CF6',
              },
            }}
          >
            {loading ? 'Running...' : 'Run Lex Checker'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Change Run Frequency Modal */}
      <Dialog
        open={frequencyModalOpen}
        onClose={() => setFrequencyModalOpen(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          },
        }}
      >
        <DialogTitle sx={{ color: isDarkMode ? '#fff' : 'inherit' }}>
          Change Run Frequency for Checker
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mb: 2 }}>
            <Typography
              variant="body2"
              sx={{ color: isDarkMode ? '#e2e8f0' : '#4a5568', mb: 2 }}
            >
              Enter how often (in days) the Lex Checker should run for{' '}
              {selectedReviews.length} selected review(s).
            </Typography>
          </Box>

          <TextField
            fullWidth
            label="Run Frequency (days)"
            type="number"
            value={selectedFrequency}
            onChange={(e) => {
              const value = parseInt(e.target.value);
              if (value >= 0 && value <= 15) {
                setSelectedFrequency(value);
              }
            }}
            inputProps={{
              min: 0,
              max: 15,
              step: 1,
            }}
            helperText="Enter a number between 0 and 15 days"
            sx={{
              mb: 2,
              '& .MuiOutlinedInput-root': {
                backgroundColor: isDarkMode
                  ? 'rgba(255, 255, 255, 0.05)'
                  : 'inherit',
                color: isDarkMode ? '#fff' : 'inherit',
                '& fieldset': {
                  borderColor: isDarkMode
                    ? 'rgba(255, 255, 255, 0.23)'
                    : 'inherit',
                },
                '&:hover fieldset': {
                  borderColor: isDarkMode
                    ? 'rgba(255, 255, 255, 0.4)'
                    : 'inherit',
                },
                '&.Mui-focused fieldset': {
                  borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                },
              },
              '& .MuiInputLabel-root': {
                color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'inherit',
                '&.Mui-focused': {
                  color: isDarkMode ? '#90CAF9' : '#1976d2',
                },
              },
              '& .MuiFormHelperText-root': {
                color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'inherit',
              },
            }}
          />

          <Box sx={{ mb: 2 }}>
            <Typography
              variant="subtitle2"
              sx={{ color: isDarkMode ? '#fff' : 'inherit', mb: 1 }}
            >
              Reviews to Update: {selectedReviews.length}
            </Typography>
            <Typography
              variant="subtitle2"
              sx={{ color: isDarkMode ? '#fff' : 'inherit', mb: 1 }}
            >
              New Frequency: {selectedFrequency} day
              {selectedFrequency === 1 ? '' : 's'}
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setFrequencyModalOpen(false)}
            sx={{ color: isDarkMode ? '#e2e8f0' : '#4a5568' }}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            disabled={loading}
            onClick={handleChangeFrequency}
            startIcon={
              loading ? <CircularProgress size={16} /> : <PlayArrowIcon />
            }
            sx={{
              backgroundColor: isDarkMode ? '#7C3AED' : '#6D28D9',
              '&:hover': {
                backgroundColor: isDarkMode ? '#A78BFA' : '#8B5CF6',
              },
            }}
          >
            {loading ? 'Updating...' : 'Update Frequency'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ReviewsTab;
