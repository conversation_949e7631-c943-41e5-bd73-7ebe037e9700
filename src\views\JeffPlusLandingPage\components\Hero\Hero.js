import React from 'react';
import { alpha, useTheme } from '@mui/material/styles';
import { colors } from '@mui/material';
import useMediaQuery from '@mui/material/useMediaQuery';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Grid from '@mui/material/Grid';
import Spacer from '../../../../components/Equal/Spacer';
import { usePostHog } from 'posthog-js/react';
import { useSearchParams } from 'next/navigation';

const JEFF_BUBBLES = [
  {
    message: 'I can do advanced amazon audit of sellers and make suggestions.',
    backgroundColor: alpha(colors.purple[200], 1),
    top: 24,
    bottom: null,
    right: null,
    borderRadius: '24px 24px 4px 24px',
  },
  {
    message: 'I am powered by a database of 200k+ amazon sellers.',
    backgroundColor: alpha(colors.purple[200], 0.7),
    bottom: 10,
    right: null,
    borderRadius: '24px 4px 24px 24px',
  },
  {
    message: 'I can find competitors of sellers you are prospecting.',
    backgroundColor: alpha(colors.purple[200], 0.7),
    bottom: null,
    right: 4,
    borderRadius: '24px 24px 24px 4px',
  },
  {
    message:
      'I can craft personalised emails using audits & 20+ other signals.',
    backgroundColor: alpha(colors.purple[200], 1),
    bottom: 40,
    right: 10,
    borderRadius: '4px 24px 24px 24px',
  },
];

const Hero = ({ schedulingLink }) => {
  const theme = useTheme();
  const isMd = useMediaQuery(theme.breakpoints.up('md'), {
    defaultMatches: true,
  });

  const posthog = usePostHog();

  const searchParams = useSearchParams();
  const prospectIdentifier = searchParams.get('c') || 'unknown';

  return (
    <>
      <Grid container spacing={1} xs={12} alignItems="center">
        <Grid
          item
          md={12}
          maxWidth={500}
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          <Box
            sx={{
              maxWidth: 800,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
            }}
          >
            <Typography
              variant="h3"
              color="text.primary"
              align="center"
              sx={{
                fontWeight: 700,
              }}
            >
              {'Meet Jeff: The '}
              <Typography
                color={'primary'}
                component={'span'}
                variant={'inherit'}
                sx={{
                  fontWeight: 700,
                  display: 'inline',
                  background: `linear-gradient(180deg, transparent 82%, ${alpha(
                    colors.purple[200],
                    0.3,
                  )} 0%)`,
                }}
              >
                {'AI sales rep'}
              </Typography>
              {' that sells to '}
              <Typography
                color={'primary'}
                component={'span'}
                variant={'inherit'}
                sx={{
                  fontWeight: 700,
                  display: 'inline',
                  background: `linear-gradient(180deg, transparent 82%, ${alpha(
                    colors.purple[200],
                    0.3,
                  )} 0%)`,
                }}
              >
                {'Amazon Sellers'}
              </Typography>
            </Typography>
            <Spacer y={2} />
            <Typography
              variant="h6"
              component="p"
              color="text.primary"
              sx={{ fontWeight: 500, maxWidth: '90%', textAlign: 'center' }}
            >
              After training with 14 agencies and booking 500+ meetings worth $1M+ LTV…Jeff has mastered reaching out to sellers with the exact problems you solve.
            </Typography>
            <Spacer y={2} />
            <Box
              sx={{
                display: 'flex',
                flexDirection: { md: 'row', xs: 'column' },
              }}
            >
              <Box sx={{ display: isMd ? 'inline-block' : 'block' }}>
                <Button
                  component={'a'}
                  variant="contained"
                  color="primary"
                  size="large"
                  fullWidth={true}
                  onClick={() => {
                    posthog?.capture('book_call_hero_clicked', {
                      prospectIdentifier: prospectIdentifier,
                    });
                  }}
                  href={schedulingLink}
                  target={'_blank'}
                >
                  Book a Call
                </Button>
                <Spacer y={0.2} />
              </Box>
            </Box>
          </Box>
        </Grid>
        <Grid
          item
          md={12}
          xs={12}
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          <Spacer y={2} />
          <Box sx={{ position: 'relative', width: { md: 650, xs: '100%' } }}>
            {isMd && (
              <>
                {JEFF_BUBBLES.map((msg, key) => {
                  return (
                    <Box
                      key={key+msg.message+msg.borderRadius}
                      sx={{
                        position: 'absolute',
                        top: msg.top,
                        bottom: msg.bottom,
                        right: msg.right,
                        backgroundColor: msg.backgroundColor,
                        width: 250,
                        padding: theme.spacing(1),
                        borderRadius: msg.borderRadius,
                      }}
                    >
                      <Typography variant="body1">{msg.message}</Typography>
                    </Box>
                  );
                })}
                <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                  <img src="/images/jeff/persona.png" width={300} />
                </Box>
              </>
            )}
            {!isMd && (
              <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                  <img src="/images/jeff/persona.png" width={300} />
                </Box>
                <Box sx={{ marginTop: '-15%' }}>
                  {JEFF_BUBBLES.map((msg, key) => {
                    return (
                      <>
                        <Box
                          key={msg.message + key}
                          sx={{
                            backgroundColor: msg.backgroundColor,
                            padding: theme.spacing(1),
                            borderRadius: msg.borderRadius,
                          }}
                        >
                          <Typography variant="body1">{msg.message}</Typography>
                        </Box>
                        <Spacer y={1} />
                      </>
                    );
                  })}
                </Box>
              </Box>
            )}
          </Box>
        </Grid>
      </Grid>
    </>
  );
};

export default Hero;
