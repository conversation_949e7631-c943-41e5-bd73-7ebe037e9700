import React from 'react';
import Brandbuddy from 'views/Brandbuddy';
import { Providers } from './providers';
import { getUser } from './getUser';
import { getJobs } from './getJobs';
import {
  dehydrate,
  HydrationBoundary,
  QueryClient,
} from '@tanstack/react-query';

const BrandbuddyPage = async () => {
  const data = await getUser();

  const queryClient = new QueryClient();
  await queryClient.prefetchQuery({
    queryKey: ['jobs'],
    queryFn: getJobs,
  });

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <Providers user={data}>
        <Brandbuddy />
      </Providers>
    </HydrationBoundary>
  );
};

export default BrandbuddyPage;
